<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title><PERSON><PERSON><PERSON></title>
    <style>
        body {
            font-family: Arial, sans-serif;
            font-size: 12px;
            margin: 20px;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 2px solid #333;
            padding-bottom: 15px;
        }
        .header h1 {
            margin: 0;
            font-size: 18px;
            color: #333;
        }
        .header h2 {
            margin: 5px 0;
            font-size: 14px;
            color: #666;
        }
        .info {
            margin-bottom: 20px;
            font-size: 11px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
            vertical-align: top;
        }
        th {
            background-color: #f5f5f5;
            font-weight: bold;
            font-size: 11px;
        }
        td {
            font-size: 10px;
        }
        .status-pending { color: #f59e0b; }
        .status-in_progress { color: #3b82f6; }
        .status-resolved { color: #10b981; }
        .status-closed { color: #6b7280; }
        .priority-low { color: #10b981; }
        .priority-medium { color: #f59e0b; }
        .priority-high { color: #f97316; }
        .priority-urgent { color: #ef4444; }
        .footer {
            margin-top: 30px;
            text-align: right;
            font-size: 11px;
            color: #666;
        }
        .description {
            max-width: 200px;
            word-wrap: break-word;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>LAPORAN PENGADUAN MASYARAKAT</h1>
        <h2>Desa Lemah Duhur, Kecamatan Caringin, Kabupaten Bogor</h2>
    </div>

    <div class="info">
        <strong>Tanggal Cetak:</strong> {{ $generated_at }}<br>
        <strong>Total Pengaduan:</strong> {{ $complaints->count() }} pengaduan
        @if(isset($filters['date_from']) || isset($filters['date_to']))
            <br><strong>Periode:</strong> 
            @if(isset($filters['date_from']))
                {{ \Carbon\Carbon::parse($filters['date_from'])->format('d/m/Y') }}
            @endif
            @if(isset($filters['date_from']) && isset($filters['date_to']))
                s/d 
            @endif
            @if(isset($filters['date_to']))
                {{ \Carbon\Carbon::parse($filters['date_to'])->format('d/m/Y') }}
            @endif
        @endif
    </div>

    <table>
        <thead>
            <tr>
                <th style="width: 8%">No. Tiket</th>
                <th style="width: 12%">Nama</th>
                <th style="width: 10%">Kategori</th>
                <th style="width: 15%">Subjek</th>
                <th style="width: 25%">Deskripsi</th>
                <th style="width: 8%">Status</th>
                <th style="width: 8%">Prioritas</th>
                <th style="width: 10%">Tanggal</th>
                <th style="width: 4%">Respon</th>
            </tr>
        </thead>
        <tbody>
            @forelse($complaints as $complaint)
                <tr>
                    <td>{{ $complaint->ticket_number }}</td>
                    <td>{{ $complaint->name }}</td>
                    <td>{{ $complaint->category }}</td>
                    <td>{{ $complaint->subject }}</td>
                    <td class="description">{{ Str::limit(strip_tags($complaint->description), 100) }}</td>
                    <td class="status-{{ $complaint->status }}">{{ $complaint->status }}</td>
                    <td class="priority-{{ $complaint->priority }}">{{ $complaint->priority }}</td>
                    <td>{{ $complaint->created_at->format('d/m/Y') }}</td>
                    <td>{{ $complaint->admin_response ? 'Ya' : 'Belum' }}</td>
                </tr>
            @empty
                <tr>
                    <td colspan="9" style="text-align: center; color: #666;">Tidak ada data pengaduan</td>
                </tr>
            @endforelse
        </tbody>
    </table>

    <div class="footer">
        <p>Dicetak oleh sistem pada {{ $generated_at }}</p>
    </div>
</body>
</html>