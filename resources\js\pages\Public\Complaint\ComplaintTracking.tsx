import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { useSettings } from '@/hooks/useSettings';
import PublicLayout from '@/layouts/PublicLayout';
import { Head, useForm } from '@inertiajs/react';
import { HelpCircle, Mail, Phone, Search } from 'lucide-react';
import React from 'react';

interface ComplaintTrackingProps {
    pageTitle: string;
    pageDescription: string;
}

export default function ComplaintTracking({ pageTitle, pageDescription }: ComplaintTrackingProps) {
    const { data, setData, post, processing, errors } = useForm({
        ticket_number: '',
    });

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        post(route('complaints.track'));
    };

    return (
        <PublicLayout>
            <Head title={pageTitle} />

            <div className="min-h-screen bg-gray-50 py-8 dark:bg-neutral-900">
                <div className="container mx-auto px-4">
                    {/* Header */}
                    <div className="mb-8 text-center">
                        <h1 className="mb-4 text-3xl font-bold text-gray-900 dark:text-gray-100">Lacak Pengaduan</h1>
                        <p className="mx-auto max-w-2xl text-lg text-gray-600 dark:text-gray-300">{pageDescription}</p>
                    </div>

                    <div className="mx-auto max-w-2xl">
                        {/* Tracking Form */}
                        <Card className="mb-8">
                            <CardHeader>
                                <CardTitle className="flex items-center gap-2">
                                    <Search className="h-5 w-5" />
                                    Masukkan Nomor Tiket
                                </CardTitle>
                                <CardDescription>Masukkan nomor tiket pengaduan Anda untuk melihat status terkini</CardDescription>
                            </CardHeader>
                            <CardContent>
                                <form onSubmit={handleSubmit} className="space-y-4">
                                    <div>
                                        <Label htmlFor="ticket_number">
                                            Nomor Tiket <span className="text-red-500">*</span>
                                        </Label>
                                        <Input
                                            id="ticket_number"
                                            type="text"
                                            value={data.ticket_number}
                                            onChange={(e) => setData('ticket_number', e.target.value.toUpperCase())}
                                            placeholder="LDH-20240724-001"
                                            className={errors.ticket_number ? 'border-red-500' : ''}
                                        />
                                        {errors.ticket_number && <p className="mt-1 text-sm text-red-500">{errors.ticket_number}</p>}
                                        <p className="mt-1 text-sm text-gray-500">Format: LDH-YYYYMMDD-XXX (contoh: LDH-20240724-001)</p>
                                    </div>

                                    <Button type="submit" disabled={processing} className="w-full">
                                        <Search className="mr-2 h-4 w-4" />
                                        {processing ? 'Mencari...' : 'Lacak Pengaduan'}
                                    </Button>
                                </form>
                            </CardContent>
                        </Card>

                        {/* Help Information */}
                        <Card className="mb-8">
                            <CardHeader>
                                <CardTitle className="flex items-center gap-2">
                                    <HelpCircle className="h-5 w-5" />
                                    Bantuan
                                </CardTitle>
                            </CardHeader>
                            <CardContent className="space-y-4">
                                <div>
                                    <h4 className="mb-2 text-sm font-semibold text-gray-900">Tidak Menemukan Nomor Tiket?</h4>
                                    <p className="text-sm text-gray-600">
                                        Nomor tiket diberikan setelah Anda berhasil mengirim pengaduan. Periksa kembali email konfirmasi atau
                                        screenshot halaman konfirmasi.
                                    </p>
                                </div>

                                <div>
                                    <h4 className="mb-2 text-sm font-semibold text-gray-900">Status Pengaduan</h4>
                                    <div className="space-y-2 text-sm">
                                        <div className="flex items-center gap-2">
                                            <span className="inline-flex items-center rounded-full bg-yellow-100 px-2 py-1 text-xs font-medium text-yellow-800">
                                                Menunggu
                                            </span>
                                            <span className="text-gray-600">Pengaduan baru diterima</span>
                                        </div>
                                        <div className="flex items-center gap-2">
                                            <span className="inline-flex items-center rounded-full bg-blue-100 px-2 py-1 text-xs font-medium text-blue-800">
                                                Diproses
                                            </span>
                                            <span className="text-gray-600">Sedang ditindaklanjuti</span>
                                        </div>
                                        <div className="flex items-center gap-2">
                                            <span className="inline-flex items-center rounded-full bg-green-100 px-2 py-1 text-xs font-medium text-green-800">
                                                Selesai
                                            </span>
                                            <span className="text-gray-600">Pengaduan telah diselesaikan</span>
                                        </div>
                                        <div className="flex items-center gap-2">
                                            <span className="inline-flex items-center rounded-full bg-gray-100 px-2 py-1 text-xs font-medium text-gray-800">
                                                Ditutup
                                            </span>
                                            <span className="text-gray-600">Kasus ditutup</span>
                                        </div>
                                    </div>
                                </div>

                                <div>
                                    <h4 className="mb-2 text-sm font-semibold text-gray-900">Waktu Respon</h4>
                                    <p className="text-sm text-gray-600">
                                        Kami berkomitmen memberikan respon dalam waktu 3-7 hari kerja. Untuk kasus mendesak, silakan hubungi kontak di
                                        bawah ini.
                                    </p>
                                </div>
                            </CardContent>
                        </Card>

                        {/* Contact Information */}
                        <Card>
                            <CardHeader>
                                <CardTitle>Kontak Bantuan</CardTitle>
                                <CardDescription>Hubungi kami jika memerlukan bantuan lebih lanjut</CardDescription>
                            </CardHeader>
                            <CardContent>
                                <div className="space-y-3">
                                    <div className="flex items-center gap-3">
                                        <Phone className="h-5 w-5 text-gray-400" />
                                        <div>
                                            <p className="font-medium text-gray-900">{useSettings().getContactInfo()?.phone || 'Belum tersedia'}</p>
                                            <p className="text-sm text-gray-600">
                                                {useSettings().getOperatingHours()?.weekdays || 'Senin - Jumat, 08:00 - 15:00 WIB'}
                                            </p>
                                        </div>
                                    </div>

                                    <div className="flex items-center gap-3">
                                        <Mail className="h-5 w-5 text-gray-400" />
                                        <div>
                                            <p className="font-medium text-gray-900">{useSettings().getContactInfo()?.email || 'Belum tersedia'}</p>
                                            <p className="text-sm text-gray-600">Respon dalam 1x24 jam</p>
                                        </div>
                                    </div>
                                </div>
                            </CardContent>
                        </Card>
                    </div>
                </div>
            </div>
        </PublicLayout>
    );
}
