/**
 * Application Initialization Utilities
 * 
 * This module handles the initialization of various app features
 * including browser extension defense and other startup tasks.
 */

import { initializeBrowserExtensionDefense } from './browserExtensionDefense';

/**
 * Initialize all application features
 */
export function initializeApp(): void {
    // Initialize browser extension defense early
    initializeBrowserExtensionDefense();
    
    // Add any other initialization tasks here
    console.debug('Application initialized with browser extension defense');
}
