<?php

use Lara<PERSON>\Pennant\Feature;

if (! function_exists('feature')) {
    /**
     * Check if a feature flag is active
     *
     * @param  string  $feature  The feature flag name
     * @return bool True if the feature is active
     */
    function feature(string $feature): bool
    {
        return Feature::active($feature);
    }
}

if (! function_exists('feature_inactive')) {
    /**
     * Check if a feature flag is inactive
     *
     * @param  string  $feature  The feature flag name
     * @return bool True if the feature is inactive
     */
    function feature_inactive(string $feature): bool
    {
        return Feature::inactive($feature);
    }
}

if (! function_exists('when_feature')) {
    /**
     * Execute callback when feature is active
     *
     * @param  string  $feature  The feature flag name
     * @param  callable  $callback  The callback to execute
     * @param  callable|null  $default  Optional default callback when feature is inactive
     * @return mixed The result of the callback or null
     */
    function when_feature(string $feature, callable $callback, ?callable $default = null): mixed
    {
        if (Feature::active($feature)) {
            return $callback();
        }

        return $default ? $default() : null;
    }
}

if (! function_exists('unless_feature')) {
    /**
     * Execute callback when feature is inactive
     *
     * @param  string  $feature  The feature flag name
     * @param  callable  $callback  The callback to execute
     * @param  callable|null  $default  Optional default callback when feature is active
     * @return mixed The result of the callback or null
     */
    function unless_feature(string $feature, callable $callback, ?callable $default = null): mixed
    {
        if (Feature::inactive($feature)) {
            return $callback();
        }

        return $default ? $default() : null;
    }
}

if (! function_exists('feature_value')) {
    /**
     * Get the value of a feature flag
     *
     * @param  string  $feature  The feature flag name
     * @param  mixed  $default  Default value if feature is not found
     * @return mixed The feature flag value
     */
    function feature_value(string $feature, mixed $default = false): mixed
    {
        return Feature::value($feature, $default);
    }
}
