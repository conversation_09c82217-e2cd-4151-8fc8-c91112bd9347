<?php

namespace Database\Factories;

use App\Models\Profile;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Profile>
 */
class ProfileFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = Profile::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $sections = ['history', 'vision_mission', 'organization', 'demographics', 'geography'];

        return [
            'section' => $this->faker->randomElement($sections),
            'title' => $this->faker->sentence(3),
            'content' => $this->faker->paragraphs(3, true),
            'image' => null,
            'order' => $this->faker->numberBetween(1, 10),
        ];
    }

    /**
     * Indicate that the profile has an image.
     */
    public function withImage(): static
    {
        return $this->state(fn (array $attributes) => [
            'image' => 'profiles/'.$this->faker->uuid().'.jpg',
        ]);
    }

    /**
     * Create a profile for a specific section.
     */
    public function forSection(string $section): static
    {
        return $this->state(fn (array $attributes) => [
            'section' => $section,
        ]);
    }

    /**
     * Create a history section profile.
     */
    public function history(): static
    {
        return $this->state(fn (array $attributes) => [
            'section' => 'history',
            'title' => 'Sejarah '.$this->faker->words(2, true),
            'content' => $this->faker->paragraphs(4, true),
        ]);
    }

    /**
     * Create a vision mission profile.
     */
    public function visionMission(): static
    {
        return $this->state(fn (array $attributes) => [
            'section' => 'vision_mission',
            'title' => $this->faker->randomElement(['Visi Desa', 'Misi Desa']),
            'content' => $this->faker->paragraphs(2, true),
        ]);
    }

    /**
     * Create an organization profile.
     */
    public function organization(): static
    {
        return $this->state(fn (array $attributes) => [
            'section' => 'organization',
            'title' => $this->faker->randomElement(['Kepala Desa', 'Sekretaris Desa', 'Bendahara Desa']),
            'content' => $this->faker->paragraph(),
        ]);
    }

    /**
     * Create a demographics profile.
     */
    public function demographics(): static
    {
        return $this->state(fn (array $attributes) => [
            'section' => 'demographics',
            'title' => 'Data '.$this->faker->randomElement(['Penduduk', 'Keluarga', 'Pendidikan']),
            'content' => $this->faker->paragraphs(2, true),
        ]);
    }

    /**
     * Create a geography profile.
     */
    public function geography(): static
    {
        return $this->state(fn (array $attributes) => [
            'section' => 'geography',
            'title' => $this->faker->randomElement(['Lokasi Desa', 'Batas Wilayah', 'Topografi']),
            'content' => $this->faker->paragraphs(2, true),
        ]);
    }
}
