<?php

namespace App\Providers;

use App\Services\FeatureFlagService;
use App\Services\MockFeatureFlagService;
use Illuminate\Support\ServiceProvider;

class TestServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        // Bind mock services during testing
        if (app()->environment('testing')) {
            $this->app->bind(FeatureFlagService::class, MockFeatureFlagService::class);
        }
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        //
    }
}
