<?php

return [
    /*
    |--------------------------------------------------------------------------
    | Performance Configuration
    |--------------------------------------------------------------------------
    |
    | This file contains performance-related configuration options for the
    | Website Profil Desa Lemah Duhur application.
    |
    */

    /*
    |--------------------------------------------------------------------------
    | Page Caching
    |--------------------------------------------------------------------------
    |
    | Enable page caching to improve response times for public pages.
    | Cache TTL is in seconds.
    |
    */
    'page_cache_enabled' => env('PAGE_CACHE_ENABLED', false),
    'page_cache_ttl' => env('PAGE_CACHE_TTL', 0), // Disabled

    /*
    |--------------------------------------------------------------------------
    | Asset Optimization
    |--------------------------------------------------------------------------
    |
    | Configuration for asset compression, minification, and versioning.
    |
    */
    'asset_compression_enabled' => env('ASSET_COMPRESSION_ENABLED', true),
    'asset_compression_level' => env('ASSET_COMPRESSION_LEVEL', 9),
    'asset_minification_enabled' => env('ASSET_MINIFICATION_ENABLED', true),
    'asset_versioning_enabled' => env('ASSET_VERSIONING_ENABLED', true),

    /*
    |--------------------------------------------------------------------------
    | Database Query Optimization
    |--------------------------------------------------------------------------
    |
    | Settings for database query caching and optimization.
    |
    */
    'db_query_cache_enabled' => env('DB_QUERY_CACHE_ENABLED', false),
    'db_query_cache_ttl' => env('DB_QUERY_CACHE_TTL', 0), // Disabled
    'db_analyze_tables' => env('DB_ANALYZE_TABLES', true),
    'db_cleanup_cache' => env('DB_CLEANUP_CACHE', true),

    /*
    |--------------------------------------------------------------------------
    | Image Optimization
    |--------------------------------------------------------------------------
    |
    | Configuration for automatic image optimization and WebP conversion.
    |
    */
    'image_optimization_enabled' => env('IMAGE_OPTIMIZATION_ENABLED', true),
    'image_quality' => env('IMAGE_QUALITY', 85),
    'webp_enabled' => env('WEBP_ENABLED', true),
    'webp_quality' => env('WEBP_QUALITY', 80),
    'lazy_loading_enabled' => env('LAZY_LOADING_ENABLED', true),

    /*
    |--------------------------------------------------------------------------
    | Performance Monitoring
    |--------------------------------------------------------------------------
    |
    | Settings for monitoring application performance and logging slow queries.
    |
    */
    'performance_monitoring_enabled' => env('PERFORMANCE_MONITORING_ENABLED', true),
    'performance_logging_enabled' => env('PERFORMANCE_LOGGING_ENABLED', false),
    'slow_query_threshold' => env('SLOW_QUERY_THRESHOLD', 1000), // milliseconds
    'memory_limit_threshold' => env('MEMORY_LIMIT_THRESHOLD', 128), // MB
    'response_time_threshold' => env('RESPONSE_TIME_THRESHOLD', 2000), // milliseconds

    /*
    |--------------------------------------------------------------------------
    | CDN Configuration
    |--------------------------------------------------------------------------
    |
    | Configuration for Content Delivery Network integration.
    |
    */
    'cdn_enabled' => env('CDN_ENABLED', false),
    'cdn_url' => env('CDN_URL', ''),

    /*
    |--------------------------------------------------------------------------
    | Cache Warming
    |--------------------------------------------------------------------------
    |
    | Configuration for cache warming strategies.
    |
    */
    'cache_warm_up_enabled' => false,
    'cache_warm_up_schedule' => '0 */6 * * *', // Every 6 hours

    /*
    |--------------------------------------------------------------------------
    | Compression Settings
    |--------------------------------------------------------------------------
    |
    | Settings for response compression.
    |
    */
    'gzip_enabled' => true,
    'gzip_level' => 6,
    'brotli_enabled' => false,

    /*
    |--------------------------------------------------------------------------
    | Resource Hints
    |--------------------------------------------------------------------------
    |
    | Configuration for resource hints like preload, prefetch, etc.
    |
    */
    'resource_hints_enabled' => true,
    'preload_critical_css' => true,
    'preload_fonts' => true,
    'prefetch_next_page' => false,
];
