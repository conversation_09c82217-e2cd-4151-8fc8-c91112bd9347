<?php

namespace App\Services;

use App\Models\Setting;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;

/**
 * Feature Flag Resolver
 *
 * This class bridges Laravel Pennant with the Settings model,
 * providing feature flag resolution with caching integration.
 */
class FeatureFlagResolver
{
    /**
     * Cache TTL for feature flags (in seconds)
     */
    private const CACHE_TTL = 3600; // 1 hour

    /**
     * Cache key prefix for feature flags
     */
    private const CACHE_PREFIX = 'feature_flag_';

    /**
     * Resolve a feature flag value from settings
     *
     * @param  string  $feature  The feature flag name
     * @return bool The feature flag state
     */
    public function resolve(string $feature): bool
    {
        try {
            // Check if database is available (important for testing)
            if (! $this->isDatabaseAvailable()) {
                return false; // Safe default when database is not available
            }

            $cacheKey = self::CACHE_PREFIX.$feature;

            return Cache::remember($cacheKey, self::CACHE_TTL, function () use ($feature) {
                $settingKey = "feature_flags.{$feature}";
                $value = Setting::get($settingKey, false);

                // Ensure we always return a boolean
                return (bool) $value;
            });
        } catch (\Exception $e) {
            Log::error("Failed to resolve feature flag '{$feature}': ".$e->getMessage());

            // Return false as safe default when resolution fails
            return false;
        }
    }

    /**
     * Define a feature flag with a default value
     *
     * @param  string  $feature  The feature flag name
     * @param  bool  $default  The default value for the flag
     * @param  string|null  $description  Optional description for the flag
     */
    public function define(string $feature, bool $default = false, ?string $description = null): void
    {
        try {
            // Skip if database is not available (e.g., during testing setup)
            if (! $this->isDatabaseAvailable()) {
                return;
            }

            $settingKey = "feature_flags.{$feature}";

            // Only set if the flag doesn't already exist
            if (! Setting::has($settingKey)) {
                Setting::set($settingKey, $default, $description);
            }

            // Clear cache to ensure fresh data
            $this->clearCache($feature);
        } catch (\Exception $e) {
            Log::error("Failed to define feature flag '{$feature}': ".$e->getMessage());
            // Don't throw in testing environment
            if (! app()->environment('testing')) {
                throw $e;
            }
        }
    }

    /**
     * Update a feature flag value
     *
     * @param  string  $feature  The feature flag name
     * @param  bool  $enabled  The new flag state
     */
    public function update(string $feature, bool $enabled): void
    {
        try {
            $settingKey = "feature_flags.{$feature}";
            Setting::set($settingKey, $enabled);

            // Clear cache after update
            $this->clearCache($feature);
        } catch (\Exception $e) {
            Log::error("Failed to update feature flag '{$feature}': ".$e->getMessage());
            throw $e;
        }
    }

    /**
     * Check if a feature flag exists
     *
     * @param  string  $feature  The feature flag name
     * @return bool True if the flag exists
     */
    public function exists(string $feature): bool
    {
        try {
            $settingKey = "feature_flags.{$feature}";

            return Setting::has($settingKey);
        } catch (\Exception $e) {
            Log::error("Failed to check feature flag existence '{$feature}': ".$e->getMessage());

            return false;
        }
    }

    /**
     * Clear cache for a specific feature flag
     *
     * @param  string  $feature  The feature flag name
     */
    public function clearCache(string $feature): void
    {
        try {
            $cacheKey = self::CACHE_PREFIX.$feature;
            Cache::forget($cacheKey);

            // Also clear the settings cache
            $settingCacheKey = "setting_feature_flags.{$feature}";
            Cache::forget($settingCacheKey);
        } catch (\Exception $e) {
            Log::error("Failed to clear cache for feature flag '{$feature}': ".$e->getMessage());
        }
    }

    /**
     * Clear all feature flag caches
     */
    public function clearAllCache(): void
    {
        try {
            // Get all feature flag keys from settings
            $flagKeys = Setting::where('key', 'LIKE', 'feature_flags.%')->pluck('key');

            foreach ($flagKeys as $settingKey) {
                $feature = str_replace('feature_flags.', '', $settingKey);
                $this->clearCache($feature);
            }
        } catch (\Exception $e) {
            Log::error('Failed to clear all feature flag caches: '.$e->getMessage());
        }
    }

    /**
     * Get all feature flags with their current values
     *
     * @return array Array of feature flags with their states
     */
    public function getAllFlags(): array
    {
        try {
            $flags = [];
            $flagSettings = Setting::where('key', 'LIKE', 'feature_flags.%')
                ->where('key', '!=', 'feature_flags._metadata')
                ->get();

            foreach ($flagSettings as $setting) {
                $feature = str_replace('feature_flags.', '', $setting->key);
                $flags[$feature] = (bool) $setting->value;
            }

            return $flags;
        } catch (\Exception $e) {
            Log::error('Failed to get all feature flags: '.$e->getMessage());

            return [];
        }
    }

    /**
     * Check if database is available for operations
     */
    private function isDatabaseAvailable(): bool
    {
        try {
            \Illuminate\Support\Facades\DB::connection()->getPdo();

            // Also check if settings table exists
            return \Illuminate\Support\Facades\Schema::hasTable('settings');
        } catch (\Exception $e) {
            return false;
        }
    }
}
