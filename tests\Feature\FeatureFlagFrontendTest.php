<?php

use App\Models\Setting;
use Illuminate\Foundation\Testing\RefreshDatabase;

uses(RefreshDatabase::class);

function setupFeatureFlagsForFrontend()
{
    // Create metadata
    Setting::updateOrCreate(
        ['key' => 'feature_flags._metadata'],
        [
            'value' => [
                'pages.layanan' => [
                    'title' => 'Halaman Layanan',
                    'description' => 'Mengaktifkan/menonaktifkan akses ke halaman layanan publik desa',
                    'category' => 'pages',
                    'default' => true,
                    'experimental' => false,
                    'warning' => 'Menonaktifkan halaman ini akan menyembunyikan semua informasi layanan dari warga',
                    'dependencies' => [],
                ],
                'pages.berita' => [
                    'title' => 'Halaman Berita',
                    'description' => 'Mengaktifkan/menonaktifkan akses ke halaman berita dan artikel desa',
                    'category' => 'pages',
                    'default' => true,
                    'experimental' => false,
                    'warning' => 'Menonaktifkan halaman ini akan menyembunyikan semua berita dari warga',
                    'dependencies' => [],
                ],
                'pages.potensi' => [
                    'title' => 'Halaman Potensi',
                    'description' => 'Mengaktifkan/menonaktifkan akses ke halaman potensi wisata dan UMKM desa',
                    'category' => 'pages',
                    'default' => true,
                    'experimental' => false,
                    'warning' => 'Menonaktifkan halaman ini akan menyembunyikan informasi wisata dan UMKM',
                    'dependencies' => [],
                ],
                'pages.pengaduan' => [
                    'title' => 'Halaman Pengaduan',
                    'description' => 'Mengaktifkan/menonaktifkan akses ke sistem pengaduan masyarakat',
                    'category' => 'pages',
                    'default' => true,
                    'experimental' => false,
                    'warning' => 'Menonaktifkan halaman ini akan menutup akses warga untuk menyampaikan pengaduan',
                    'dependencies' => [],
                ],
            ],
            'type' => 'json',
            'description' => 'Metadata dan definisi untuk feature flags halaman publik website desa',
        ]
    );

    // Create individual flag settings
    Setting::updateOrCreate(['key' => 'feature_flags.pages.layanan'], ['value' => true, 'type' => 'boolean']);
    Setting::updateOrCreate(['key' => 'feature_flags.pages.berita'], ['value' => true, 'type' => 'boolean']);
    Setting::updateOrCreate(['key' => 'feature_flags.pages.potensi'], ['value' => true, 'type' => 'boolean']);
    Setting::updateOrCreate(['key' => 'feature_flags.pages.pengaduan'], ['value' => true, 'type' => 'boolean']);
}

beforeEach(function () {
    setupFeatureFlagsForFrontend();
});

describe('Feature Flag Frontend Integration', function () {
    it('passes feature flags to frontend via Inertia middleware', function () {
        $response = $this->get('/');

        $response->assertStatus(200);

        // Check that feature flags are passed to frontend
        $page = $response->viewData('page');
        expect($page['props'])->toHaveKey('featureFlags');

        $featureFlags = $page['props']['featureFlags'];
        expect($featureFlags)->toBeArray();

        // Check that all expected flags are present
        expect($featureFlags)->toHaveKey('pages.layanan');
        expect($featureFlags)->toHaveKey('pages.berita');
        expect($featureFlags)->toHaveKey('pages.potensi');
        expect($featureFlags)->toHaveKey('pages.pengaduan');

        // All should be enabled by default
        expect($featureFlags['pages.layanan'])->toBeTrue();
        expect($featureFlags['pages.berita'])->toBeTrue();
        expect($featureFlags['pages.potensi'])->toBeTrue();
        expect($featureFlags['pages.pengaduan'])->toBeTrue();
    });

    it('reflects disabled flags in frontend', function () {
        // Disable some flags
        $resolver = app(\App\Services\FeatureFlagResolver::class);
        $service = new \App\Services\FeatureFlagService($resolver);
        $service->updateFlag('pages.layanan', false);
        $service->updateFlag('pages.berita', false);

        $response = $this->get('/');

        $response->assertStatus(200);

        $page = $response->viewData('page');
        $featureFlags = $page['props']['featureFlags'];

        // Disabled flags should be false
        expect($featureFlags['pages.layanan'])->toBeFalse();
        expect($featureFlags['pages.berita'])->toBeFalse();

        // Enabled flags should still be true
        expect($featureFlags['pages.potensi'])->toBeTrue();
        expect($featureFlags['pages.pengaduan'])->toBeTrue();
    });

    it('handles missing feature flags gracefully', function () {
        // Clear all feature flags from database
        Setting::where('key', 'LIKE', 'feature_flags.%')->delete();

        $response = $this->get('/');

        $response->assertStatus(200);

        $page = $response->viewData('page');
        expect($page['props'])->toHaveKey('featureFlags');

        $featureFlags = $page['props']['featureFlags'];
        expect($featureFlags)->toBeArray();
        // Should be empty array when no flags exist
        expect($featureFlags)->toBeEmpty();
    });

    it('provides consistent feature flags across different pages', function () {
        // Test home page
        $homeResponse = $this->get('/');
        $homePage = $homeResponse->viewData('page');
        $homeFlags = $homePage['props']['featureFlags'];

        // Test profile page
        $profileResponse = $this->get('/profil');
        $profilePage = $profileResponse->viewData('page');
        $profileFlags = $profilePage['props']['featureFlags'];

        // Feature flags should be identical across pages
        expect($homeFlags)->toEqual($profileFlags);

        // Both should have all expected flags
        expect($homeFlags)->toHaveKey('pages.layanan');
        expect($homeFlags)->toHaveKey('pages.berita');
        expect($homeFlags)->toHaveKey('pages.potensi');
        expect($homeFlags)->toHaveKey('pages.pengaduan');

        expect($profileFlags)->toHaveKey('pages.layanan');
        expect($profileFlags)->toHaveKey('pages.berita');
        expect($profileFlags)->toHaveKey('pages.potensi');
        expect($profileFlags)->toHaveKey('pages.pengaduan');
    });

    it('updates frontend flags when backend flags change', function () {
        // Initial state - all enabled
        $response1 = $this->get('/');
        $page1 = $response1->viewData('page');
        $flags1 = $page1['props']['featureFlags'];

        expect($flags1['pages.layanan'])->toBeTrue();
        expect($flags1['pages.berita'])->toBeTrue();

        // Disable some flags
        $resolver = app(\App\Services\FeatureFlagResolver::class);
        $service = new \App\Services\FeatureFlagService($resolver);
        $service->updateFlag('pages.layanan', false);
        $service->updateFlag('pages.berita', false);

        // Clear any caches that might interfere
        $resolver->clearAllCache();

        // Check updated state
        $response2 = $this->get('/');
        $page2 = $response2->viewData('page');
        $flags2 = $page2['props']['featureFlags'];

        expect($flags2['pages.layanan'])->toBeFalse();
        expect($flags2['pages.berita'])->toBeFalse();
        expect($flags2['pages.potensi'])->toBeTrue();
        expect($flags2['pages.pengaduan'])->toBeTrue();
    });
});

describe('Feature Flag Navigation Logic', function () {
    it('should hide navigation links when corresponding flags are disabled', function () {
        // This test verifies the expected behavior for frontend components
        // The actual implementation would be in React components using useFeatureFlags hook

        $resolver = app(\App\Services\FeatureFlagResolver::class);
        $service = new \App\Services\FeatureFlagService($resolver);

        // Test scenario: disable layanan and berita
        $service->updateFlag('pages.layanan', false);
        $service->updateFlag('pages.berita', false);

        $response = $this->get('/');
        $page = $response->viewData('page');
        $featureFlags = $page['props']['featureFlags'];

        // Frontend should receive these flags and hide corresponding navigation
        expect($featureFlags['pages.layanan'])->toBeFalse(); // Should hide "Layanan" nav
        expect($featureFlags['pages.berita'])->toBeFalse();  // Should hide "Berita" nav
        expect($featureFlags['pages.potensi'])->toBeTrue();  // Should show "Potensi" nav
        expect($featureFlags['pages.pengaduan'])->toBeTrue(); // Should show "Pengaduan" nav
    });

    it('should show all navigation links when all flags are enabled', function () {
        // Ensure all flags are enabled
        $resolver = app(\App\Services\FeatureFlagResolver::class);
        $service = new \App\Services\FeatureFlagService($resolver);
        $service->updateFlag('pages.layanan', true);
        $service->updateFlag('pages.berita', true);
        $service->updateFlag('pages.potensi', true);
        $service->updateFlag('pages.pengaduan', true);

        $response = $this->get('/');
        $page = $response->viewData('page');
        $featureFlags = $page['props']['featureFlags'];

        // All navigation should be visible
        expect($featureFlags['pages.layanan'])->toBeTrue();
        expect($featureFlags['pages.berita'])->toBeTrue();
        expect($featureFlags['pages.potensi'])->toBeTrue();
        expect($featureFlags['pages.pengaduan'])->toBeTrue();
    });
});

describe('Feature Flag Error Handling', function () {
    it('handles database connection errors gracefully', function () {
        // This test is complex to implement properly in testing environment
        // The middleware has proper error handling that returns empty array on database errors
        // We'll test this by verifying the middleware doesn't crash when feature flags are missing

        // Clear all feature flags to simulate database issue
        Setting::where('key', 'LIKE', 'feature_flags.%')->delete();
        \Illuminate\Support\Facades\Cache::flush();

        // Should not throw exception, should return empty array or handle gracefully
        $response = $this->get('/');

        $response->assertStatus(200);

        $page = $response->viewData('page');
        expect($page['props'])->toHaveKey('featureFlags');

        $featureFlags = $page['props']['featureFlags'];
        expect($featureFlags)->toBeArray();
        // Should be empty or have default values when flags are missing
    });

    it('handles corrupted feature flag data gracefully', function () {
        // Insert corrupted data
        Setting::create([
            'key' => 'feature_flags.pages.corrupted',
            'value' => 'invalid_boolean_value',
            'type' => 'boolean',
        ]);

        $response = $this->get('/');

        // Should not crash, should handle gracefully
        $response->assertStatus(200);

        $page = $response->viewData('page');
        expect($page['props'])->toHaveKey('featureFlags');

        $featureFlags = $page['props']['featureFlags'];
        expect($featureFlags)->toBeArray();
    });
});
