<?php

namespace App\Console\Commands;

use App\Models\News;
use App\Models\Potential;
use App\Models\Profile;
use App\Models\Service;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Route;

class CacheWarmUpCommand extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'cache:warm-up {--force : Force cache refresh}';

    /**
     * The console command description.
     */
    protected $description = 'Warm up application cache for better performance';

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $this->info('🔥 Starting cache warm-up process...');

        $force = $this->option('force');

        // Warm up model caches
        $this->warmUpModelCaches($force);

        // Warm up route caches
        $this->warmUpRouteCaches();

        // Warm up configuration caches
        $this->warmUpConfigCaches();

        $this->info('✅ Cache warm-up completed successfully!');

        return Command::SUCCESS;
    }

    /**
     * Warm up model-based caches
     */
    private function warmUpModelCaches(bool $force = false): void
    {
        $this->info('📊 Warming up model caches...');

        // Cache latest news
        $cacheKey = 'homepage.latest_news';
        if ($force || ! Cache::has($cacheKey)) {
            $latestNews = News::where('is_published', true)
                ->latest('published_at')
                ->take(4)
                ->get(['id', 'title', 'slug', 'excerpt', 'featured_image', 'published_at', 'category']);

            Cache::put($cacheKey, $latestNews, now()->addHours(6));
            $this->line('  • Latest news cached');
        }

        // Cache featured potentials
        $cacheKey = 'homepage.featured_potentials';
        if ($force || ! Cache::has($cacheKey)) {
            $featuredPotentials = Potential::where('is_featured', true)
                ->take(4)
                ->get(['id', 'name', 'type', 'description', 'images', 'location']);

            Cache::put($cacheKey, $featuredPotentials, now()->addHours(12));
            $this->line('  • Featured potentials cached');
        }

        // Cache active services
        $cacheKey = 'services.active';
        if ($force || ! Cache::has($cacheKey)) {
            $activeServices = Service::where('is_active', true)
                ->orderBy('name')
                ->get(['id', 'name', 'description', 'cost', 'processing_time']);

            Cache::put($cacheKey, $activeServices, now()->addHours(24));
            $this->line('  • Active services cached');
        }

        // Cache village profile
        $cacheKey = 'profile.village_data';
        if ($force || ! Cache::has($cacheKey)) {
            $profileData = Profile::orderBy('order')
                ->get(['section', 'title', 'content', 'image'])
                ->keyBy('section');

            Cache::put($cacheKey, $profileData, now()->addDays(7));
            $this->line('  • Village profile cached');
        }

        // Cache tourism potentials
        $cacheKey = 'potentials.tourism';
        if ($force || ! Cache::has($cacheKey)) {
            $tourismPotentials = Potential::where('type', 'tourism')
                ->orderBy('is_featured', 'desc')
                ->orderBy('name')
                ->get();

            Cache::put($cacheKey, $tourismPotentials, now()->addHours(12));
            $this->line('  • Tourism potentials cached');
        }

        // Cache UMKM potentials
        $cacheKey = 'potentials.umkm';
        if ($force || ! Cache::has($cacheKey)) {
            $umkmPotentials = Potential::where('type', 'umkm')
                ->orderBy('is_featured', 'desc')
                ->orderBy('name')
                ->get();

            Cache::put($cacheKey, $umkmPotentials, now()->addHours(12));
            $this->line('  • UMKM potentials cached');
        }

        // Cache news categories
        $cacheKey = 'news.categories';
        if ($force || ! Cache::has($cacheKey)) {
            $categories = News::where('is_published', true)
                ->distinct()
                ->pluck('category')
                ->filter()
                ->sort()
                ->values();

            Cache::put($cacheKey, $categories, now()->addDays(1));
            $this->line('  • News categories cached');
        }
    }

    /**
     * Warm up route caches
     */
    private function warmUpRouteCaches(): void
    {
        $this->info('🛣️  Warming up route caches...');

        // Get all registered routes
        $routes = Route::getRoutes();

        $publicRoutes = 0;
        foreach ($routes as $route) {
            if (str_starts_with($route->getName() ?? '', 'public.')) {
                $publicRoutes++;
            }
        }

        $this->line("  • {$publicRoutes} public routes ready");
    }

    /**
     * Warm up configuration caches
     */
    private function warmUpConfigCaches(): void
    {
        $this->info('⚙️  Warming up configuration caches...');

        // Cache application settings
        $settings = [
            'app.name' => config('app.name'),
            'app.url' => config('app.url'),
            'app.timezone' => config('app.timezone'),
            'app.locale' => config('app.locale'),
        ];

        Cache::put('app.settings', $settings, now()->addDays(30));
        $this->line('  • Application settings cached');

        // Cache performance settings
        $performanceSettings = [
            'page_cache_enabled' => config('performance.page_cache_enabled', true),
            'page_cache_ttl' => config('performance.page_cache_ttl', 3600),
            'image_optimization_enabled' => config('performance.image_optimization_enabled', true),
            'lazy_loading_enabled' => config('performance.lazy_loading_enabled', true),
        ];

        Cache::put('performance.settings', $performanceSettings, now()->addDays(30));
        $this->line('  • Performance settings cached');
    }
}
