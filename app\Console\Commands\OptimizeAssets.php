<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\File;

class OptimizeAssets extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'assets:optimize {--force : Force optimization even if files exist}';

    /**
     * The console command description.
     */
    protected $description = 'Optimize static assets for production';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Starting asset optimization...');

        // Create gzipped versions of CSS and JS files
        $this->compressAssets();

        // Optimize images if needed
        $this->optimizeImages();

        // Generate asset manifest for cache busting
        $this->generateAssetManifest();

        $this->info('Asset optimization completed!');
    }

    /**
     * Compress CSS and JS assets
     */
    private function compressAssets(): void
    {
        $publicPath = public_path();
        $buildPath = $publicPath.'/build';

        if (! File::exists($buildPath)) {
            $this->warn('Build directory not found. Run npm run build first.');

            return;
        }

        $this->info('Compressing CSS and JS files...');

        // Find all CSS and JS files in build directory
        $files = File::allFiles($buildPath);

        foreach ($files as $file) {
            $extension = $file->getExtension();

            if (in_array($extension, ['css', 'js'])) {
                $filePath = $file->getPathname();
                $gzipPath = $filePath.'.gz';

                // Create gzipped version if it doesn't exist or force is used
                if (! File::exists($gzipPath) || $this->option('force')) {
                    $content = File::get($filePath);
                    $compressed = gzencode($content, 9);
                    File::put($gzipPath, $compressed);

                    $originalSize = File::size($filePath);
                    $compressedSize = File::size($gzipPath);
                    $savings = round((1 - $compressedSize / $originalSize) * 100, 1);

                    $this->line("Compressed {$file->getFilename()} - {$savings}% smaller");
                }
            }
        }
    }

    /**
     * Optimize images in public directory
     */
    private function optimizeImages(): void
    {
        $this->info('Checking for image optimization opportunities...');

        $imageDirectories = [
            public_path('images'),
            public_path('storage/images'),
        ];

        foreach ($imageDirectories as $directory) {
            if (File::exists($directory)) {
                $images = File::allFiles($directory);

                foreach ($images as $image) {
                    $extension = strtolower($image->getExtension());

                    if (in_array($extension, ['jpg', 'jpeg', 'png', 'gif'])) {
                        // Check if WebP version exists
                        $webpPath = $image->getPath().'/'.$image->getFilenameWithoutExtension().'.webp';

                        if (! File::exists($webpPath)) {
                            $this->line("Consider creating WebP version of: {$image->getFilename()}");
                        }
                    }
                }
            }
        }
    }

    /**
     * Generate asset manifest for cache busting
     */
    private function generateAssetManifest(): void
    {
        $this->info('Generating asset manifest...');

        $buildPath = public_path('build');
        $manifest = [];

        if (File::exists($buildPath)) {
            $files = File::allFiles($buildPath);

            foreach ($files as $file) {
                $relativePath = str_replace($buildPath.'/', '', $file->getPathname());
                $manifest[$relativePath] = [
                    'size' => $file->getSize(),
                    'modified' => $file->getMTime(),
                    'hash' => md5_file($file->getPathname()),
                ];
            }

            File::put(
                public_path('build/manifest.json'),
                json_encode($manifest, JSON_PRETTY_PRINT)
            );

            $this->info('Asset manifest generated successfully.');
        }
    }
}
