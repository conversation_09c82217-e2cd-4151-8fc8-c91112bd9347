<?php

namespace App\Http\Requests\Settings;

use Illuminate\Foundation\Http\FormRequest;

class UpdateFeatureFlagsRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        // Only allow authenticated admin users to update feature flags
        return $this->user() && $this->user()->isAdmin();
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'feature_flags' => ['required', 'array'],
            'feature_flags.*' => ['required', 'boolean'],
        ];
    }

    /**
     * Get custom validation error messages in Indonesian.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'feature_flags.required' => 'Data feature flag wajib diisi.',
            'feature_flags.array' => 'Data feature flag harus berupa array.',
            'feature_flags.*.required' => 'Status feature flag wajib diisi.',
            'feature_flags.*.boolean' => 'Status feature flag harus berupa true atau false.',
        ];
    }

    /**
     * Get custom attribute names for validation errors.
     *
     * @return array<string, string>
     */
    public function attributes(): array
    {
        return [
            'feature_flags' => 'feature flags',
            'feature_flags.*' => 'status feature flag',
        ];
    }
}
