import { useEffect, useState } from 'react';
import { ErrorNotification } from './ErrorNotification';

interface NotificationState {
    message: string;
    type: 'error' | 'warning' | 'info';
    id: number;
}

export function GlobalErrorProvider({ children }: { children: React.ReactNode }) {
    const [notifications, setNotifications] = useState<NotificationState[]>([]);

    useEffect(() => {
        const handleErrorNotification = (event: CustomEvent) => {
            const { message, type = 'error' } = event.detail;
            const id = Date.now();

            setNotifications((prev) => [...prev, { message, type, id }]);
        };

        window.addEventListener('show-error-notification', handleErrorNotification as EventListener);

        return () => {
            window.removeEventListener('show-error-notification', handleErrorNotification as EventListener);
        };
    }, []);

    const removeNotification = (id: number) => {
        setNotifications((prev) => prev.filter((notification) => notification.id !== id));
    };

    return (
        <>
            {children}
            {notifications.map((notification) => (
                <ErrorNotification
                    key={notification.id}
                    message={notification.message}
                    type={notification.type}
                    onClose={() => removeNotification(notification.id)}
                />
            ))}
        </>
    );
}
