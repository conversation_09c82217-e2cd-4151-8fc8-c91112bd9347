import { DarkModeToggle } from '@/components/DarkModeToggle';
import { useSettings } from '@/hooks/useSettings';
import { Link } from '@inertiajs/react';
import { Clock, Mail, MapPin, Phone } from 'lucide-react';

export function Footer() {
    const settings = useSettings();
    const contactInfo = settings.getContactInfo();
    const villageProfile = settings.getVillageProfile();
    const operatingHours = settings.getOperatingHours();

    return (
        <footer className="border-t bg-muted/50">
            <div className="container mx-auto px-3 py-8 sm:px-4 sm:py-12 lg:px-8">
                <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 sm:gap-8 lg:grid-cols-4">
                    {/* Informasi Desa */}
                    <div className="space-y-3 sm:col-span-2 sm:space-y-4 lg:col-span-1">
                        <div className="flex items-center space-x-2">
                            <div className="flex h-8 w-8 flex-shrink-0 items-center justify-center">
                                <img src="/images/logo-kabupaten-bogor.png" alt="logo kabupaten bogor" />
                            </div>
                            <div className="min-w-0">
                                <h3 className="font-semibold text-foreground">{villageProfile?.name || 'Desa Lemah Duhur'}</h3>
                                <p className="text-xs text-muted-foreground">{villageProfile?.regency || 'Kabupaten Bogor'}</p>
                            </div>
                        </div>
                        <p className="text-sm leading-relaxed text-muted-foreground">
                            Website desa dibangun dengan tujuan sebagai media pelayanan publik resmi desa, yang dibangun dan dikelola oleh tim desa
                            setempat. Dengan memanfaatkan website penyelenggaraan pelayanan publik dapat dilakukan secara cepat dan mudah
                        </p>

                        <DarkModeToggle />
                    </div>

                    {/* Menu Navigasi */}
                    <div className="space-y-3 sm:space-y-4">
                        <h4 className="font-semibold text-foreground">Menu</h4>
                        <nav className="space-y-2">
                            <Link
                                href="/profil"
                                className="block touch-manipulation py-1 text-sm text-muted-foreground transition-colors hover:text-foreground"
                            >
                                Profil Desa
                            </Link>
                            <Link
                                href="/layanan"
                                className="block touch-manipulation py-1 text-sm text-muted-foreground transition-colors hover:text-foreground"
                            >
                                Layanan Publik
                            </Link>
                            <Link
                                href="/berita"
                                className="block touch-manipulation py-1 text-sm text-muted-foreground transition-colors hover:text-foreground"
                            >
                                Berita & Pengumuman
                            </Link>
                            <Link
                                href="/potensi"
                                className="block touch-manipulation py-1 text-sm text-muted-foreground transition-colors hover:text-foreground"
                            >
                                Potensi Desa
                            </Link>
                        </nav>
                    </div>

                    {/* Kontak */}
                    <div className="space-y-3 sm:space-y-4">
                        <h4 className="font-semibold text-foreground">Kontak</h4>
                        <div className="space-y-3">
                            <div className="flex items-start space-x-3">
                                <MapPin className="mt-0.5 h-4 w-4 flex-shrink-0 text-muted-foreground" />
                                <div className="min-w-0 text-sm text-muted-foreground">
                                    <p>
                                        {contactInfo?.address || 'Kantor Desa Lemah Duhur, Kec. Caringin, Kab. Bogor, Jawa Barat'}
                                        {contactInfo?.postal_code && ` ${contactInfo.postal_code}`}
                                    </p>
                                </div>
                            </div>
                            {contactInfo?.phone && (
                                <div className="flex items-center space-x-3">
                                    <Phone className="h-4 w-4 flex-shrink-0 text-muted-foreground" />
                                    <span className="text-sm break-all text-muted-foreground">{contactInfo.phone}</span>
                                </div>
                            )}
                            {contactInfo?.email && (
                                <div className="flex items-center space-x-3">
                                    <Mail className="h-4 w-4 flex-shrink-0 text-muted-foreground" />
                                    <span className="text-sm break-all text-muted-foreground">{contactInfo.email}</span>
                                </div>
                            )}
                        </div>
                    </div>

                    {/* Jam Pelayanan */}
                    <div className="space-y-3 sm:space-y-4">
                        <h4 className="font-semibold text-foreground">Jam Pelayanan</h4>
                        <div className="space-y-3">
                            <div className="flex items-start space-x-3">
                                <Clock className="mt-0.5 h-4 w-4 flex-shrink-0 text-muted-foreground" />
                                <div className="text-sm text-muted-foreground">
                                    <p className="font-medium">Senin - Jumat</p>
                                    <p>{operatingHours?.weekdays || '08:00 - 15:00 WIB'}</p>
                                </div>
                            </div>
                            <div className="flex items-start space-x-3">
                                <div className="h-4 w-4 flex-shrink-0"></div>
                                <div className="text-sm text-muted-foreground">
                                    <p className="font-medium">Sabtu</p>
                                    <p>{operatingHours?.saturday || '08:00 - 12:00 WIB'}</p>
                                </div>
                            </div>
                            <div className="flex items-start space-x-3">
                                <div className="h-4 w-4 flex-shrink-0"></div>
                                <div className="text-sm text-muted-foreground">
                                    <p className="font-medium">Minggu & Libur</p>
                                    <p className="text-red-500">{operatingHours?.sunday || 'Tutup'}</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                {/* Copyright */}
                <div className="mt-6 border-t border-border pt-6 sm:mt-8 sm:pt-8">
                    <div className="flex flex-col items-center justify-between space-y-3 sm:flex-row sm:space-y-0">
                        <div className="flex flex-col items-center space-y-2 sm:flex-row sm:space-y-0 sm:space-x-4">
                            <p className="text-center text-xs text-muted-foreground sm:text-left sm:text-sm">
                                © 2025 {villageProfile?.name || 'Desa Lemah Duhur'}. Seluruh hak cipta dilindungi.
                            </p>
                        </div>
                        <p className="text-center text-xs text-muted-foreground sm:text-right sm:text-sm">Dibuat dengan ❤️ untuk masyarakat desa</p>
                    </div>
                </div>
            </div>
        </footer>
    );
}
