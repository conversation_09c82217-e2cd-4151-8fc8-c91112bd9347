<?php

namespace App\Observers;

use App\Models\News;
use App\Services\CacheService;

class NewsObserver
{
    public function __construct(
        private CacheService $cacheService
    ) {}

    /**
     * Handle the News "created" event.
     */
    public function created(News $news): void
    {
        $this->clearCache();
    }

    /**
     * Handle the News "updated" event.
     */
    public function updated(News $news): void
    {
        $this->clearCache();

        // Clear specific related news cache if category changed
        if ($news->wasChanged('category')) {
            cache()->forget("related_news_{$news->getOriginal('category')}_{$news->id}");
            cache()->forget("related_news_{$news->category}_{$news->id}");
        }
    }

    /**
     * Handle the News "deleted" event.
     */
    public function deleted(News $news): void
    {
        $this->clearCache();
        cache()->forget("related_news_{$news->category}_{$news->id}");
    }

    /**
     * Clear news-related cache
     */
    private function clearCache(): void
    {
        $this->cacheService->clearNewsCache();
        cache()->forget('homepage_news');
        cache()->forget('news_categories');
    }
}
