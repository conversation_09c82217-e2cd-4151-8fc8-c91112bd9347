<?php

namespace App\Http\Traits;

use App\Services\ImageOptimizationService;
use Illuminate\Http\Request;

trait HandlesImageUploads
{
    protected ImageOptimizationService $imageService;

    /**
     * Initialize image service
     */
    protected function initializeImageService(): void
    {
        if (! isset($this->imageService)) {
            $this->imageService = app(ImageOptimizationService::class);
        }
    }

    /**
     * Handle single image upload
     */
    protected function handleImageUpload(
        Request $request,
        string $fieldName,
        string $directory = 'images',
        array $sizes = ['original' => null, 'medium' => 800, 'thumbnail' => 300]
    ): ?array {
        $this->initializeImageService();

        if (! $request->hasFile($fieldName)) {
            return null;
        }

        $file = $request->file($fieldName);

        throw_unless($file->isValid(), new \InvalidArgumentException('File upload tidak valid.'));

        try {
            return $this->imageService->uploadAndOptimize($file, $directory, $sizes);
        } catch (\Exception $e) {
            throw new \InvalidArgumentException('Gagal mengupload gambar: '.$e->getMessage(), $e->getCode(), $e);
        }
    }

    /**
     * Handle multiple image uploads
     */
    protected function handleMultipleImageUploads(
        Request $request,
        string $fieldName,
        string $directory = 'images',
        array $sizes = ['original' => null, 'medium' => 800, 'thumbnail' => 300]
    ): array {
        $this->initializeImageService();

        if (! $request->hasFile($fieldName)) {
            return [];
        }

        $files = $request->file($fieldName);
        $results = [];

        foreach ($files as $file) {
            if ($file->isValid()) {
                try {
                    $results[] = $this->imageService->uploadAndOptimize($file, $directory, $sizes);
                } catch (\Exception $e) {
                    // Log error but continue with other files
                    \Log::error('Image upload failed: '.$e->getMessage());
                }
            }
        }

        return $results;
    }

    /**
     * Delete old image when updating
     */
    protected function deleteOldImage(?string $oldImagePath): void
    {
        if ($oldImagePath) {
            $this->initializeImageService();
            $this->imageService->deleteImage($oldImagePath);
        }
    }

    /**
     * Validate image upload request
     */
    protected function validateImageUpload(Request $request, string $fieldName, bool $required = false): void
    {
        $rules = [
            $fieldName => [
                $required ? 'required' : 'nullable',
                'image',
                'mimes:jpeg,png,jpg,gif,webp',
                'max:10240', // 10MB
            ],
        ];

        $messages = [
            $fieldName.'.required' => 'Gambar wajib diupload.',
            $fieldName.'.image' => 'File harus berupa gambar.',
            $fieldName.'.mimes' => 'Format gambar harus JPEG, PNG, JPG, GIF, atau WebP.',
            $fieldName.'.max' => 'Ukuran gambar maksimal 10MB.',
        ];

        $request->validate($rules, $messages);
    }

    /**
     * Validate multiple image uploads
     */
    protected function validateMultipleImageUploads(Request $request, string $fieldName, bool $required = false): void
    {
        $rules = [
            $fieldName => $required ? 'required|array' : 'nullable|array',
            $fieldName.'.*' => [
                'image',
                'mimes:jpeg,png,jpg,gif,webp',
                'max:10240', // 10MB
            ],
        ];

        $messages = [
            $fieldName.'.required' => 'Minimal satu gambar wajib diupload.',
            $fieldName.'.array' => 'Format upload gambar tidak valid.',
            $fieldName.'.*.image' => 'Semua file harus berupa gambar.',
            $fieldName.'.*.mimes' => 'Format gambar harus JPEG, PNG, JPG, GIF, atau WebP.',
            $fieldName.'.*.max' => 'Ukuran setiap gambar maksimal 10MB.',
        ];

        $request->validate($rules, $messages);
    }
}
