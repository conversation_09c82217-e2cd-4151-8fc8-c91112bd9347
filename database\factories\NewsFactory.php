<?php

namespace Database\Factories;

use App\Models\News;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\News>
 */
class NewsFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = News::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $title = $this->faker->sentence(6);
        $content = $this->faker->paragraphs(5, true);

        return [
            'title' => $title,
            'slug' => null, // Let the model generate the slug
            'content' => $content,
            'excerpt' => $this->faker->paragraph(3),
            'featured_image' => null,
            'category' => $this->faker->randomElement(['pengumuman', 'kegiatan', 'pembangunan', 'sosial']),
            'published_at' => $this->faker->dateTimeBetween('-1 month', 'now'),
            'is_published' => $this->faker->boolean(80),
            'meta_title' => null,
            'meta_description' => null,
        ];
    }

    /**
     * Indicate that the news is published.
     */
    public function published(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_published' => true,
            'published_at' => $this->faker->dateTimeBetween('-1 month', 'now'),
        ]);
    }

    /**
     * Indicate that the news is unpublished.
     */
    public function unpublished(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_published' => false,
            'published_at' => null,
        ]);
    }

    /**
     * Indicate that the news has a featured image.
     */
    public function withFeaturedImage(): static
    {
        return $this->state(fn (array $attributes) => [
            'featured_image' => [
                'original' => [
                    'url' => 'storage/news/original/'.$this->faker->uuid().'.jpg',
                    'width' => 1200,
                    'height' => 800,
                ],
                'medium' => [
                    'url' => 'storage/news/medium/'.$this->faker->uuid().'.jpg',
                    'width' => 800,
                    'height' => 533,
                ],
                'thumbnail' => [
                    'url' => 'storage/news/thumbnail/'.$this->faker->uuid().'.jpg',
                    'width' => 300,
                    'height' => 200,
                ],
            ],
        ]);
    }

    /**
     * Create news for a specific category.
     */
    public function category(string $category): static
    {
        return $this->state(fn (array $attributes) => [
            'category' => $category,
        ]);
    }

    /**
     * Create news with custom meta tags.
     */
    public function withMeta(): static
    {
        return $this->state(fn (array $attributes) => [
            'meta_title' => $this->faker->sentence(8),
            'meta_description' => $this->faker->paragraph(2),
        ]);
    }

    /**
     * Create future published news.
     */
    public function futurePublished(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_published' => true,
            'published_at' => $this->faker->dateTimeBetween('now', '+1 month'),
        ]);
    }
}
