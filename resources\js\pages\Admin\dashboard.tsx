import { Badge } from '@/components/ui/badge';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import AdminLayout from '@/layouts/AdminLayout';
import { type BreadcrumbItem } from '@/types';
import { <PERSON>, <PERSON> } from '@inertiajs/react';
import {
    Activity,
    AlertTriangle,
    Building2,
    Calendar,
    CheckCircle,
    Clock,
    Eye,
    FileText,
    MapPin,
    MessageSquare,
    Settings,
    Star,
    TrendingUp,
    Users,
} from 'lucide-react';

interface Stats {
    total_news: number;
    published_news: number;
    draft_news: number;
    total_services: number;
    active_services: number;
    inactive_services: number;
    total_potentials: number;
    featured_potentials: number;
    tourism_potentials: number;
    umkm_potentials: number;
    total_users: number;
    profile_sections: number;
    total_complaints: number;
    pending_complaints: number;
    urgent_complaints: number;
}

interface ActivityStats {
    news_this_month: number;
    services_updated: number;
    potentials_added: number;
    complaints_this_month: number;
}

interface RecentNews {
    id: number;
    title: string;
    slug: string;
    is_published: boolean;
    created_at: string;
    category: string;
}

interface RecentPotential {
    id: number;
    name: string;
    type: string;
    is_featured: boolean;
    created_at: string;
}

interface RecentComplaint {
    id: number;
    ticket_number: string;
    name: string;
    subject: string;
    status: string;
    priority: string;
    created_at: string;
}

interface QuickAction {
    title: string;
    description: string;
    href: string;
    icon: string;
    color: string;
    count: number;
    label: string;
}

interface Props {
    stats: Stats;
    activity_stats: ActivityStats;
    recent_news: RecentNews[];
    recent_potentials: RecentPotential[];
    recent_complaints: RecentComplaint[];
    quick_actions: QuickAction[];
}

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'Dashboard',
        href: '/dashboard',
    },
];

const iconMap = {
    FileText,
    Settings,
    MapPin,
    Building: Building2,
    Users,
    MessageSquare,
};

export default function Dashboard({ stats, activity_stats, recent_news, recent_potentials, recent_complaints, quick_actions }: Props) {
    const mainStatCards = [
        {
            title: 'Total Berita',
            value: stats.total_news,
            subtitle: `${stats.published_news} dipublikasikan, ${stats.draft_news} draft`,
            icon: FileText,
            color: 'text-blue-600',
            bgColor: 'bg-blue-50',
        },
        {
            title: 'Layanan Publik',
            value: stats.total_services,
            subtitle: `${stats.active_services} aktif, ${stats.inactive_services} nonaktif`,
            icon: Settings,
            color: 'text-green-600',
            bgColor: 'bg-green-50',
        },
        {
            title: 'Potensi Desa',
            value: stats.total_potentials,
            subtitle: `${stats.tourism_potentials} wisata, ${stats.umkm_potentials} UMKM`,
            icon: MapPin,
            color: 'text-purple-600',
            bgColor: 'bg-purple-50',
        },
        {
            title: 'Pengaduan Masyarakat',
            value: stats.total_complaints,
            subtitle: `${stats.pending_complaints} menunggu, ${stats.urgent_complaints} mendesak`,
            icon: MessageSquare,
            color: 'text-red-600',
            bgColor: 'bg-red-50',
        },
    ];

    const activityCards = [
        {
            title: 'Berita Bulan Ini',
            value: activity_stats.news_this_month,
            icon: Calendar,
            color: 'text-blue-600',
        },
        {
            title: 'Layanan Diperbarui',
            value: activity_stats.services_updated,
            icon: Activity,
            color: 'text-green-600',
        },
        {
            title: 'Potensi Ditambahkan',
            value: activity_stats.potentials_added,
            icon: TrendingUp,
            color: 'text-purple-600',
        },
        {
            title: 'Pengaduan Bulan Ini',
            value: activity_stats.complaints_this_month,
            icon: MessageSquare,
            color: 'text-red-600',
        },
    ];

    return (
        <AdminLayout breadcrumbs={breadcrumbs}>
            <Head title="Dashboard - Desa Lemah Duhur" />

            <div className="flex h-full flex-1 flex-col gap-6 bg-gray-50 p-6 dark:bg-gray-950">
                {/* Welcome Header */}
                <div className="flex flex-col gap-2">
                    <h1 className="text-3xl font-bold text-gray-900 dark:text-white">Dashboard</h1>
                    <p className="text-gray-600 dark:text-gray-300">Selamat datang di panel administrasi Website Desa Lemah Duhur</p>
                </div>

                {/* Main Statistics */}
                <div className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-4">
                    {mainStatCards.map((stat, index) => {
                        const IconComponent = stat.icon;
                        return (
                            <Card key={index} className="transition-all hover:shadow-md dark:bg-gray-800">
                                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                                    <CardTitle className="text-sm font-medium text-gray-600 dark:text-gray-300">{stat.title}</CardTitle>
                                    <div className={`rounded-lg p-2 ${stat.bgColor} dark:bg-opacity-10`}>
                                        <IconComponent className={`h-5 w-5 ${stat.color} dark:text-opacity-80`} />
                                    </div>
                                </CardHeader>
                                <CardContent>
                                    <div className="text-3xl font-bold text-gray-900 dark:text-white">{stat.value}</div>
                                    <p className="mt-1 text-xs text-gray-500 dark:text-gray-400">{stat.subtitle}</p>
                                </CardContent>
                            </Card>
                        );
                    })}
                </div>

                {/* Activity Statistics */}
                <Card className="dark:bg-gray-800">
                    <CardHeader>
                        <CardTitle className="flex items-center space-x-2">
                            <Activity className="h-5 w-5 text-blue-600" />
                            <span>Aktivitas 30 Hari Terakhir</span>
                        </CardTitle>
                    </CardHeader>
                    <CardContent>
                        <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-4">
                            {activityCards.map((activity, index) => {
                                const IconComponent = activity.icon;
                                return (
                                    <div key={index} className="flex items-center space-x-3 rounded-lg bg-gray-50 p-4 dark:bg-gray-900">
                                        <IconComponent className={`h-6 w-6 ${activity.color} dark:text-opacity-80`} />
                                        <div>
                                            <div className="text-2xl font-bold text-gray-900 dark:text-white">{activity.value}</div>
                                            <p className="text-sm text-gray-600 dark:text-gray-300">{activity.title}</p>
                                        </div>
                                    </div>
                                );
                            })}
                        </div>
                    </CardContent>
                </Card>

                {/* Content Overview */}
                <div className="grid grid-cols-1 gap-6 lg:grid-cols-3">
                    {/* Recent News */}
                    <Card className="dark:bg-gray-800">
                        <CardHeader>
                            <CardTitle className="flex items-center justify-between">
                                <div className="flex items-center space-x-2">
                                    <FileText className="h-5 w-5 text-blue-600" />
                                    <span>Berita Terbaru</span>
                                </div>
                                <Link
                                    href="/admin/news"
                                    className="text-sm text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300"
                                >
                                    Lihat Semua
                                </Link>
                            </CardTitle>
                        </CardHeader>
                        <CardContent>
                            <div className="space-y-3">
                                {recent_news.length > 0 ? (
                                    recent_news.map((news) => (
                                        <div key={news.id} className="flex items-start justify-between rounded-lg border p-3 dark:border-gray-700">
                                            <div className="flex-1">
                                                <h4 className="line-clamp-2 text-sm font-medium text-gray-900 dark:text-white">{news.title}</h4>
                                                <div className="mt-2 flex items-center space-x-2">
                                                    <Badge variant="outline" className="text-xs">
                                                        {news.category}
                                                    </Badge>
                                                    <span className="text-xs text-gray-500 dark:text-gray-400">
                                                        {new Date(news.created_at).toLocaleDateString('id-ID')}
                                                    </span>
                                                </div>
                                            </div>
                                            <div className="ml-3">
                                                {news.is_published ? (
                                                    <CheckCircle className="h-4 w-4 text-green-600" />
                                                ) : (
                                                    <Eye className="h-4 w-4 text-gray-400 dark:text-gray-600" />
                                                )}
                                            </div>
                                        </div>
                                    ))
                                ) : (
                                    <p className="py-8 text-center text-sm text-gray-500 dark:text-gray-400">Belum ada berita yang dibuat</p>
                                )}
                            </div>
                        </CardContent>
                    </Card>

                    {/* Recent Potentials */}
                    <Card className="dark:bg-gray-800">
                        <CardHeader>
                            <CardTitle className="flex items-center justify-between">
                                <div className="flex items-center space-x-2">
                                    <MapPin className="h-5 w-5 text-purple-600" />
                                    <span>Potensi Terbaru</span>
                                </div>
                                <Link
                                    href="/admin/potentials"
                                    className="text-sm text-purple-600 hover:text-purple-800 dark:text-purple-400 dark:hover:text-purple-300"
                                >
                                    Lihat Semua
                                </Link>
                            </CardTitle>
                        </CardHeader>
                        <CardContent>
                            <div className="space-y-3">
                                {recent_potentials.length > 0 ? (
                                    recent_potentials.map((potential) => (
                                        <div
                                            key={potential.id}
                                            className="flex items-start justify-between rounded-lg border p-3 dark:border-gray-700"
                                        >
                                            <div className="flex-1">
                                                <h4 className="text-sm font-medium text-gray-900 dark:text-white">{potential.name}</h4>
                                                <div className="mt-2 flex items-center space-x-2">
                                                    <Badge variant={potential.type === 'tourism' ? 'default' : 'secondary'} className="text-xs">
                                                        {potential.type === 'tourism' ? 'Wisata' : 'UMKM'}
                                                    </Badge>
                                                    <span className="text-xs text-gray-500 dark:text-gray-400">
                                                        {new Date(potential.created_at).toLocaleDateString('id-ID')}
                                                    </span>
                                                </div>
                                            </div>
                                            <div className="ml-3">
                                                {potential.is_featured && <Star className="h-4 w-4 fill-current text-yellow-500" />}
                                            </div>
                                        </div>
                                    ))
                                ) : (
                                    <p className="py-8 text-center text-sm text-gray-500 dark:text-gray-400">Belum ada potensi yang ditambahkan</p>
                                )}
                            </div>
                        </CardContent>
                    </Card>

                    {/* Recent Complaints */}
                    <Card className="dark:bg-gray-800">
                        <CardHeader>
                            <CardTitle className="flex items-center justify-between">
                                <div className="flex items-center space-x-2">
                                    <MessageSquare className="h-5 w-5 text-red-600" />
                                    <span>Pengaduan Terbaru</span>
                                </div>
                                <Link
                                    href="/admin/complaints"
                                    className="text-sm text-red-600 hover:text-red-800 dark:text-red-400 dark:hover:text-red-300"
                                >
                                    Lihat Semua
                                </Link>
                            </CardTitle>
                        </CardHeader>
                        <CardContent>
                            <div className="space-y-3">
                                {recent_complaints.length > 0 ? (
                                    recent_complaints.map((complaint) => (
                                        <div
                                            key={complaint.id}
                                            className="flex items-start justify-between rounded-lg border p-3 dark:border-gray-700"
                                        >
                                            <div className="flex-1">
                                                <h4 className="text-sm font-medium text-gray-900 dark:text-white">{complaint.ticket_number}</h4>
                                                <p className="mt-1 text-xs text-gray-600 dark:text-gray-300">{complaint.subject}</p>
                                                <div className="mt-2 flex items-center space-x-2">
                                                    <Badge
                                                        variant="outline"
                                                        className={`text-xs ${
                                                            complaint.status === 'pending'
                                                                ? 'border-yellow-200 bg-yellow-50 text-yellow-800'
                                                                : complaint.status === 'in_progress'
                                                                  ? 'border-blue-200 bg-blue-50 text-blue-800'
                                                                  : complaint.status === 'resolved'
                                                                    ? 'border-green-200 bg-green-50 text-green-800'
                                                                    : 'border-gray-200 bg-gray-50 text-gray-800'
                                                        }`}
                                                    >
                                                        {complaint.status === 'pending'
                                                            ? 'Menunggu'
                                                            : complaint.status === 'in_progress'
                                                              ? 'Diproses'
                                                              : complaint.status === 'resolved'
                                                                ? 'Selesai'
                                                                : 'Ditutup'}
                                                    </Badge>
                                                    <span className="text-xs text-gray-500 dark:text-gray-400">
                                                        {new Date(complaint.created_at).toLocaleDateString('id-ID')}
                                                    </span>
                                                </div>
                                            </div>
                                            <div className="ml-3">
                                                {complaint.priority === 'urgent' ? (
                                                    <AlertTriangle className="h-4 w-4 text-red-600" />
                                                ) : complaint.status === 'pending' ? (
                                                    <Clock className="h-4 w-4 text-yellow-600" />
                                                ) : (
                                                    <CheckCircle className="h-4 w-4 text-green-600" />
                                                )}
                                            </div>
                                        </div>
                                    ))
                                ) : (
                                    <p className="py-8 text-center text-sm text-gray-500 dark:text-gray-400">Belum ada pengaduan</p>
                                )}
                            </div>
                        </CardContent>
                    </Card>
                </div>

                {/* Quick Actions */}
                <Card className="dark:bg-gray-800">
                    <CardHeader>
                        <CardTitle className="flex items-center space-x-2">
                            <Settings className="h-5 w-5 text-green-600" />
                            <span>Aksi Cepat</span>
                        </CardTitle>
                    </CardHeader>
                    <CardContent>
                        <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-4">
                            {quick_actions.map((action, index) => {
                                const IconComponent = iconMap[action.icon as keyof typeof iconMap];
                                const colorClasses = {
                                    blue: 'bg-blue-50 hover:bg-blue-100 text-blue-600 border-blue-200 dark:bg-blue-900 dark:hover:bg-blue-800 dark:text-blue-300 dark:border-blue-800',
                                    green: 'bg-green-50 hover:bg-green-100 text-green-600 border-green-200 dark:bg-green-900 dark:hover:bg-green-800 dark:text-green-300 dark:border-green-800',
                                    purple: 'bg-purple-50 hover:bg-purple-100 text-purple-600 border-purple-200 dark:bg-purple-900 dark:hover:bg-purple-800 dark:text-purple-300 dark:border-purple-800',
                                    orange: 'bg-orange-50 hover:bg-orange-100 text-orange-600 border-orange-200 dark:bg-orange-900 dark:hover:bg-orange-800 dark:text-orange-300 dark:border-orange-800',
                                    red: 'bg-red-50 hover:bg-red-100 text-red-600 border-red-200 dark:bg-red-900 dark:hover:bg-red-800 dark:text-red-300 dark:border-red-800',
                                };

                                return (
                                    <Link
                                        key={index}
                                        href={action.href}
                                        className={`block rounded-lg border p-4 transition-all hover:shadow-md ${
                                            colorClasses[action.color as keyof typeof colorClasses]
                                        }`}
                                    >
                                        <div className="flex items-start space-x-3">
                                            <IconComponent className="h-6 w-6 flex-shrink-0" />
                                            <div className="flex-1">
                                                <h4 className="font-medium text-gray-900 dark:text-white">{action.title}</h4>
                                                <p className="mt-1 text-sm text-gray-600 dark:text-gray-300">{action.description}</p>
                                                <div className="mt-2 flex items-center space-x-1">
                                                    <span className="text-lg font-bold text-gray-900 dark:text-white">{action.count}</span>
                                                    <span className="text-xs text-gray-500 dark:text-gray-400">{action.label}</span>
                                                </div>
                                            </div>
                                        </div>
                                    </Link>
                                );
                            })}
                        </div>
                    </CardContent>
                </Card>
            </div>
        </AdminLayout>
    );
}
