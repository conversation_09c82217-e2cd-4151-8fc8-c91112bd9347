import { Button } from '@/components/ui/button';
import { <PERSON>, <PERSON>Content, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Textarea } from '@/components/ui/textarea';
import AdminLayout from '@/layouts/AdminLayout';
import { type BreadcrumbItem } from '@/types';
import { Head, Link, useForm } from '@inertiajs/react';
import { ArrowLeft, Minus, Plus, Save, Settings } from 'lucide-react';

interface Service {
    id: number;
    name: string;
    description: string;
    requirements: string[];
    procedure: string[];
    cost: string | null;
    processing_time: string;
    contact_info: {
        phone?: string;
        email?: string;
        person?: string;
    } | null;
    is_active: boolean;
}

interface Props {
    service: Service;
}

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'Dashboard',
        href: '/dashboard',
    },
    {
        title: 'Manajemen Layanan',
        href: '/admin/services',
    },
    {
        title: 'Edit Layanan',
        href: '#',
    },
];

export default function EditService({ service }: Props) {
    const { data, setData, put, processing, errors } = useForm({
        name: service.name,
        description: service.description,
        requirements: service.requirements.length > 0 ? service.requirements : [''],
        procedure: service.procedure.length > 0 ? service.procedure : [''],
        cost: service.cost || '',
        processing_time: service.processing_time,
        contact_info: {
            phone: service.contact_info?.phone || '',
            email: service.contact_info?.email || '',
            person: service.contact_info?.person || '',
        },
        is_active: service.is_active as boolean,
    });

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        put(route('admin.services.update', service.id));
    };

    const addRequirement = () => {
        setData('requirements', [...data.requirements, '']);
    };

    const removeRequirement = (index: number) => {
        if (data.requirements.length > 1) {
            const newRequirements = data.requirements.filter((_, i) => i !== index);
            setData('requirements', newRequirements);
        }
    };

    const updateRequirement = (index: number, value: string) => {
        const newRequirements = [...data.requirements];
        newRequirements[index] = value;
        setData('requirements', newRequirements);
    };

    const addProcedure = () => {
        setData('procedure', [...data.procedure, '']);
    };

    const removeProcedure = (index: number) => {
        if (data.procedure.length > 1) {
            const newProcedure = data.procedure.filter((_, i) => i !== index);
            setData('procedure', newProcedure);
        }
    };

    const updateProcedure = (index: number, value: string) => {
        const newProcedure = [...data.procedure];
        newProcedure[index] = value;
        setData('procedure', newProcedure);
    };

    return (
        <AdminLayout breadcrumbs={breadcrumbs}>
            <Head title={`Edit ${service.name} - Admin Desa Lemah Duhur`} />

            <div className="flex h-full flex-1 flex-col gap-6 p-6">
                {/* Header */}
                <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
                    <div>
                        <h1 className="text-3xl font-bold text-gray-900 dark:text-gray-100">Edit Layanan</h1>
                        <p className="text-gray-600 dark:text-gray-300">Perbarui informasi layanan: {service.name}</p>
                    </div>
                    <Link href={route('admin.services.index')}>
                        <Button variant="outline" className="flex items-center space-x-2">
                            <ArrowLeft className="h-4 w-4" />
                            <span>Kembali</span>
                        </Button>
                    </Link>
                </div>

                <form onSubmit={handleSubmit} className="space-y-6">
                    {/* Basic Information */}
                    <Card>
                        <CardHeader>
                            <CardTitle className="flex items-center space-x-2">
                                <Settings className="h-5 w-5" />
                                <span>Informasi Dasar</span>
                            </CardTitle>
                        </CardHeader>
                        <CardContent className="space-y-4">
                            <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                                <div className="space-y-2">
                                    <Label htmlFor="name">Nama Layanan *</Label>
                                    <Input
                                        id="name"
                                        value={data.name}
                                        onChange={(e) => setData('name', e.target.value)}
                                        placeholder="Contoh: Surat Keterangan Domisili"
                                        className={errors.name ? 'border-red-500' : ''}
                                    />
                                    {errors.name && <p className="text-sm text-red-600">{errors.name}</p>}
                                </div>

                                <div className="space-y-2">
                                    <Label htmlFor="processing_time">Waktu Proses *</Label>
                                    <Input
                                        id="processing_time"
                                        value={data.processing_time}
                                        onChange={(e) => setData('processing_time', e.target.value)}
                                        placeholder="Contoh: 1-2 hari kerja"
                                        className={errors.processing_time ? 'border-red-500' : ''}
                                    />
                                    {errors.processing_time && <p className="text-sm text-red-600">{errors.processing_time}</p>}
                                </div>
                            </div>

                            <div className="space-y-2">
                                <Label htmlFor="description">Deskripsi Layanan *</Label>
                                <Textarea
                                    id="description"
                                    value={data.description}
                                    onChange={(e) => setData('description', e.target.value)}
                                    placeholder="Jelaskan layanan ini secara detail..."
                                    rows={4}
                                    className={errors.description ? 'border-red-500' : ''}
                                />
                                {errors.description && <p className="text-sm text-red-600">{errors.description}</p>}
                            </div>

                            <div className="space-y-2">
                                <Label htmlFor="cost">Biaya Layanan</Label>
                                <Input
                                    id="cost"
                                    value={data.cost}
                                    onChange={(e) => setData('cost', e.target.value)}
                                    placeholder="Contoh: Gratis atau Rp 5.000"
                                    className={errors.cost ? 'border-red-500' : ''}
                                />
                                {errors.cost && <p className="text-sm text-red-600">{errors.cost}</p>}
                                <p className="text-sm text-gray-500 dark:text-gray-400">Kosongkan jika gratis</p>
                            </div>
                        </CardContent>
                    </Card>

                    {/* Requirements */}
                    <Card>
                        <CardHeader>
                            <CardTitle>Persyaratan Layanan *</CardTitle>
                        </CardHeader>
                        <CardContent className="space-y-4">
                            {data.requirements.map((requirement, index) => (
                                <div key={index} className="flex items-center space-x-2">
                                    <div className="flex-1">
                                        <Input
                                            value={requirement}
                                            onChange={(e) => updateRequirement(index, e.target.value)}
                                            placeholder={`Persyaratan ${index + 1}`}
                                            className={errors[`requirements.${index}` as keyof typeof errors] ? 'border-red-500' : ''}
                                        />
                                        {errors[`requirements.${index}` as keyof typeof errors] && (
                                            <p className="mt-1 text-sm text-red-600">{errors[`requirements.${index}` as keyof typeof errors]}</p>
                                        )}
                                    </div>
                                    <Button
                                        type="button"
                                        variant="outline"
                                        size="sm"
                                        onClick={() => removeRequirement(index)}
                                        disabled={data.requirements.length === 1}
                                    >
                                        <Minus className="h-4 w-4" />
                                    </Button>
                                </div>
                            ))}
                            <Button type="button" variant="outline" onClick={addRequirement} className="flex items-center space-x-2">
                                <Plus className="h-4 w-4" />
                                <span>Tambah Persyaratan</span>
                            </Button>
                        </CardContent>
                    </Card>

                    {/* Procedure */}
                    <Card>
                        <CardHeader>
                            <CardTitle>Prosedur Layanan *</CardTitle>
                        </CardHeader>
                        <CardContent className="space-y-4">
                            {data.procedure.map((step, index) => (
                                <div key={index} className="flex items-center space-x-2">
                                    <div className="flex-1">
                                        <Input
                                            value={step}
                                            onChange={(e) => updateProcedure(index, e.target.value)}
                                            placeholder={`Langkah ${index + 1}`}
                                            className={errors[`procedure.${index}` as keyof typeof errors] ? 'border-red-500' : ''}
                                        />
                                        {errors[`procedure.${index}` as keyof typeof errors] && (
                                            <p className="mt-1 text-sm text-red-600">{errors[`procedure.${index}` as keyof typeof errors]}</p>
                                        )}
                                    </div>
                                    <Button
                                        type="button"
                                        variant="outline"
                                        size="sm"
                                        onClick={() => removeProcedure(index)}
                                        disabled={data.procedure.length === 1}
                                    >
                                        <Minus className="h-4 w-4" />
                                    </Button>
                                </div>
                            ))}
                            <Button type="button" variant="outline" onClick={addProcedure} className="flex items-center space-x-2">
                                <Plus className="h-4 w-4" />
                                <span>Tambah Langkah</span>
                            </Button>
                        </CardContent>
                    </Card>

                    {/* Contact Information */}
                    <Card>
                        <CardHeader>
                            <CardTitle>Informasi Kontak</CardTitle>
                        </CardHeader>
                        <CardContent className="space-y-4">
                            <div className="grid grid-cols-1 gap-4 md:grid-cols-3">
                                <div className="space-y-2">
                                    <Label htmlFor="contact_phone">Nomor Telepon</Label>
                                    <Input
                                        id="contact_phone"
                                        value={data.contact_info.phone}
                                        onChange={(e) => setData('contact_info', { ...data.contact_info, phone: e.target.value })}
                                        placeholder="Contoh: 0"
                                        className={errors['contact_info.phone'] ? 'border-red-500' : ''}
                                    />
                                    {errors['contact_info.phone'] && <p className="text-sm text-red-600">{errors['contact_info.phone']}</p>}
                                </div>

                                <div className="space-y-2">
                                    <Label htmlFor="contact_email">Email</Label>
                                    <Input
                                        id="contact_email"
                                        type="email"
                                        value={data.contact_info.email}
                                        onChange={(e) => setData('contact_info', { ...data.contact_info, email: e.target.value })}
                                        placeholder="Contoh: <EMAIL>"
                                        className={errors['contact_info.email'] ? 'border-red-500' : ''}
                                    />
                                    {errors['contact_info.email'] && <p className="text-sm text-red-600">{errors['contact_info.email']}</p>}
                                </div>

                                <div className="space-y-2">
                                    <Label htmlFor="contact_person">Penanggung Jawab</Label>
                                    <Input
                                        id="contact_person"
                                        value={data.contact_info.person}
                                        onChange={(e) => setData('contact_info', { ...data.contact_info, person: e.target.value })}
                                        placeholder="Contoh: Bapak Sekretaris Desa"
                                        className={errors['contact_info.person'] ? 'border-red-500' : ''}
                                    />
                                    {errors['contact_info.person'] && <p className="text-sm text-red-600">{errors['contact_info.person']}</p>}
                                </div>
                            </div>
                        </CardContent>
                    </Card>

                    {/* Status */}
                    <Card>
                        <CardHeader>
                            <CardTitle>Status Layanan</CardTitle>
                        </CardHeader>
                        <CardContent>
                            <div className="flex items-center space-x-2">
                                <Switch
                                    id="is_active"
                                    checked={data.is_active}
                                    onCheckedChange={(checked: boolean) => setData('is_active', checked)}
                                />
                                <Label htmlFor="is_active">Aktifkan layanan ini</Label>
                            </div>
                            <p className="mt-2 text-sm text-gray-500 dark:text-gray-400">Layanan yang aktif akan ditampilkan di halaman publik</p>
                        </CardContent>
                    </Card>

                    {/* Submit Button */}
                    <div className="flex justify-end space-x-4">
                        <Link href={route('admin.services.index')}>
                            <Button type="button" variant="outline">
                                Batal
                            </Button>
                        </Link>
                        <Button type="submit" disabled={processing} className="flex items-center space-x-2">
                            <Save className="h-4 w-4" />
                            <span>{processing ? 'Menyimpan...' : 'Perbarui Layanan'}</span>
                        </Button>
                    </div>
                </form>
            </div>
        </AdminLayout>
    );
}
