<?php

namespace Tests\Feature;

use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class AdminAuthTest extends TestCase
{
    use RefreshDatabase;

    public function test_admin_can_access_dashboard(): void
    {
        // Create an admin user
        $admin = User::factory()->create([
            'role' => 'admin',
            'email' => '<EMAIL>',
        ]);

        // Login as admin
        $this->actingAs($admin);

        // Access admin dashboard
        $response = $this->get('/admin/dashboard');

        $response->assertStatus(200);
    }

    public function test_regular_user_cannot_access_admin_dashboard(): void
    {
        // Create a regular user
        $user = User::factory()->create([
            'role' => 'user',
            'email' => '<EMAIL>',
        ]);

        // Login as regular user
        $this->actingAs($user);

        // Try to access admin dashboard
        $response = $this->get('/admin/dashboard');

        $response->assertStatus(403);
    }

    public function test_guest_cannot_access_admin_dashboard(): void
    {
        // Try to access admin dashboard without login
        $response = $this->get('/admin/dashboard');

        $response->assertRedirect('/login');
    }

    public function test_admin_login_redirects_to_admin_dashboard(): void
    {
        // Create an admin user
        $admin = User::factory()->create([
            'role' => 'admin',
            'email' => '<EMAIL>',
        ]);

        // Login as admin
        $response = $this->post('/login', [
            'email' => '<EMAIL>',
            'password' => 'password',
        ]);

        $response->assertRedirect('/admin/dashboard');
    }

    public function test_regular_user_login_redirects_to_regular_dashboard(): void
    {
        // Create a regular user
        $user = User::factory()->create([
            'role' => 'user',
            'email' => '<EMAIL>',
        ]);

        // Login as regular user
        $response = $this->post('/login', [
            'email' => '<EMAIL>',
            'password' => 'password',
        ]);

        $response->assertRedirect('/dashboard');
    }
}
