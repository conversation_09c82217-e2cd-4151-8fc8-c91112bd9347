<?php

use App\Models\News;
use App\Models\Potential;
use App\Models\Profile;
use App\Models\Service;

describe('Homepage Feature', function () {
    beforeEach(function () {
        // Create test data
        News::factory()->published()->count(5)->create();
        Service::factory()->active()->count(6)->create();
        Potential::factory()->featured()->count(4)->create();
        Profile::factory()->history()->create([
            'title' => 'Sambutan Kepala Desa',
            'content' => 'Selamat datang di website Desa Lemah Duhur',
        ]);
    });

    it('displays homepage successfully', function () {
        $response = $this->get('/');

        $response->assertStatus(200);
        $response->assertInertia(fn ($page) => $page->component('Public/Home')
        );
    });

    it('shows latest news on homepage', function () {
        $response = $this->get('/');

        $response->assertInertia(fn ($page) => $page->has('latestNews')
            ->where('latestNews', fn ($news) => count($news) <= 4 && count($news) > 0
            )
        );
    });

    it('shows featured services on homepage', function () {
        $response = $this->get('/');

        $response->assertInertia(fn ($page) => $page->has('featuredServices')
            ->where('featuredServices', fn ($services) => count($services) <= 6 && count($services) > 0
            )
        );
    });

    it('shows featured potentials on homepage', function () {
        $response = $this->get('/');

        $response->assertInertia(fn ($page) => $page->has('featuredPotentials')
            ->where('featuredPotentials', fn ($potentials) => count($potentials) <= 4 && count($potentials) > 0
            )
        );
    });

    it('shows village welcome message', function () {
        $response = $this->get('/');

        $response->assertStatus(200);
        // Test that the page loads successfully - specific data structure may vary
    });

    it('includes SEO meta tags', function () {
        $response = $this->get('/');

        $response->assertStatus(200);
        // SEO meta tags should be included in the response
    });

    it('only shows published news', function () {
        // Create unpublished news
        News::factory()->unpublished()->create();

        $response = $this->get('/');

        $response->assertInertia(fn ($page) => $page->where('latestNews', function ($news) {
            foreach ($news as $article) {
                if (! $article['is_published']) {
                    return false;
                }
            }

            return true;
        })
        );
    });

    it('only shows active services', function () {
        // Create inactive service
        Service::factory()->inactive()->create();

        $response = $this->get('/');

        $response->assertInertia(fn ($page) => $page->where('featuredServices', function ($services) {
            foreach ($services as $service) {
                if (! $service['is_active']) {
                    return false;
                }
            }

            return true;
        })
        );
    });

    it('only shows featured potentials', function () {
        // Create non-featured potential
        Potential::factory()->create(['is_featured' => false]);

        $response = $this->get('/');

        $response->assertInertia(fn ($page) => $page->where('featuredPotentials', function ($potentials) {
            foreach ($potentials as $potential) {
                if (! $potential['is_featured']) {
                    return false;
                }
            }

            return true;
        })
        );
    });

    it('handles empty data gracefully', function () {
        // Clear all data
        News::query()->delete();
        Service::query()->delete();
        Potential::query()->delete();
        Profile::query()->delete();

        $response = $this->get('/');

        $response->assertStatus(200);
        $response->assertInertia(fn ($page) => $page->component('Public/Home')
            ->has('latestNews')
            ->has('featuredServices')
            ->has('featuredPotentials')
        );
    });

    it('includes contact information', function () {
        $response = $this->get('/');

        $response->assertInertia(fn ($page) => $page->has('contactInfo')
        );
    });

    it('includes village statistics', function () {
        $response = $this->get('/');

        $response->assertInertia(fn ($page) => $page->has('villageStats')
        );
    });

    describe('Performance', function () {
        it('loads within acceptable time', function () {
            $start = microtime(true);

            $response = $this->get('/');

            $end = microtime(true);
            $loadTime = $end - $start;

            $response->assertStatus(200);
            expect($loadTime)->toBeLessThan(2.0); // Should load within 2 seconds
        });

        it('uses efficient database queries', function () {
            \DB::enableQueryLog();

            $this->get('/');

            $queries = \DB::getQueryLog();

            // Should not have excessive queries (N+1 problem)
            expect(count($queries))->toBeLessThan(10);
        });
    });

    describe('Responsive Design', function () {
        it('serves mobile-friendly content', function () {
            $response = $this->get('/', [
                'User-Agent' => 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X) AppleWebKit/605.1.15',
            ]);

            $response->assertStatus(200);
        });
    });
});
