import { Button } from '@/components/ui/button';
import { useAppearance } from '@/hooks/use-appearance';
import { Moon, Sun } from 'lucide-react';

export function DarkModeToggle() {
    const { appearance, updateAppearance } = useAppearance();

    const toggleTheme = () => {
        if (appearance === 'light') {
            updateAppearance('dark');
        } else {
            updateAppearance('light');
        }
    };

    const getCurrentIcon = () => {
        switch (appearance) {
            case 'dark':
                return <Moon className="h-4 w-4" />;
            default:
                return <Sun className="h-4 w-4" />;
        }
    };

    const getCurrentLabel = () => {
        switch (appearance) {
            case 'dark':
                return 'Mode Gelap';
            default:
                return 'Mode Terang';
        }
    };

    return (
        <Button
            variant="ghost"
            size="sm"
            onClick={toggleTheme}
            className="h-8 gap-2 text-xs text-muted-foreground hover:text-foreground"
            aria-label={`<PERSON>ralih ke mode tema berikutnya. Saat ini: ${getCurrentLabel()}`}
        >
            {getCurrentIcon()}
            <span className="hidden sm:inline">{getCurrentLabel()}</span>
        </Button>
    );
}
