<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Service;
use Illuminate\Http\Request;
use Inertia\Inertia;

class ServiceController extends Controller
{
    /**
     * Display a listing of services for admin management
     */
    public function index(Request $request)
    {
        $query = Service::query()->latest('created_at');

        // Apply status filter
        if ($request->filled('status')) {
            if ($request->status === 'active') {
                $query->where('is_active', true);
            } elseif ($request->status === 'inactive') {
                $query->where('is_active', false);
            }
        }

        // Apply search filter
        if ($request->filled('search')) {
            $searchTerm = $request->search;
            $query->where(function ($q) use ($searchTerm) {
                $q->where('name', 'like', "%{$searchTerm}%")
                    ->orWhere('description', 'like', "%{$searchTerm}%");
            });
        }

        // Get paginated results
        $services = $query->select([
            'id', 'name', 'description', 'cost', 'processing_time',
            'is_active', 'created_at', 'updated_at',
        ])->paginate(10);

        return Inertia::render('Admin/Services/Index', [
            'services' => $services,
            'filters' => [
                'status' => $request->status,
                'search' => $request->search,
            ],
        ]);
    }

    /**
     * Show the form for creating a new service
     */
    public function create()
    {
        return Inertia::render('Admin/Services/Create');
    }

    /**
     * Store a newly created service
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255|unique:services,name',
            'description' => 'required|string',
            'requirements' => 'required|array|min:1',
            'requirements.*' => 'required|string|max:500',
            'procedure' => 'required|array|min:1',
            'procedure.*' => 'required|string|max:500',
            'cost' => 'nullable|numeric|max:999999999',
            'processing_time' => 'required|string|max:100',
            'contact_info' => 'nullable|array',
            'contact_info.phone' => 'nullable|string|max:20',
            'contact_info.email' => 'nullable|email|max:100',
            'contact_info.person' => 'nullable|string|max:100',
            'is_active' => 'sometimes|boolean',
        ]);

        // Clean up empty requirements and procedures
        $validated['requirements'] = array_filter($validated['requirements'], function ($item) {
            return ! in_array(trim($item), ['', '0'], true);
        });

        $validated['procedure'] = array_filter($validated['procedure'], function ($item) {
            return ! in_array(trim($item), ['', '0'], true);
        });

        // Clean up contact_info - remove empty values
        if (isset($validated['contact_info'])) {
            $validated['contact_info'] = array_filter($validated['contact_info'], function ($value) {
                return ! in_array(trim($value), ['', '0'], true);
            });

            if (empty($validated['contact_info'])) {
                $validated['contact_info'] = null;
            }
        }

        // Convert cost to string if it's numeric
        if (isset($validated['cost']) && is_numeric($validated['cost'])) {
            $validated['cost'] = (string) $validated['cost'];
        }

        Service::create($validated);

        return redirect()->route('admin.services.index')
            ->with('success', 'Layanan berhasil dibuat.');
    }

    /**
     * Display the specified service for editing
     */
    public function show(Service $service)
    {
        return Inertia::render('Admin/Services/Show', [
            'service' => $service,
        ]);
    }

    /**
     * Show the form for editing the specified service
     */
    public function edit(Service $service)
    {
        return Inertia::render('Admin/Services/Edit', [
            'service' => $service,
        ]);
    }

    /**
     * Update the specified service
     */
    public function update(Request $request, Service $service)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255|unique:services,name,'.$service->id,
            'description' => 'required|string',
            'requirements' => 'required|array|min:1',
            'requirements.*' => 'required|string|max:500',
            'procedure' => 'required|array|min:1',
            'procedure.*' => 'required|string|max:500',
            'cost' => 'nullable|numeric|max:999999999',
            'processing_time' => 'required|string|max:100',
            'contact_info' => 'nullable|array',
            'contact_info.phone' => 'nullable|string|max:20',
            'contact_info.email' => 'nullable|email|max:100',
            'contact_info.person' => 'nullable|string|max:100',
            'is_active' => 'boolean',
        ]);

        // Clean up empty requirements and procedures
        $validated['requirements'] = array_filter($validated['requirements'], function ($item) {
            return ! in_array(trim($item), ['', '0'], true);
        });

        $validated['procedure'] = array_filter($validated['procedure'], function ($item) {
            return ! in_array(trim($item), ['', '0'], true);
        });

        // Clean up contact_info - remove empty values
        if (isset($validated['contact_info'])) {
            $validated['contact_info'] = array_filter($validated['contact_info'], function ($value) {
                return ! in_array(trim($value), ['', '0'], true);
            });

            if (empty($validated['contact_info'])) {
                $validated['contact_info'] = null;
            }
        }

        // Convert cost to string if it's numeric
        if (isset($validated['cost']) && is_numeric($validated['cost'])) {
            $validated['cost'] = (string) $validated['cost'];
        }

        $service->update($validated);

        return redirect()->route('admin.services.index')
            ->with('success', 'Layanan berhasil diperbarui.');
    }

    /**
     * Remove the specified service
     */
    public function destroy(Service $service)
    {
        $service->delete();

        return redirect()->route('admin.services.index')
            ->with('success', 'Layanan berhasil dihapus.');
    }

    /**
     * Toggle active status of service
     */
    public function toggleStatus(Service $service)
    {
        $service->update([
            'is_active' => ! $service->is_active,
        ]);

        $status = $service->is_active ? 'diaktifkan' : 'dinonaktifkan';

        return redirect()->back()
            ->with('success', "Layanan berhasil {$status}.");
    }

    /**
     * Activate a service
     */
    public function activate(Service $service)
    {
        $service->update(['is_active' => true]);

        return redirect()->back()
            ->with('success', 'Layanan berhasil diaktifkan.');
    }

    /**
     * Deactivate a service
     */
    public function deactivate(Service $service)
    {
        $service->update(['is_active' => false]);

        return redirect()->back()
            ->with('success', 'Layanan berhasil dinonaktifkan.');
    }

    /**
     * Bulk activate services
     */
    public function bulkActivate(Request $request)
    {
        $validated = $request->validate([
            'service_ids' => 'required|array',
            'service_ids.*' => 'exists:services,id',
        ]);

        Service::whereIn('id', $validated['service_ids'])
            ->update(['is_active' => true]);

        return redirect()->back()
            ->with('success', 'Layanan berhasil diaktifkan.');
    }

    /**
     * Bulk deactivate services
     */
    public function bulkDeactivate(Request $request)
    {
        $validated = $request->validate([
            'service_ids' => 'required|array',
            'service_ids.*' => 'exists:services,id',
        ]);

        Service::whereIn('id', $validated['service_ids'])
            ->update(['is_active' => false]);

        return redirect()->back()
            ->with('success', 'Layanan berhasil dinonaktifkan.');
    }

    /**
     * Bulk delete services
     */
    public function bulkDelete(Request $request)
    {
        $validated = $request->validate([
            'service_ids' => 'required|array',
            'service_ids.*' => 'exists:services,id',
        ]);

        Service::whereIn('id', $validated['service_ids'])->delete();

        return redirect()->back()
            ->with('success', 'Layanan berhasil dihapus.');
    }
}
