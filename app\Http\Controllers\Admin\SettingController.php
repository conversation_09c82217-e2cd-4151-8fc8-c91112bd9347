<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\Requests\Settings\UpdateFeatureFlagsRequest;
use App\Models\Setting;
use App\Services\FeatureFlagService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\RedirectResponse;
use Illuminate\Support\Facades\Log;
use Inertia\Inertia;
use Inertia\Response;

class SettingController extends Controller
{
    /**
     * Feature flag service instance
     */
    protected FeatureFlagService $featureFlagService;

    public function __construct(FeatureFlagService $featureFlagService)
    {
        $this->featureFlagService = $featureFlagService;
    }

    /**
     * Display the admin settings page.
     */
    public function index(): Response
    {
        // Get all current settings organized by category
        $settings = [
            'village.contact_info' => Setting::get('village.contact_info', [
                'phone' => '',
                'whatsapp' => '',
                'email' => '',
                'address' => '',
                'postal_code' => '',
                'maps_link' => '',
            ]),
            'village.profile' => Setting::get('village.profile', [
                'name' => '',
                'district' => '',
                'regency' => '',
                'province' => '',
                'established_year' => '',
                'area' => '',
                'population' => '',
            ]),
            'village.operating_hours' => Setting::get('village.operating_hours', [
                'weekdays' => '',
                'saturday' => '',
                'sunday' => '',
                'break' => '',
                'holidays' => '',
            ]),
            'village.emergency_contacts' => Setting::get('village.emergency_contacts', [
                'village_head' => ['title' => '', 'phone' => ''],
                'village_secretary' => ['title' => '', 'phone' => ''],
                'security' => ['title' => '', 'phone' => ''],
                'health_center' => ['title' => '', 'phone' => ''],
            ]),
            'village.service_settings' => Setting::get('village.service_settings', [
                'online_services_available' => false,
                'mobile_optimized' => false,
                'emergency_contact_available' => false,
                'multilingual_support' => false,
                'digital_signature_enabled' => false,
            ]),
        ];

        // Get feature flags data
        $featureFlags = $this->featureFlagService->getFlagsByCategory();
        $featureFlagsSummary = $this->featureFlagService->getSummary();

        return Inertia::render('Admin/Settings/Index', [
            'settings' => $settings,
            'featureFlags' => $featureFlags,
            'featureFlagsSummary' => $featureFlagsSummary,
        ]);
    }

    /**
     * Update the settings.
     */
    public function update(\App\Http\Requests\Settings\UpdateSettingsRequest $request): RedirectResponse
    {
        $validated = $request->validated();

        // Update each setting category
        foreach ($validated['village'] as $category => $data) {
            Setting::set("village.{$category}", $data);
        }

        return redirect()->back()->with('message', 'Pengaturan diperbarui');
    }

    /**
     * Get all feature flags with metadata
     */
    public function getFeatureFlags(): JsonResponse
    {
        try {
            $flags = $this->featureFlagService->getAllFlags();
            $flagsByCategory = $this->featureFlagService->getFlagsByCategory();
            $summary = $this->featureFlagService->getSummary();

            return response()->json([
                'success' => true,
                'data' => [
                    'flags' => $flags,
                    'flagsByCategory' => $flagsByCategory,
                    'summary' => $summary,
                ],
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to retrieve feature flags: '.$e->getMessage());

            return response()->json([
                'success' => false,
                'message' => 'Gagal mengambil data feature flags',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Update feature flags in bulk
     */
    public function updateFeatureFlags(UpdateFeatureFlagsRequest $request): RedirectResponse
    {
        try {
            $validated = $request->validated();
            $flagUpdates = $validated['feature_flags'];

            // Update multiple flags at once
            $results = $this->featureFlagService->updateMultipleFlags($flagUpdates);

            // Check if all updates were successful
            $failedUpdates = array_filter($results, fn ($success) => ! $success);

            if (! empty($failedUpdates)) {
                Log::warning('Some feature flag updates failed', [
                    'failed_flags' => array_keys($failedUpdates),
                    'all_results' => $results,
                ]);

                return redirect()->back()->with('error', 'Beberapa feature flag gagal diperbarui');
            }

            Log::info('Feature flags updated successfully', [
                'updated_flags' => array_keys($flagUpdates),
                'user_id' => $request->user()->id,
            ]);

            return redirect()->back()->with('success', 'Feature flags berhasil diperbarui');
        } catch (\Exception $e) {
            Log::error('Failed to update feature flags: '.$e->getMessage(), [
                'request_data' => $request->validated(),
                'user_id' => $request->user()->id,
            ]);

            return redirect()->back()->with('error', 'Gagal memperbarui feature flags');
        }
    }

    /**
     * Update a single feature flag
     */
    public function updateSingleFeatureFlag(string $flagKey, UpdateFeatureFlagsRequest $request): RedirectResponse
    {
        try {
            $validated = $request->validated();

            // Extract the single flag value from the request
            if (! isset($validated['feature_flags'][$flagKey])) {
                return redirect()->back()->with('error', 'Flag tidak ditemukan dalam request');
            }

            $enabled = $validated['feature_flags'][$flagKey];
            $success = $this->featureFlagService->updateFlag($flagKey, $enabled);

            if (! $success) {
                return redirect()->back()->with('error', 'Gagal memperbarui feature flag');
            }

            Log::info('Single feature flag updated', [
                'flag' => $flagKey,
                'enabled' => $enabled,
                'user_id' => $request->user()->id,
            ]);

            return redirect()->back()->with('success', 'Feature flag berhasil diperbarui');
        } catch (\Exception $e) {
            Log::error("Failed to update single feature flag '{$flagKey}': ".$e->getMessage(), [
                'flag_key' => $flagKey,
                'request_data' => $request->validated(),
                'user_id' => $request->user()->id,
            ]);

            return redirect()->back()->with('error', 'Gagal memperbarui feature flag');
        }
    }

    /**
     * Reset a feature flag to its default value
     */
    public function resetFeatureFlag(string $flagKey): RedirectResponse
    {
        try {
            $success = $this->featureFlagService->resetFlag($flagKey);

            if (! $success) {
                return redirect()->back()->with('error', 'Gagal mereset feature flag ke nilai default');
            }

            Log::info('Feature flag reset to default', [
                'flag' => $flagKey,
                'user_id' => auth()->id(),
            ]);

            return redirect()->back()->with('success', 'Feature flag berhasil direset ke nilai default');
        } catch (\Exception $e) {
            Log::error("Failed to reset feature flag '{$flagKey}': ".$e->getMessage(), [
                'flag_key' => $flagKey,
                'user_id' => auth()->id(),
            ]);

            return redirect()->back()->with('error', 'Gagal mereset feature flag');
        }
    }

    /**
     * Reset all feature flags to their default values
     */
    public function resetAllFeatureFlags(): RedirectResponse
    {
        try {
            $results = $this->featureFlagService->resetAllFlags();

            // Check if all resets were successful
            $failedResets = array_filter($results, fn ($success) => ! $success);

            if (! empty($failedResets)) {
                Log::warning('Some feature flag resets failed', [
                    'failed_flags' => array_keys($failedResets),
                    'all_results' => $results,
                ]);

                return redirect()->back()->with('error', 'Beberapa feature flag gagal direset');
            }

            Log::info('All feature flags reset to defaults', [
                'user_id' => auth()->id(),
            ]);

            return redirect()->back()->with('success', 'Semua feature flags berhasil direset ke nilai default');
        } catch (\Exception $e) {
            Log::error('Failed to reset all feature flags: '.$e->getMessage(), [
                'user_id' => auth()->id(),
            ]);

            return redirect()->back()->with('error', 'Gagal mereset semua feature flags');
        }
    }
}
