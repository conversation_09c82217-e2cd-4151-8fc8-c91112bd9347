<?php

namespace App\Observers;

use App\Jobs\SendComplaintNotification;
use App\Jobs\SendComplaintStatusUpdate;
use App\Models\Complaint;
use Illuminate\Support\Facades\Log;

class ComplaintObserver
{
    public function created(Complaint $complaint): void
    {
        // Send notification to admin when new complaint is created
        try {
            SendComplaintNotification::dispatch($complaint);

            Log::info('Complaint notification job dispatched', [
                'ticket_number' => $complaint->ticket_number,
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to dispatch complaint notification job', [
                'ticket_number' => $complaint->ticket_number,
                'error' => $e->getMessage(),
            ]);
        }
    }

    private static $previousStatuses = [];

    public function updating(Complaint $complaint): void
    {
        // Store the original status before update
        if ($complaint->isDirty('status')) {
            self::$previousStatuses[$complaint->id] = $complaint->getOriginal('status');
        }
    }

    public function updated(Complaint $complaint): void
    {
        // Send notification when status changes
        if ($complaint->wasChanged('status') && isset(self::$previousStatuses[$complaint->id])) {
            $previousStatus = self::$previousStatuses[$complaint->id];

            try {
                SendComplaintStatusUpdate::dispatch($complaint, $previousStatus);

                Log::info('Complaint status update job dispatched', [
                    'ticket_number' => $complaint->ticket_number,
                    'previous_status' => $previousStatus,
                    'new_status' => $complaint->status,
                ]);
            } catch (\Exception $e) {
                Log::error('Failed to dispatch complaint status update job', [
                    'ticket_number' => $complaint->ticket_number,
                    'error' => $e->getMessage(),
                ]);
            }

            // Clean up the stored status
            unset(self::$previousStatuses[$complaint->id]);
        }

        // Update responded_at timestamp when admin_response is added/updated
        if ($complaint->wasChanged('admin_response') && $complaint->admin_response && ! $complaint->responded_at) {
            $complaint->responded_at = now();
            $complaint->saveQuietly(); // Save without triggering observers again
        }
    }
}
