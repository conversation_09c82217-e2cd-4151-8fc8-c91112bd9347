export interface FeatureFlag {
    key: string;
    title: string;
    description: string;
    category: 'ui' | 'services' | 'experimental' | 'performance';
    enabled: boolean;
    default: boolean;
    warning?: string;
    experimental?: boolean;
    dependencies?: string[];
}

export interface FeatureFlagCategory {
    name: string;
    title: string;
    description: string;
    flags: FeatureFlag[];
    enabledCount: number;
    totalCount: number;
}

export interface FeatureFlagSectionProps {
    flags: FeatureFlag[];
    onToggle: (key: string, enabled: boolean) => void;
    processing: boolean;
}

export interface FeatureFlagToggleProps {
    flag: FeatureFlag;
    enabled: boolean;
    onToggle: (enabled: boolean) => void;
    disabled?: boolean;
}

export interface FeatureFlagCategoryProps {
    category: FeatureFlagCategory;
    onToggle: (key: string, enabled: boolean) => void;
    processing: boolean;
}
// Type for feature flags object passed from backend
export interface FeatureFlags {
    [key: string]: boolean;
}

// Type for Inertia page props that include feature flags
export interface PagePropsWithFeatureFlags {
    featureFlags: FeatureFlags;
}
