<?php

namespace App\Console\Commands;

use App\Models\News;
use App\Models\Potential;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\File;

class SitemapGenerateCommand extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'sitemap:generate';

    /**
     * The console command description.
     */
    protected $description = 'Generate XML sitemap for SEO optimization';

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $this->info('🗺️  Generating XML sitemap...');

        $sitemap = $this->generateSitemap();

        // Save sitemap to public directory
        $sitemapPath = public_path('sitemap.xml');
        File::put($sitemapPath, $sitemap);

        $this->info("✅ Sitemap generated successfully: {$sitemapPath}");

        return Command::SUCCESS;
    }

    /**
     * Generate sitemap XML content
     */
    private function generateSitemap(): string
    {
        $baseUrl = config('app.url');
        $now = now()->toISOString();

        $xml = '<?xml version="1.0" encoding="UTF-8"?>'."\n";
        $xml .= '<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">'."\n";

        // Static pages
        $staticPages = [
            ['url' => '/', 'priority' => '1.0', 'changefreq' => 'daily'],
            ['url' => '/profil', 'priority' => '0.9', 'changefreq' => 'weekly'],
            ['url' => '/layanan', 'priority' => '0.8', 'changefreq' => 'weekly'],
            ['url' => '/berita', 'priority' => '0.8', 'changefreq' => 'daily'],
            ['url' => '/potensi', 'priority' => '0.7', 'changefreq' => 'weekly'],
        ];

        foreach ($staticPages as $page) {
            $xml .= $this->generateUrlEntry(
                $baseUrl.$page['url'],
                $now,
                $page['changefreq'],
                $page['priority']
            );
        }

        // News articles
        $news = News::where('is_published', true)
            ->orderBy('published_at', 'desc')
            ->get(['slug', 'published_at', 'updated_at']);

        foreach ($news as $article) {
            $lastmod = $article->updated_at > $article->published_at
                ? $article->updated_at->toISOString()
                : $article->published_at->toISOString();

            $xml .= $this->generateUrlEntry(
                $baseUrl.'/berita/'.$article->slug,
                $lastmod,
                'monthly',
                '0.6'
            );
        }

        // Tourism potentials
        $tourismPotentials = Potential::where('type', 'tourism')
            ->get(['id', 'name', 'updated_at']);

        foreach ($tourismPotentials as $potential) {
            $xml .= $this->generateUrlEntry(
                $baseUrl.'/potensi/wisata/'.$potential->id,
                $potential->updated_at->toISOString(),
                'monthly',
                '0.5'
            );
        }

        // UMKM potentials
        $umkmPotentials = Potential::where('type', 'umkm')
            ->get(['id', 'name', 'updated_at']);

        foreach ($umkmPotentials as $potential) {
            $xml .= $this->generateUrlEntry(
                $baseUrl.'/potensi/umkm/'.$potential->id,
                $potential->updated_at->toISOString(),
                'monthly',
                '0.5'
            );
        }

        $xml .= '</urlset>';

        return $xml;
    }

    /**
     * Generate individual URL entry for sitemap
     */
    private function generateUrlEntry(string $url, string $lastmod, string $changefreq, string $priority): string
    {
        return "  <url>\n".
               "    <loc>{$url}</loc>\n".
               "    <lastmod>{$lastmod}</lastmod>\n".
               "    <changefreq>{$changefreq}</changefreq>\n".
               "    <priority>{$priority}</priority>\n".
               "  </url>\n";
    }
}
