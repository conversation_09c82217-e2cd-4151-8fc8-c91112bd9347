import type { SettingKey, SettingValue, VillageSettings } from '@/types/settings';
import { usePage } from '@inertiajs/react';
import { useMemo } from 'react';

interface PageProps {
    settings?: VillageSettings;
    [key: string]: unknown;
}

/**
 * React hook for accessing village settings in components
 * This hook provides a reactive way to access settings with proper TypeScript support
 *
 * @returns Object with settings access methods and current settings data
 */
export function useSettings() {
    const { props } = usePage<PageProps>();

    // Memoize settings to prevent unnecessary re-renders
    const settings = useMemo(() => {
        return props.settings;
    }, [props.settings]);

    /**
     * Get a specific setting by key
     *
     * @param key - The setting key to retrieve
     * @param defaultValue - Default value if setting is not found
     * @returns The setting value or default value
     */
    const getSetting = <K extends SettingKey>(key: K, defaultValue?: SettingValue<K>): SettingValue<K> | undefined => {
        try {
            if (settings && settings[key]) {
                return settings[key];
            }
            return defaultValue;
        } catch (error) {
            console.warn(`Failed to get setting '${key}':`, error);
            return defaultValue;
        }
    };

    /**
     * Get a nested property from a setting using dot notation
     *
     * @param key - The setting key
     * @param path - Dot notation path (e.g., 'phone', 'etymology.lemah')
     * @param defaultValue - Default value if property is not found
     * @returns The nested property value or default value
     */
    const getSettingProperty = <K extends SettingKey>(key: K, path: string, defaultValue?: unknown): unknown => {
        try {
            const setting = getSetting(key);

            if (!setting) {
                return defaultValue;
            }

            const pathParts = path.split('.');
            let current: unknown = setting;

            for (const part of pathParts) {
                if (current && typeof current === 'object' && current !== null && part in current) {
                    current = (current as Record<string, unknown>)[part];
                } else {
                    return defaultValue;
                }
            }

            return current;
        } catch (error) {
            console.warn(`Failed to get setting property '${key}.${path}':`, error);
            return defaultValue;
        }
    };

    /**
     * Check if a setting exists and has a value
     *
     * @param key - The setting key to check
     * @returns True if setting exists and has a truthy value
     */
    const hasSetting = <K extends SettingKey>(key: K): boolean => {
        try {
            const setting = getSetting(key);
            return setting !== undefined && setting !== null;
        } catch (error) {
            console.warn(`Failed to check setting existence '${key}':`, error);
            return false;
        }
    };

    /**
     * Get contact information with fallback values
     *
     * @returns Contact information object
     */
    const getContactInfo = () => {
        return getSetting('village.contact_info', {
            phone: '',
            whatsapp: '',
            email: '',
            address: '',
            postal_code: '',
            maps_link: '',
        });
    };

    /**
     * Get village profile information with fallback values
     *
     * @returns Village profile object
     */
    const getVillageProfile = () => {
        return getSetting('village.profile', {
            name: 'Desa Lemah Duhur',
            district: 'Kecamatan Caringin',
            regency: 'Kabupaten Bogor',
            province: 'Jawa Barat',
            established_year: '1910-1920',
            area: '',
            population: '',
        });
    };

    /**
     * Get operating hours with fallback values
     *
     * @returns Operating hours object
     */
    const getOperatingHours = () => {
        return getSetting('village.operating_hours', {
            weekdays: 'Senin - Jumat: 08:00 - 15:00 WIB',
            saturday: 'Sabtu: 08:00 - 12:00 WIB',
            sunday: 'Minggu: Tutup',
            break: 'Istirahat: 12:00 - 13:00 WIB',
            holidays: 'Hari libur nasional: Tutup',
        });
    };

    /**
     * Get emergency contacts with fallback values
     *
     * @returns Emergency contacts object
     */
    const getEmergencyContacts = () => {
        return getSetting('village.emergency_contacts', {
            village_head: { title: 'Kepala Desa', phone: '' },
            village_secretary: { title: 'Sekretaris Desa', phone: '' },
            security: { title: 'Keamanan Desa (Hansip)', phone: '' },
            health_center: { title: 'Puskesmas Caringin', phone: '' },
        });
    };

    /**
     * Get village history information
     *
     * @returns Village history object or undefined if not available
     */
    const getVillageHistory = () => {
        return getSetting('village.history');
    };

    /**
     * Get service settings with fallback values
     *
     * @returns Service settings object
     */
    const getServiceSettings = () => {
        return getSetting('village.service_settings', {
            online_services_available: true,
            mobile_optimized: true,
            emergency_contact_available: true,
            multilingual_support: false,
            digital_signature_enabled: false,
        });
    };

    return {
        // Raw settings data
        settings,

        // Generic access methods
        getSetting,
        getSettingProperty,
        hasSetting,

        // Convenience methods for common settings
        getContactInfo,
        getVillageProfile,
        getOperatingHours,
        getEmergencyContacts,
        getVillageHistory,
        getServiceSettings,

        // Utility properties
        isAvailable: settings !== undefined && settings !== null,
        isEmpty: !settings || Object.keys(settings).length === 0,
    };
}
