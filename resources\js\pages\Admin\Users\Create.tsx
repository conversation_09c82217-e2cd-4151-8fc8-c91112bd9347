import { Button } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import AdminLayout from '@/layouts/AdminLayout';
import { type BreadcrumbItem } from '@/types';
import { Head, Link, useForm } from '@inertiajs/react';
import { ArrowLeft, Eye, EyeOff, Save, UserPlus } from 'lucide-react';
import { FormEventHandler, useState } from 'react';

interface Props {
    roles: Record<string, string>;
}

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'Dashboard',
        href: '/dashboard',
    },
    {
        title: 'Manajemen Pengguna',
        href: '/admin/users',
    },
    {
        title: 'Tambah Pengguna',
        href: '/admin/users/create',
    },
];

export default function UserCreate({ roles }: Props) {
    const [showPassword, setShowPassword] = useState(false);
    const [showPasswordConfirmation, setShowPasswordConfirmation] = useState(false);

    const { data, setData, post, processing, errors } = useForm({
        name: '',
        email: '',
        password: '',
        password_confirmation: '',
        role: '',
    });

    const submit: FormEventHandler = (e) => {
        e.preventDefault();
        post(route('admin.users.store'));
    };

    const togglePasswordVisibility = () => {
        setShowPassword(!showPassword);
    };

    const togglePasswordConfirmationVisibility = () => {
        setShowPasswordConfirmation(!showPasswordConfirmation);
    };

    return (
        <AdminLayout breadcrumbs={breadcrumbs}>
            <Head title="Tambah Pengguna - Admin Desa Lemah Duhur" />

            <div className="flex h-full flex-1 flex-col gap-6 p-6">
                {/* Header */}
                <div className="flex items-center justify-between">
                    <div>
                        <h1 className="text-3xl font-bold text-gray-900 dark:text-gray-100">Tambah Pengguna</h1>
                        <p className="text-gray-600 dark:text-gray-300">Buat akun pengguna baru untuk sistem</p>
                    </div>
                    <Link href={route('admin.users.index')}>
                        <Button variant="outline" className="flex items-center space-x-2">
                            <ArrowLeft className="h-4 w-4" />
                            <span>Kembali</span>
                        </Button>
                    </Link>
                </div>

                <form onSubmit={submit} className="space-y-6">
                    <div className="grid grid-cols-1 gap-6 lg:grid-cols-3">
                        {/* Main Content */}
                        <div className="space-y-6 lg:col-span-2">
                            {/* Basic Information */}
                            <Card>
                                <CardHeader>
                                    <CardTitle className="flex items-center space-x-2">
                                        <UserPlus className="h-5 w-5" />
                                        <span>Informasi Akun</span>
                                    </CardTitle>
                                </CardHeader>
                                <CardContent className="space-y-4">
                                    <div className="space-y-2">
                                        <Label htmlFor="name">Nama Lengkap *</Label>
                                        <Input
                                            id="name"
                                            value={data.name}
                                            onChange={(e) => setData('name', e.target.value)}
                                            placeholder="Masukkan nama lengkap..."
                                            className={errors.name ? 'border-red-500' : ''}
                                            required
                                        />
                                        {errors.name && <p className="text-sm text-red-600">{errors.name}</p>}
                                    </div>

                                    <div className="space-y-2">
                                        <Label htmlFor="email">Alamat Email *</Label>
                                        <Input
                                            id="email"
                                            type="email"
                                            value={data.email}
                                            onChange={(e) => setData('email', e.target.value)}
                                            placeholder="<EMAIL>"
                                            className={errors.email ? 'border-red-500' : ''}
                                            required
                                        />
                                        {errors.email && <p className="text-sm text-red-600">{errors.email}</p>}
                                        <p className="text-xs text-gray-500 dark:text-gray-400">Email akan digunakan untuk login ke sistem</p>
                                    </div>

                                    <div className="space-y-2">
                                        <Label htmlFor="password">Kata Sandi *</Label>
                                        <div className="relative">
                                            <Input
                                                id="password"
                                                type={showPassword ? 'text' : 'password'}
                                                value={data.password}
                                                onChange={(e) => setData('password', e.target.value)}
                                                placeholder="Masukkan kata sandi..."
                                                className={errors.password ? 'border-red-500 pr-10' : 'pr-10'}
                                                required
                                            />
                                            <Button
                                                type="button"
                                                variant="ghost"
                                                size="sm"
                                                className="absolute top-0 right-0 h-full px-3 py-2 hover:bg-transparent"
                                                onClick={togglePasswordVisibility}
                                            >
                                                {showPassword ? (
                                                    <EyeOff className="h-4 w-4 text-gray-400" />
                                                ) : (
                                                    <Eye className="h-4 w-4 text-gray-400" />
                                                )}
                                            </Button>
                                        </div>
                                        {errors.password && <p className="text-sm text-red-600">{errors.password}</p>}
                                        <p className="text-xs text-gray-500 dark:text-gray-400">
                                            Minimal 8 karakter, disarankan menggunakan kombinasi huruf, angka, dan simbol
                                        </p>
                                    </div>

                                    <div className="space-y-2">
                                        <Label htmlFor="password_confirmation">Konfirmasi Kata Sandi *</Label>
                                        <div className="relative">
                                            <Input
                                                id="password_confirmation"
                                                type={showPasswordConfirmation ? 'text' : 'password'}
                                                value={data.password_confirmation}
                                                onChange={(e) => setData('password_confirmation', e.target.value)}
                                                placeholder="Ulangi kata sandi..."
                                                className={errors.password_confirmation ? 'border-red-500 pr-10' : 'pr-10'}
                                                required
                                            />
                                            <Button
                                                type="button"
                                                variant="ghost"
                                                size="sm"
                                                className="absolute top-0 right-0 h-full px-3 py-2 hover:bg-transparent"
                                                onClick={togglePasswordConfirmationVisibility}
                                            >
                                                {showPasswordConfirmation ? (
                                                    <EyeOff className="h-4 w-4 text-gray-400" />
                                                ) : (
                                                    <Eye className="h-4 w-4 text-gray-400" />
                                                )}
                                            </Button>
                                        </div>
                                        {errors.password_confirmation && <p className="text-sm text-red-600">{errors.password_confirmation}</p>}
                                        <p className="text-xs text-gray-500 dark:text-gray-400">
                                            Pastikan konfirmasi kata sandi sama dengan kata sandi di atas
                                        </p>
                                    </div>
                                </CardContent>
                            </Card>
                        </div>

                        {/* Sidebar */}
                        <div className="space-y-6">
                            {/* Role Selection */}
                            <Card>
                                <CardHeader>
                                    <CardTitle>Peran Pengguna</CardTitle>
                                </CardHeader>
                                <CardContent className="space-y-4">
                                    <div className="space-y-2">
                                        <Label>Pilih Peran *</Label>
                                        <Select value={data.role} onValueChange={(value) => setData('role', value)}>
                                            <SelectTrigger className={errors.role ? 'border-red-500' : ''}>
                                                <SelectValue placeholder="Pilih peran pengguna..." />
                                            </SelectTrigger>
                                            <SelectContent>
                                                {Object.entries(roles).map(([key, label]) => (
                                                    <SelectItem key={key} value={key}>
                                                        {label}
                                                    </SelectItem>
                                                ))}
                                            </SelectContent>
                                        </Select>
                                        {errors.role && <p className="text-sm text-red-600">{errors.role}</p>}
                                        <div className="space-y-2 text-xs text-gray-500 dark:text-gray-400">
                                            <p>
                                                <strong>User:</strong> Akses terbatas untuk pengguna umum
                                            </p>
                                            <p>
                                                <strong>Admin:</strong> Akses ke sistem administrasi
                                            </p>
                                            {Object.keys(roles).includes('superadmin') && (
                                                <p>
                                                    <strong>Super Admin:</strong> Akses penuh termasuk mengelola admin lain
                                                </p>
                                            )}
                                        </div>
                                    </div>
                                </CardContent>
                            </Card>

                            {/* Account Information */}
                            <Card>
                                <CardHeader>
                                    <CardTitle>Informasi Akun</CardTitle>
                                </CardHeader>
                                <CardContent className="space-y-3 text-sm text-gray-600 dark:text-gray-300">
                                    <div>
                                        <p className="font-medium">Status Akun:</p>
                                        <p>Akun akan aktif setelah dibuat</p>
                                    </div>
                                    <div>
                                        <p className="font-medium">Verifikasi Email:</p>
                                        <p>Pengguna perlu memverifikasi email mereka</p>
                                    </div>
                                    <div>
                                        <p className="font-medium">Keamanan:</p>
                                        <p>Kata sandi akan dienkripsi secara otomatis</p>
                                    </div>
                                </CardContent>
                            </Card>

                            {/* Submit Button */}
                            <Button type="submit" disabled={processing} className="w-full">
                                <Save className="mr-2 h-4 w-4" />
                                {processing ? 'Membuat Akun...' : 'Buat Akun Pengguna'}
                            </Button>
                        </div>
                    </div>
                </form>
            </div>
        </AdminLayout>
    );
}
