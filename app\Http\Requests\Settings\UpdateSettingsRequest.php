<?php

namespace App\Http\Requests\Settings;

use Illuminate\Foundation\Http\FormRequest;

class UpdateSettingsRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        // Only allow authenticated admin users to update settings
        return $this->user() && $this->user()->isAdmin();
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation(): void
    {
        // Transform flattened form data into nested structure for validation
        $transformedData = [
            'village' => [
                'contact_info' => [
                    'phone' => $this->input('village_contact_info_phone', ''),
                    'whatsapp' => $this->input('village_contact_info_whatsapp', ''),
                    'email' => $this->input('village_contact_info_email', ''),
                    'address' => $this->input('village_contact_info_address', ''),
                    'postal_code' => $this->input('village_contact_info_postal_code', ''),
                    'maps_link' => $this->input('village_contact_info_maps_link', ''),
                ],
                'profile' => [
                    'name' => $this->input('village_profile_name', ''),
                    'district' => $this->input('village_profile_district', ''),
                    'regency' => $this->input('village_profile_regency', ''),
                    'province' => $this->input('village_profile_province', ''),
                    'established_year' => $this->input('village_profile_established_year', ''),
                    'area' => $this->input('village_profile_area', ''),
                    'population' => $this->input('village_profile_population', ''),
                ],
                'operating_hours' => [
                    'weekdays' => $this->input('village_operating_hours_weekdays', ''),
                    'saturday' => $this->input('village_operating_hours_saturday', ''),
                    'sunday' => $this->input('village_operating_hours_sunday', ''),
                    'break' => $this->input('village_operating_hours_break', ''),
                    'holidays' => $this->input('village_operating_hours_holidays', ''),
                ],
                'emergency_contacts' => [
                    'village_head' => [
                        'title' => $this->input('village_emergency_contacts_village_head_title', ''),
                        'phone' => $this->input('village_emergency_contacts_village_head_phone', ''),
                    ],
                    'village_secretary' => [
                        'title' => $this->input('village_emergency_contacts_village_secretary_title', ''),
                        'phone' => $this->input('village_emergency_contacts_village_secretary_phone', ''),
                    ],
                    'security' => [
                        'title' => $this->input('village_emergency_contacts_security_title', ''),
                        'phone' => $this->input('village_emergency_contacts_security_phone', ''),
                    ],
                    'health_center' => [
                        'title' => $this->input('village_emergency_contacts_health_center_title', ''),
                        'phone' => $this->input('village_emergency_contacts_health_center_phone', ''),
                    ],
                ],
            ],
        ];

        $this->merge($transformedData);
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            // Village contact information validation
            'village.contact_info.phone' => [
                'required',
                'string',
                'max:20',
                'regex:/^(\(0\d{2,3}\)\s?\d{4,8}|\+62\d{8,13}|0\d{9,13})$/',
            ],
            'village.contact_info.whatsapp' => [
                'required',
                'string',
                'max:20',
                'regex:/^(\+62|62|0)[8-9]\d{8,12}$/',
            ],
            'village.contact_info.email' => [
                'required',
                'email:rfc,dns',
                'max:255',
            ],
            'village.contact_info.address' => [
                'required',
                'string',
                'max:500',
                'min:10',
            ],
            'village.contact_info.postal_code' => [
                'required',
                'string',
                'regex:/^\d{5}$/',
            ],
            'village.contact_info.maps_link' => [
                'nullable',
                'url',
                'max:500',
            ],

            // Village profile validation
            'village.profile.name' => [
                'required',
                'string',
                'max:100',
                'min:3',
            ],
            'village.profile.district' => [
                'required',
                'string',
                'max:100',
            ],
            'village.profile.regency' => [
                'required',
                'string',
                'max:100',
            ],
            'village.profile.province' => [
                'required',
                'string',
                'max:100',
            ],
            'village.profile.established_year' => [
                'nullable',
                'string',
                'max:50',
            ],
            'village.profile.area' => [
                'nullable',
                'string',
                'max:200',
            ],
            'village.profile.population' => [
                'nullable',
                'string',
                'max:200',
            ],

            // Operating hours validation
            'village.operating_hours.weekdays' => [
                'required',
                'string',
                'max:100',
            ],
            'village.operating_hours.saturday' => [
                'required',
                'string',
                'max:100',
            ],
            'village.operating_hours.sunday' => [
                'required',
                'string',
                'max:100',
            ],
            'village.operating_hours.break' => [
                'nullable',
                'string',
                'max:100',
            ],
            'village.operating_hours.holidays' => [
                'nullable',
                'string',
                'max:100',
            ],

            // Emergency contacts validation
            'village.emergency_contacts.village_head.title' => [
                'required',
                'string',
                'max:100',
            ],
            'village.emergency_contacts.village_head.phone' => [
                'required',
                'string',
                'max:20',
                'regex:/^(\+62|62|0)[8-9]\d{8,12}$/',
            ],
            'village.emergency_contacts.village_secretary.title' => [
                'required',
                'string',
                'max:100',
            ],
            'village.emergency_contacts.village_secretary.phone' => [
                'required',
                'string',
                'max:20',
                'regex:/^(\+62|62|0)[8-9]\d{8,12}$/',
            ],
            'village.emergency_contacts.security.title' => [
                'required',
                'string',
                'max:100',
            ],
            'village.emergency_contacts.security.phone' => [
                'required',
                'string',
                'max:20',
                'regex:/^(\+62|62|0)[8-9]\d{8,12}$/',
            ],
            'village.emergency_contacts.health_center.title' => [
                'required',
                'string',
                'max:100',
            ],
            'village.emergency_contacts.health_center.phone' => [
                'required',
                'string',
                'max:20',
                'regex:/^(\(0\d{2,3}\)\s?\d{4,8}|\+62\d{8,13}|0\d{9,13})$/',
            ],
        ];
    }

    /**
     * Get custom validation error messages in Indonesian.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            // Contact info messages
            'village.contact_info.phone.required' => 'Nomor telepon kantor desa wajib diisi.',
            'village.contact_info.phone.regex' => 'Format nomor telepon tidak valid. Gunakan format Indonesia yang benar.',
            'village.contact_info.phone.max' => 'Nomor telepon tidak boleh lebih dari 20 karakter.',

            'village.contact_info.whatsapp.required' => 'Nomor WhatsApp wajib diisi.',
            'village.contact_info.whatsapp.regex' => 'Format nomor WhatsApp tidak valid. Gunakan format nomor HP Indonesia.',
            'village.contact_info.whatsapp.max' => 'Nomor WhatsApp tidak boleh lebih dari 20 karakter.',

            'village.contact_info.email.required' => 'Alamat email wajib diisi.',
            'village.contact_info.email.email' => 'Format alamat email tidak valid.',
            'village.contact_info.email.max' => 'Alamat email tidak boleh lebih dari 255 karakter.',

            'village.contact_info.address.required' => 'Alamat kantor desa wajib diisi.',
            'village.contact_info.address.min' => 'Alamat harus minimal 10 karakter.',
            'village.contact_info.address.max' => 'Alamat tidak boleh lebih dari 500 karakter.',

            'village.contact_info.postal_code.required' => 'Kode pos wajib diisi.',
            'village.contact_info.postal_code.regex' => 'Kode pos harus terdiri dari 5 digit angka.',

            'village.contact_info.maps_link.url' => 'Link Google Maps harus berupa URL yang valid.',
            'village.contact_info.maps_link.max' => 'Link Google Maps tidak boleh lebih dari 500 karakter.',

            // Profile messages
            'village.profile.name.required' => 'Nama desa wajib diisi.',
            'village.profile.name.min' => 'Nama desa harus minimal 3 karakter.',
            'village.profile.name.max' => 'Nama desa tidak boleh lebih dari 100 karakter.',

            'village.profile.district.required' => 'Nama kecamatan wajib diisi.',
            'village.profile.district.max' => 'Nama kecamatan tidak boleh lebih dari 100 karakter.',

            'village.profile.regency.required' => 'Nama kabupaten wajib diisi.',
            'village.profile.regency.max' => 'Nama kabupaten tidak boleh lebih dari 100 karakter.',

            'village.profile.province.required' => 'Nama provinsi wajib diisi.',
            'village.profile.province.max' => 'Nama provinsi tidak boleh lebih dari 100 karakter.',

            'village.profile.established_year.max' => 'Tahun berdiri tidak boleh lebih dari 50 karakter.',
            'village.profile.area.max' => 'Deskripsi wilayah tidak boleh lebih dari 200 karakter.',
            'village.profile.population.max' => 'Informasi populasi tidak boleh lebih dari 200 karakter.',

            // Operating hours messages
            'village.operating_hours.weekdays.required' => 'Jam operasional hari kerja wajib diisi.',
            'village.operating_hours.weekdays.max' => 'Jam operasional hari kerja tidak boleh lebih dari 100 karakter.',

            'village.operating_hours.saturday.required' => 'Jam operasional hari Sabtu wajib diisi.',
            'village.operating_hours.saturday.max' => 'Jam operasional hari Sabtu tidak boleh lebih dari 100 karakter.',

            'village.operating_hours.sunday.required' => 'Jam operasional hari Minggu wajib diisi.',
            'village.operating_hours.sunday.max' => 'Jam operasional hari Minggu tidak boleh lebih dari 100 karakter.',

            'village.operating_hours.break.max' => 'Jam istirahat tidak boleh lebih dari 100 karakter.',
            'village.operating_hours.holidays.max' => 'Jam operasional hari libur tidak boleh lebih dari 100 karakter.',

            // Emergency contacts messages
            'village.emergency_contacts.village_head.title.required' => 'Jabatan kepala desa wajib diisi.',
            'village.emergency_contacts.village_head.title.max' => 'Jabatan kepala desa tidak boleh lebih dari 100 karakter.',
            'village.emergency_contacts.village_head.phone.required' => 'Nomor telepon kepala desa wajib diisi.',
            'village.emergency_contacts.village_head.phone.regex' => 'Format nomor telepon kepala desa tidak valid.',
            'village.emergency_contacts.village_head.phone.max' => 'Nomor telepon kepala desa tidak boleh lebih dari 20 karakter.',

            'village.emergency_contacts.village_secretary.title.required' => 'Jabatan sekretaris desa wajib diisi.',
            'village.emergency_contacts.village_secretary.title.max' => 'Jabatan sekretaris desa tidak boleh lebih dari 100 karakter.',
            'village.emergency_contacts.village_secretary.phone.required' => 'Nomor telepon sekretaris desa wajib diisi.',
            'village.emergency_contacts.village_secretary.phone.regex' => 'Format nomor telepon sekretaris desa tidak valid.',
            'village.emergency_contacts.village_secretary.phone.max' => 'Nomor telepon sekretaris desa tidak boleh lebih dari 20 karakter.',

            'village.emergency_contacts.security.title.required' => 'Jabatan keamanan desa wajib diisi.',
            'village.emergency_contacts.security.title.max' => 'Jabatan keamanan desa tidak boleh lebih dari 100 karakter.',
            'village.emergency_contacts.security.phone.required' => 'Nomor telepon keamanan desa wajib diisi.',
            'village.emergency_contacts.security.phone.regex' => 'Format nomor telepon keamanan desa tidak valid.',
            'village.emergency_contacts.security.phone.max' => 'Nomor telepon keamanan desa tidak boleh lebih dari 20 karakter.',

            'village.emergency_contacts.health_center.title.required' => 'Nama puskesmas wajib diisi.',
            'village.emergency_contacts.health_center.title.max' => 'Nama puskesmas tidak boleh lebih dari 100 karakter.',
            'village.emergency_contacts.health_center.phone.required' => 'Nomor telepon puskesmas wajib diisi.',
            'village.emergency_contacts.health_center.phone.regex' => 'Format nomor telepon puskesmas tidak valid.',
            'village.emergency_contacts.health_center.phone.max' => 'Nomor telepon puskesmas tidak boleh lebih dari 20 karakter.',
        ];
    }

    /**
     * Get custom attribute names for validation errors.
     *
     * @return array<string, string>
     */
    public function attributes(): array
    {
        return [
            'village.contact_info.phone' => 'nomor telepon',
            'village.contact_info.whatsapp' => 'nomor WhatsApp',
            'village.contact_info.email' => 'alamat email',
            'village.contact_info.address' => 'alamat',
            'village.contact_info.postal_code' => 'kode pos',
            'village.contact_info.maps_link' => 'link Google Maps',

            'village.profile.name' => 'nama desa',
            'village.profile.district' => 'kecamatan',
            'village.profile.regency' => 'kabupaten',
            'village.profile.province' => 'provinsi',
            'village.profile.established_year' => 'tahun berdiri',
            'village.profile.area' => 'wilayah',
            'village.profile.population' => 'populasi',

            'village.operating_hours.weekdays' => 'jam operasional hari kerja',
            'village.operating_hours.saturday' => 'jam operasional Sabtu',
            'village.operating_hours.sunday' => 'jam operasional Minggu',
            'village.operating_hours.break' => 'jam istirahat',
            'village.operating_hours.holidays' => 'jam operasional hari libur',
        ];
    }
}
