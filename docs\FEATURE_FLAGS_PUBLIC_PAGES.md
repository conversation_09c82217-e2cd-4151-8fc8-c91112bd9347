# Feature Flags for Public Pages

This document describes the implementation of feature flags for controlling the visibility of public pages (layanan, berita, potensi, pengaduan) in the Desa Lemah Duhur website.

## Overview

The feature flag system allows administrators to enable/disable public pages dynamically without code deployment. When a page is disabled, users are redirected to the homepage and the navigation menu items are hidden.

This implementation focuses specifically on public page visibility control and only includes the "pages" category for simplicity and focused functionality.

## Implementation Details

### 1. Feature Flag Definitions

The following feature flags control public page visibility:

- `pages.layanan` - Controls access to the services page (/layanan)
- `pages.berita` - Controls access to the news page (/berita)
- `pages.potensi` - Controls access to the potential page (/potensi)
- `pages.pengaduan` - Controls access to the complaints page (/pengaduan)

All flags are enabled by default (`true`). Only the "pages" category is implemented to keep the system focused and simple.

### 2. Backend Implementation

#### Database Seeder
- Updated `FeatureFlagSeeder.php` to include the new page flags
- Added a new "pages" category for organizing public page flags

#### Service Layer
- Updated `FeatureFlagService.php` to only include the "pages" category
- Removed unnecessary categories (ui, services, experimental, performance)
- Category label: "Halaman Publik"
- Category description: "Kontrol visibilitas halaman-halaman publik website desa"

#### Middleware Protection
- Updated `routes/web.php` to protect public routes with feature flag middleware
- Routes are wrapped with `feature:pages.{page_name},and,redirect,/` middleware
- When disabled, users are redirected to the homepage

#### Inertia Props
- Updated `HandleInertiaRequests.php` to share feature flags with frontend
- Feature flags are available in all React components via `featureFlags` prop

### 3. Frontend Implementation

#### React Hook
- Created `useFeatureFlags.ts` hook for accessing feature flags in React components
- Provides methods: `isEnabled()`, `isPageEnabled()`, `areAllEnabled()`, `isAnyEnabled()`, `getAllFlags()`

#### Navigation Updates
- Updated `Header.tsx` component to conditionally show navigation items
- Menu items are filtered based on their corresponding feature flags
- Navigation items without flags (Beranda, Profil Desa) are always shown

#### Homepage Updates
- Updated `Home.tsx` to conditionally show sections based on feature flags
- Hero section "Layanan Publik" button is hidden when layanan page is disabled
- News, Services, and Potentials sections are hidden when their respective pages are disabled

### 4. Admin Interface

The feature flags can be managed through the admin settings page at `/admin/settings`. Only the "Halaman Publik" category is displayed, containing the four public page flags with appropriate warnings about the impact of disabling them.

### 5. Cleanup Process

To keep the system focused and simple, all non-essential feature flag categories have been removed:
- **Removed Categories**: ui, services, experimental, performance
- **Cleanup Process**: Created `CleanupFeatureFlagsSeeder` to remove old flags from database
- **Result**: Only public page visibility flags remain, making the interface cleaner and more focused

## Usage Examples

### Checking Feature Flags in React Components

```typescript
import { useFeatureFlags } from '@/hooks/useFeatureFlags';

function MyComponent() {
    const { isPageEnabled, isEnabled } = useFeatureFlags();
    
    // Check if a specific page is enabled
    if (isPageEnabled('layanan')) {
        // Show layanan-related content
    }
    
    // Check any feature flag
    if (isEnabled('pages.berita')) {
        // Show berita-related content
    }
    
    return (
        <div>
            {isPageEnabled('potensi') && (
                <Link href="/potensi">Lihat Potensi</Link>
            )}
        </div>
    );
}
```

### Checking Feature Flags in PHP

```php
use App\Services\FeatureFlagService;

$featureFlagService = app(FeatureFlagService::class);

// Check if a page is enabled
if ($featureFlagService->isEnabled('pages.layanan')) {
    // Page is accessible
}

// Update a flag
$featureFlagService->updateFlag('pages.berita', false);
```

## Security Considerations

- Feature flags are checked on both server-side (middleware) and client-side (React)
- Server-side middleware provides the primary security layer
- Client-side checks are for UI optimization only
- All flag changes are logged for audit purposes

## Performance Impact

- Feature flags are cached for performance
- Frontend flags are shared via Inertia props to avoid additional API calls
- Minimal performance impact on page load times

## Testing

To test the feature flag functionality:

1. Access the admin settings page (`/admin/settings`)
2. Navigate to the "Feature Flags" section
3. Find the "Halaman Publik" category
4. Toggle any page flag off - the page will automatically refresh to show the change
5. Visit the corresponding public page - you should be redirected to homepage
6. Check the navigation menu - the disabled page should not appear
7. Visit the homepage - sections related to disabled pages should be hidden

## Recent Fixes

### Issue: JSON Response Error
**Problem**: When toggling feature flags, users received an error popup: "All Inertia requests must receive a valid Inertia response, however a plain JSON response was received."

**Solution**: Updated all feature flag controller methods to return Inertia redirect responses instead of JSON responses:
- `updateFeatureFlags()` - Now returns `RedirectResponse` with flash messages
- `updateSingleFeatureFlag()` - Now returns `RedirectResponse` with flash messages  
- `resetFeatureFlag()` - Now returns `RedirectResponse` with flash messages
- `resetAllFeatureFlags()` - Now returns `RedirectResponse` with flash messages

**Result**: Feature flag toggles now work seamlessly without error popups and automatically refresh the page to show changes.

## Files Modified

### Backend
- `database/seeders/FeatureFlagSeeder.php` - Simplified to only include page flags
- `database/seeders/CleanupFeatureFlagsSeeder.php` - New seeder to remove old flags
- `app/Services/FeatureFlagService.php` - Simplified to only include pages category
- `routes/web.php` - Added middleware protection
- `app/Http/Middleware/HandleInertiaRequests.php` - Share flags with frontend

### Frontend
- `resources/js/hooks/useFeatureFlags.ts` - New hook for accessing flags
- `resources/js/components/layout/Header.tsx` - Conditional navigation
- `resources/js/pages/Public/Home.tsx` - Conditional sections
- `resources/js/components/FeatureFlagSection.tsx` - Simplified to only show pages category

## Future Enhancements

- Add more granular controls (e.g., individual service visibility)
- Implement scheduled flag changes
- Add A/B testing capabilities
- Create flag dependency management
- Add flag usage analytics