<?php

namespace App\Providers;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Blade;
use Illuminate\Support\ServiceProvider;
use Laravel\Pennant\Feature;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        Model::automaticallyEagerLoadRelationships();

        // Register feature flag Blade directives
        $this->registerFeatureFlagBladeDirectives();
    }

    /**
     * Register Blade directives for feature flags
     */
    private function registerFeatureFlagBladeDirectives(): void
    {
        // @feature directive - show content if feature is active
        Blade::if('feature', function (string $feature) {
            return Feature::active($feature);
        });

        // @featureInactive directive - show content if feature is inactive
        Blade::if('featureInactive', function (string $feature) {
            return Feature::inactive($feature);
        });

        // @whenFeature directive - conditional rendering with callback
        Blade::directive('whenFeature', function ($expression) {
            return "<?php if(Feature::active({$expression})): ?>";
        });

        Blade::directive('endwhenFeature', function () {
            return '<?php endif; ?>';
        });

        // @unlessFeature directive - conditional rendering when feature is inactive
        Blade::directive('unlessFeature', function ($expression) {
            return "<?php if(Feature::inactive({$expression})): ?>";
        });

        Blade::directive('endunlessFeature', function () {
            return '<?php endif; ?>';
        });
    }
}
