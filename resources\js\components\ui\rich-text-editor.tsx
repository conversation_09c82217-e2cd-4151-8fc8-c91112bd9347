import { cn } from '@/lib/utils';
import React, { useEffect, useRef, useState } from 'react';
import Quill from 'quill';
import 'quill/dist/quill.snow.css';
import '../../../css/rich-text-editor.css';

interface RichTextEditorProps {
    value?: string;
    onChange?: (value: string) => void;
    placeholder?: string;
    className?: string;
    disabled?: boolean;
    error?: boolean;
}

const RichTextEditor = React.forwardRef<HTMLDivElement, RichTextEditorProps>(
    ({ value = '', onChange, placeholder = 'Masukkan konten...', className, disabled = false, error = false }, ref) => {
        const editorRef = useRef<HTMLDivElement>(null);
        const quillRef = useRef<Quill | null>(null);
        const onChangeRef = useRef(onChange);
        const [isReady, setIsReady] = useState(false);

        // Keep onChange ref up to date
        useEffect(() => {
            onChangeRef.current = onChange;
        }, [onChange]);

        // Initialize Quill editor only once
        useEffect(() => {
            if (!editorRef.current || quillRef.current) return;

            // Quill configuration optimized for news and content creation
            const quill = new Quill(editorRef.current, {
                theme: 'snow',
                placeholder,
                modules: {
                    toolbar: [
                        // Headers - optimized for news content
                        [{ 'header': [1, 2, 3, 4, false] }],
                        
                        // Text formatting
                        ['bold', 'italic', 'underline'],

                        // Lists and structure
                        [{ 'list': 'ordered'}, { 'list': 'bullet' }],
                        ['blockquote'],
                        
                        // Alignment
                        [{ 'align': [] }],

                        // Links
                        ['link'],

                        // Cleanup
                        ['clean']
                    ],
                    clipboard: {
                        // Allow more HTML when pasting to preserve existing content
                        matchVisual: true,
                    }
                },
                formats: [
                    'header', 'bold', 'italic', 'underline',
                    'list', 'align', 'blockquote', 'link'
                ]
            });

            quillRef.current = quill;

            // Handle content changes
            const handleTextChange = () => {
                const html = quill.root.innerHTML;
                const cleanHtml = html === '<p><br></p>' ? '' : html;
                if (onChangeRef.current) {
                    onChangeRef.current(cleanHtml);
                }
            };

            quill.on('text-change', handleTextChange);
            setIsReady(true);

            return () => {
                if (quillRef.current) {
                    quill.off('text-change', handleTextChange);
                    quillRef.current = null;
                    setIsReady(false);
                }
            };
        }, [placeholder]); // Only reinitialize if placeholder changes

        // Update content when value prop changes
        useEffect(() => {
            if (quillRef.current && isReady) {
                const quill = quillRef.current;
                const currentContent = quill.root.innerHTML;
                const normalizedCurrent = currentContent === '<p><br></p>' ? '' : currentContent;
                
                if (value !== normalizedCurrent) {
                    const currentSelection = quill.getSelection();
                    
                    // Temporarily disable text-change to prevent loops
                    quill.off('text-change');
                    
                    // Update content
                    quill.root.innerHTML = value || '';
                    
                    // Re-enable text-change
                    const handleTextChange = () => {
                        const html = quill.root.innerHTML;
                        const cleanHtml = html === '<p><br></p>' ? '' : html;
                        if (onChangeRef.current) {
                            onChangeRef.current(cleanHtml);
                        }
                    };
                    quill.on('text-change', handleTextChange);
                    
                    // Restore selection if valid
                    if (currentSelection && quill.getLength() > currentSelection.index) {
                        try {
                            quill.setSelection(currentSelection);
                        } catch {
                            // If selection restoration fails, place cursor at end
                            quill.setSelection(quill.getLength() - 1);
                        }
                    }
                }
            }
        }, [value, isReady]);

        // Handle disabled state
        useEffect(() => {
            if (quillRef.current && isReady) {
                quillRef.current.enable(!disabled);
            }
        }, [disabled, isReady]);



        return (
            <div 
                className={cn(
                    'rich-text-editor',
                    error && 'rich-text-editor--error',
                    disabled && 'rich-text-editor--disabled',
                    className
                )}
                ref={ref}
            >
                <div
                    ref={editorRef}
                    className={cn(
                        'min-h-[200px]',
                        error && 'border-red-500'
                    )}
                />
            </div>
        );
    }
);

RichTextEditor.displayName = 'RichTextEditor';

export { RichTextEditor };
