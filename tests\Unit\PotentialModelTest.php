<?php

use App\Models\Potential;

describe('Potential Model', function () {
    it('can create a potential', function () {
        $potential = Potential::factory()->create([
            'name' => 'Air Terjun Test',
            'type' => 'tourism',
            'description' => 'Deskripsi air terjun test',
        ]);

        expect($potential->name)->toBe('Air Terjun Test');
        expect($potential->type)->toBe('tourism');
        expect($potential->description)->toBe('Deskripsi air terjun test');
    });

    it('casts images to array', function () {
        $images = [
            ['url' => 'image1.jpg'],
            ['url' => 'image2.jpg'],
        ];

        $potential = Potential::factory()->create([
            'images' => $images,
        ]);

        expect($potential->images)->toBe($images);
        expect($potential->images)->toBeArray();
    });

    it('casts contact_info to array', function () {
        $contactInfo = [
            'phone' => '08123456789',
            'email' => '<EMAIL>',
            'person' => '<PERSON> Do<PERSON>',
        ];

        $potential = Potential::factory()->create([
            'contact_info' => $contactInfo,
        ]);

        expect($potential->contact_info)->toBe($contactInfo);
        expect($potential->contact_info)->toBeArray();
    });

    it('casts is_featured to boolean', function () {
        $potential = Potential::factory()->create([
            'is_featured' => 1,
        ]);

        expect($potential->is_featured)->toBe(true);
    });

    describe('Scopes', function () {
        beforeEach(function () {
            Potential::factory()->tourism()->create(['name' => 'Tourism Spot']);
            Potential::factory()->umkm()->create(['name' => 'UMKM Business']);
            Potential::factory()->umkm()->featured()->create(['name' => 'Featured Item']);
        });

        it('filters tourism potentials', function () {
            $tourismPotentials = Potential::tourism()->get();

            expect($tourismPotentials)->toHaveCount(1);
            expect($tourismPotentials->first()->type)->toBe('tourism');
        });

        it('filters UMKM potentials', function () {
            $umkmPotentials = Potential::umkm()->get();

            expect($umkmPotentials)->toHaveCount(2);
            expect($umkmPotentials->first()->type)->toBe('umkm');
        });

        it('filters featured potentials', function () {
            $featuredPotentials = Potential::featured()->get();

            expect($featuredPotentials)->toHaveCount(1);
            expect($featuredPotentials->first()->is_featured)->toBe(true);
        });

        it('filters by type', function () {
            $tourismPotentials = Potential::byType('tourism')->get();
            $umkmPotentials = Potential::byType('umkm')->get();

            expect($tourismPotentials)->toHaveCount(1);
            expect($umkmPotentials)->toHaveCount(2);
        });
    });

    describe('Accessors', function () {
        it('returns images as array', function () {
            $images = [['url' => 'test.jpg']];
            $potential = Potential::factory()->create(['images' => $images]);

            expect($potential->image_list)->toBe($images);
            expect($potential->image_list)->toBeArray();
        });

        it('returns empty array when images is not array', function () {
            $potential = Potential::factory()->create(['images' => null]);

            expect($potential->image_list)->toBe([]);
            expect($potential->image_list)->toBeArray();
        });

        it('returns first image as featured image', function () {
            $images = [
                ['url' => 'first.jpg'],
                ['url' => 'second.jpg'],
            ];
            $potential = Potential::factory()->create(['images' => $images]);

            expect($potential->featured_image)->toBe(['url' => 'first.jpg']);
        });

        it('returns null for featured image when no images', function () {
            $potential = Potential::factory()->create(['images' => []]);

            expect($potential->featured_image)->toBeNull();
        });

        it('returns correct type name in Indonesian', function () {
            $tourism = Potential::factory()->tourism()->create();
            $umkm = Potential::factory()->umkm()->create();

            expect($tourism->type_name)->toBe('Wisata');
            expect($umkm->type_name)->toBe('UMKM');
        });

        it('returns contact info as array', function () {
            $contactInfo = ['phone' => '123'];
            $potential = Potential::factory()->create(['contact_info' => $contactInfo]);

            expect($potential->contact_list)->toBe($contactInfo);
            expect($potential->contact_list)->toBeArray();
        });

        it('returns empty array when contact_info is not array', function () {
            $potential = Potential::factory()->create(['contact_info' => null]);

            expect($potential->contact_list)->toBe([]);
            expect($potential->contact_list)->toBeArray();
        });
    });

    describe('Image Helpers', function () {
        it('returns correct image URL for specified index and size', function () {
            $images = [
                [
                    'original' => ['url' => 'original1.jpg'],
                    'medium' => ['url' => 'medium1.jpg'],
                    'thumbnail' => ['url' => 'thumbnail1.jpg'],
                ],
                [
                    'original' => ['url' => 'original2.jpg'],
                    'medium' => ['url' => 'medium2.jpg'],
                ],
            ];

            $potential = Potential::factory()->create(['images' => $images]);

            expect($potential->getImageUrl(0, 'medium'))->toBe('medium1.jpg');
            expect($potential->getImageUrl(1, 'medium'))->toBe('medium2.jpg');
        });

        it('falls back to medium size when requested size not available', function () {
            $images = [
                [
                    'medium' => ['url' => 'medium.jpg'],
                    'original' => ['url' => 'original.jpg'],
                ],
            ];

            $potential = Potential::factory()->create(['images' => $images]);

            expect($potential->getImageUrl(0, 'large'))->toBe('medium.jpg');
        });

        it('returns null when image index does not exist', function () {
            $potential = Potential::factory()->create(['images' => []]);

            expect($potential->getImageUrl(0))->toBeNull();
        });

        it('generates correct srcset for image', function () {
            $images = [
                [
                    'original' => ['url' => 'original.jpg'],
                    'medium' => ['url' => 'medium.jpg'],
                    'thumbnail' => ['url' => 'thumbnail.jpg'],
                ],
            ];

            $potential = Potential::factory()->create(['images' => $images]);

            $srcSet = $potential->getImageSrcSet(0);

            expect($srcSet)->toContain('thumbnail.jpg 300w');
            expect($srcSet)->toContain('medium.jpg 800w');
            expect($srcSet)->toContain('original.jpg 1200w');
        });

        it('returns empty string for srcset when image does not exist', function () {
            $potential = Potential::factory()->create(['images' => []]);

            expect($potential->getImageSrcSet(0))->toBe('');
        });

        it('returns all image URLs for specified size', function () {
            $images = [
                ['medium' => ['url' => 'medium1.jpg']],
                ['medium' => ['url' => 'medium2.jpg']],
            ];

            $potential = Potential::factory()->create(['images' => $images]);

            $urls = $potential->getAllImageUrls('medium');

            expect($urls)->toBe(['medium1.jpg', 'medium2.jpg']);
        });

        it('returns empty array when no images available', function () {
            $potential = Potential::factory()->create(['images' => []]);

            expect($potential->getAllImageUrls())->toBe([]);
        });
    });

    describe('Factory States', function () {
        it('creates tourism potential', function () {
            $potential = Potential::factory()->tourism()->create();

            expect($potential->type)->toBe('tourism');
        });

        it('creates UMKM potential', function () {
            $potential = Potential::factory()->umkm()->create();

            expect($potential->type)->toBe('umkm');
        });

        it('creates featured potential', function () {
            $potential = Potential::factory()->featured()->create();

            expect($potential->is_featured)->toBe(true);
        });

        it('creates potential with images', function () {
            $potential = Potential::factory()->withImages()->create();

            expect($potential->images)->toBeArray();
            expect($potential->images)->not()->toBeEmpty();
        });

        it('creates potential with custom contact', function () {
            $customContact = ['phone' => '08123456789'];
            $potential = Potential::factory()->withContact($customContact)->create();

            expect($potential->contact_info['phone'])->toBe('08123456789');
        });
    });

    describe('Validation', function () {
        it('requires name', function () {
            expect(fn () => Potential::factory()->create(['name' => null]))
                ->toThrow(\Illuminate\Database\QueryException::class);
        });

        it('requires type', function () {
            expect(fn () => Potential::factory()->create(['type' => null]))
                ->toThrow(\Illuminate\Database\QueryException::class);
        });

        it('allows null values for optional fields', function () {
            $potential = Potential::factory()->create([
                'images' => null,
                'contact_info' => null,
                'location' => null,
            ]);

            expect($potential)->toBeInstanceOf(Potential::class);
        });
    });
});
