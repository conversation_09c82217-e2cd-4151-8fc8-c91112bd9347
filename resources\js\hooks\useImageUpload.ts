import { useCallback, useState } from 'react';

interface ImageVariant {
    path: string;
    url: string;
    size: number;
}

interface ImageUploadResult {
    [key: string]: ImageVariant;
}

interface UploadOptions {
    directory?: string;
    sizes?: Array<{ name: string; width: number }>;
    multiple?: boolean;
    onSuccess?: (data: ImageUploadResult | ImageUploadResult[]) => void;
    onError?: (error: string) => void;
}

interface UploadState {
    uploading: boolean;
    progress: number;
    error: string | null;
}

export function useImageUpload(options: UploadOptions = {}) {
    const [state, setState] = useState<UploadState>({
        uploading: false,
        progress: 0,
        error: null,
    });

    const uploadImage = useCallback(
        async (file: File | File[]) => {
            setState({ uploading: true, progress: 0, error: null });

            try {
                const formData = new FormData();

                if (Array.isArray(file)) {
                    file.forEach((f) => {
                        formData.append('images[]', f);
                    });

                    if (options.directory) {
                        formData.append('directory', options.directory);
                    }
                } else {
                    formData.append('image', file);

                    if (options.directory) {
                        formData.append('directory', options.directory);
                    }

                    if (options.sizes) {
                        formData.append('sizes', JSON.stringify(options.sizes));
                    }
                }

                const endpoint = Array.isArray(file) ? '/api/images/upload-multiple' : '/api/images/upload';

                const response = await fetch(endpoint, {
                    method: 'POST',
                    body: formData,
                    headers: {
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || '',
                    },
                });

                const result = await response.json();

                if (!response.ok) {
                    throw new Error(result.message || 'Upload gagal');
                }

                setState({ uploading: false, progress: 100, error: null });
                options.onSuccess?.(result.data);

                return result.data;
            } catch (error) {
                const errorMessage = error instanceof Error ? error.message : 'Upload gagal';
                setState({ uploading: false, progress: 0, error: errorMessage });
                options.onError?.(errorMessage);
                throw error;
            }
        },
        [options],
    );

    const deleteImage = useCallback(async (path: string) => {
        try {
            const response = await fetch('/api/images/delete', {
                method: 'DELETE',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || '',
                },
                body: JSON.stringify({ path }),
            });

            const result = await response.json();

            if (!response.ok) {
                throw new Error(result.message || 'Hapus gambar gagal');
            }

            return true;
        } catch (error) {
            const errorMessage = error instanceof Error ? error.message : 'Hapus gambar gagal';
            setState((prev) => ({ ...prev, error: errorMessage }));
            throw error;
        }
    }, []);

    const generatePlaceholder = useCallback(async (path: string) => {
        try {
            const response = await fetch('/api/images/placeholder', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || '',
                },
                body: JSON.stringify({ path }),
            });

            const result = await response.json();

            if (!response.ok) {
                throw new Error(result.message || 'Generate placeholder gagal');
            }

            return result.data.placeholder;
        } catch (error) {
            console.error('Generate placeholder error:', error);
            return null;
        }
    }, []);

    const resetState = useCallback(() => {
        setState({ uploading: false, progress: 0, error: null });
    }, []);

    return {
        ...state,
        uploadImage,
        deleteImage,
        generatePlaceholder,
        resetState,
    };
}

// Hook untuk upload gambar dengan preview
export function useImageUploadWithPreview(options: UploadOptions = {}) {
    const [previews, setPreviews] = useState<string[]>([]);
    const upload = useImageUpload(options);

    const handleFileSelect = useCallback(
        (files: File | File[]) => {
            const fileArray = Array.isArray(files) ? files : [files];

            // Generate preview URLs
            const previewUrls = fileArray.map((file) => URL.createObjectURL(file));
            setPreviews(previewUrls);

            return upload.uploadImage(files);
        },
        [upload],
    );

    const clearPreviews = useCallback(() => {
        // Cleanup object URLs
        previews.forEach((url) => URL.revokeObjectURL(url));
        setPreviews([]);
    }, [previews]);

    return {
        ...upload,
        previews,
        handleFileSelect,
        clearPreviews,
    };
}
