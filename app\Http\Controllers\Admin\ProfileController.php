<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Profile;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Inertia\Inertia;

class ProfileController extends Controller
{
    /**
     * Display a listing of profile sections for admin management
     */
    public function index(Request $request)
    {
        $query = Profile::query()->latest('created_at');

        // Apply section filter
        if ($request->filled('section')) {
            $query->where('section', $request->section);
        }

        // Apply search filter
        if ($request->filled('search')) {
            $searchTerm = $request->search;
            $query->where(function ($q) use ($searchTerm) {
                $q->where('title', 'like', "%{$searchTerm}%")
                    ->orWhere('content', 'like', "%{$searchTerm}%");
            });
        }

        // Get paginated results
        $profiles = $query->select([
            'id', 'section', 'title', 'content', 'image', 'order',
            'created_at', 'updated_at',
        ])->paginate(10);

        // Get available sections for filter
        $sections = [
            'history' => 'Sejarah Desa',
            'vision_mission' => 'Visi & Misi',
            'organization' => 'Struktur Organisasi',
            'demographics' => 'Data Demografis',
            'geography' => 'Data Geografis',
        ];

        return Inertia::render('Admin/Profiles/Index', [
            'profiles' => $profiles,
            'sections' => $sections,
            'filters' => [
                'section' => $request->section,
                'search' => $request->search,
            ],
        ]);
    }

    /**
     * Show the form for creating a new profile section
     */
    public function create()
    {
        $sections = [
            'history' => 'Sejarah Desa',
            'vision_mission' => 'Visi & Misi',
            'organization' => 'Struktur Organisasi',
            'demographics' => 'Data Demografis',
            'geography' => 'Data Geografis',
        ];

        return Inertia::render('Admin/Profiles/Create', [
            'sections' => $sections,
        ]);
    }

    /**
     * Store a newly created profile section
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'section' => 'required|string|in:history,vision_mission,organization,demographics,geography',
            'title' => 'required|string|max:255',
            'content' => 'required|string',
            'image' => 'nullable|image|mimes:jpeg,png,jpg,webp|max:2048',
            'order' => 'nullable|integer|min:0',
        ]);

        // Handle image upload
        if ($request->hasFile('image')) {
            $image = $request->file('image');
            $imagePath = $image->store('profiles', 'public');
            $validated['image'] = $imagePath;
        }

        // Set default order if not provided
        if (! isset($validated['order'])) {
            $maxOrder = Profile::where('section', $validated['section'])->max('order') ?? 0;
            $validated['order'] = $maxOrder + 1;
        }

        Profile::create($validated);

        return redirect()->route('admin.profiles.index')
            ->with('success', 'Konten profil berhasil dibuat.');
    }

    /**
     * Display the specified profile section
     */
    public function show(Profile $profile)
    {
        return Inertia::render('Admin/Profiles/Show', [
            'profile' => $profile,
        ]);
    }

    /**
     * Show the form for editing the specified profile section
     */
    public function edit(Profile $profile)
    {
        $sections = [
            'history' => 'Sejarah Desa',
            'vision_mission' => 'Visi & Misi',
            'organization' => 'Struktur Organisasi',
            'demographics' => 'Data Demografis',
            'geography' => 'Data Geografis',
        ];

        return Inertia::render('Admin/Profiles/Edit', [
            'profile' => $profile,
            'sections' => $sections,
        ]);
    }

    /**
     * Update the specified profile section
     */
    public function update(Request $request, Profile $profile)
    {
        $validated = $request->validate([
            'section' => 'required|string|in:history,vision_mission,organization,demographics,geography',
            'title' => 'required|string|max:255',
            'content' => 'required|string',
            'image' => 'nullable|image|mimes:jpeg,png,jpg,webp|max:2048',
            'order' => 'nullable|integer|min:0',
        ]);

        // Handle image upload
        if ($request->hasFile('image')) {
            // Delete old image if exists
            if ($profile->image && Storage::disk('public')->exists($profile->image)) {
                Storage::disk('public')->delete($profile->image);
            }

            $image = $request->file('image');
            $imagePath = $image->store('profiles', 'public');
            $validated['image'] = $imagePath;
        }

        // Set default order if not provided
        if (! isset($validated['order'])) {
            $validated['order'] = $profile->order ?? 1;
        }

        $profile->update($validated);

        return redirect()->route('admin.profiles.index')
            ->with('success', 'Konten profil berhasil diperbarui.');
    }

    /**
     * Remove the specified profile section
     */
    public function destroy(Profile $profile)
    {
        // Delete associated image if exists
        if ($profile->image && Storage::disk('public')->exists($profile->image)) {
            Storage::disk('public')->delete($profile->image);
        }

        $profile->delete();

        return redirect()->route('admin.profiles.index')
            ->with('success', 'Konten profil berhasil dihapus.');
    }

    /**
     * Manage profile sections by type
     */
    public function manageSection(Request $request, $section)
    {
        $validSections = ['history', 'vision_mission', 'organization', 'demographics', 'geography'];

        abort_unless(in_array($section, $validSections), 404);

        $sectionTitles = [
            'history' => 'Sejarah Desa',
            'vision_mission' => 'Visi & Misi',
            'organization' => 'Struktur Organisasi',
            'demographics' => 'Data Demografis',
            'geography' => 'Data Geografis',
        ];

        $profiles = Profile::bySection($section)->ordered()->get();

        return Inertia::render('Admin/Profiles/ManageSection', [
            'section' => $section,
            'sectionTitle' => $sectionTitles[$section],
            'profiles' => $profiles,
        ]);
    }

    /**
     * Update order of profile items within a section
     */
    public function updateOrder(Request $request, $section)
    {
        $validated = $request->validate([
            'items' => 'required|array',
            'items.*.id' => 'required|integer|exists:profiles,id',
            'items.*.order' => 'required|integer|min:0',
        ]);

        foreach ($validated['items'] as $item) {
            Profile::where('id', $item['id'])
                ->where('section', $section)
                ->update(['order' => $item['order']]);
        }

        return redirect()->back()
            ->with('success', 'Urutan konten berhasil diperbarui.');
    }
}
