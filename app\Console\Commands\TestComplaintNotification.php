<?php

namespace App\Console\Commands;

use App\Jobs\SendComplaintNotification;
use App\Models\Complaint;
use Illuminate\Console\Command;

class TestComplaintNotification extends Command
{
    protected $signature = 'complaint:test-notification {ticket_number}';

    protected $description = 'Test complaint notification system';

    public function handle()
    {
        $ticketNumber = $this->argument('ticket_number');

        $complaint = Complaint::where('ticket_number', $ticketNumber)->first();

        if (! $complaint) {
            $this->error("Complaint with ticket number {$ticketNumber} not found.");

            return 1;
        }

        $this->info("Testing notification for complaint: {$complaint->ticket_number}");
        $this->info("Subject: {$complaint->subject}");
        $this->info("Status: {$complaint->status}");

        try {
            SendComplaintNotification::dispatch($complaint);
            $this->info('✅ Notification job dispatched successfully!');

            $this->info('To process the job, run: php artisan queue:work');

        } catch (\Exception $e) {
            $this->error('❌ Failed to dispatch notification: '.$e->getMessage());

            return 1;
        }

        return 0;
    }
}
