import type { SettingKey, SettingValue, VillageSettings } from '@/types/settings';

// Note: These utility functions are deprecated in favor of the useSettings hook
// They are kept for backward compatibility but should not be used in new code

/**
 * @deprecated Use useSettings hook instead
 * Utility function to get a setting value from page props
 * This function requires page props to be passed explicitly
 *
 * @param props - Page props containing settings
 * @param key - The setting key to retrieve
 * @param defaultValue - Default value to return if setting is not found
 * @returns The setting value or default value
 */
export function getSettingFromProps<K extends SettingKey>(
    props: { settings?: VillageSettings },
    key: K,
    defaultValue?: SettingValue<K>,
): SettingValue<K> | undefined {
    try {
        const settings = props.settings;

        if (settings && settings[key]) {
            return settings[key];
        }

        return defaultValue;
    } catch (error) {
        console.warn(`Failed to get setting '${key}':`, error);
        return defaultValue;
    }
}

/**
 * @deprecated Use useSettings hook instead
 * Utility function to get a nested property from a setting
 * Useful for accessing specific fields within a setting object
 *
 * @param props - Page props containing settings
 * @param key - The setting key
 * @param path - Dot notation path to the nested property (e.g., 'phone', 'etymology.lemah')
 * @param defaultValue - Default value if property is not found
 * @returns The nested property value or default value
 */
export function getSettingProperty<K extends SettingKey>(
    props: { settings?: VillageSettings },
    key: K,
    path: string,
    defaultValue?: unknown,
): unknown {
    try {
        const setting = getSettingFromProps(props, key);

        if (!setting) {
            return defaultValue;
        }

        // Navigate through the object using dot notation
        const pathParts = path.split('.');
        let current: unknown = setting;

        for (const part of pathParts) {
            if (current && typeof current === 'object' && current !== null && part in current) {
                current = (current as Record<string, unknown>)[part];
            } else {
                return defaultValue;
            }
        }

        return current;
    } catch (error) {
        console.warn(`Failed to get setting property '${key}.${path}':`, error);
        return defaultValue;
    }
}

/**
 * @deprecated Use useSettings hook instead
 * Utility function to check if a setting exists and has a value
 *
 * @param props - Page props containing settings
 * @param key - The setting key to check
 * @returns True if setting exists and has a truthy value
 */
export function hasSettingValue<K extends SettingKey>(props: { settings?: VillageSettings }, key: K): boolean {
    try {
        const setting = getSettingFromProps(props, key);
        return setting !== undefined && setting !== null;
    } catch (error) {
        console.warn(`Failed to check setting existence '${key}':`, error);
        return false;
    }
}

/**
 * @deprecated Use useSettings hook instead
 * Utility function to get all settings from page props
 *
 * @param props - Page props containing settings
 * @returns All settings or empty object if not available
 */
export function getAllSettingsFromProps(props: { settings?: VillageSettings }): Partial<VillageSettings> {
    try {
        const settings = props.settings;
        return settings || {};
    } catch (error) {
        console.warn('Failed to get all settings:', error);
        return {};
    }
}

/**
 * @deprecated Use useSettings hook instead
 * Type guard to check if settings are available in page props
 *
 * @param props - Page props containing settings
 * @returns True if settings are available
 */
export function areSettingsAvailable(props: { settings?: VillageSettings }): boolean {
    try {
        const settings = props.settings;
        return settings !== undefined && settings !== null && typeof settings === 'object';
    } catch {
        return false;
    }
}
