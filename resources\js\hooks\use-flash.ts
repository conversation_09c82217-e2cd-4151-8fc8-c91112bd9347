import { useToast } from '@/components/Common/ToastProvider';
import { router } from '@inertiajs/react';

export const useFlash = () => {
    const { addToast } = useToast();

    const flash = {
        success: (message: string, title?: string) => {
            addToast({
                type: 'success',
                title: title || 'Berhasil',
                message,
                duration: 5000,
            });
        },

        error: (message: string, title?: string) => {
            addToast({
                type: 'error',
                title: title || 'Kesalahan',
                message,
                duration: 7000,
            });
        },

        warning: (message: string, title?: string) => {
            addToast({
                type: 'warning',
                title: title || 'Peringatan',
                message,
                duration: 6000,
            });
        },

        info: (message: string, title?: string) => {
            addToast({
                type: 'info',
                title: title || 'Informasi',
                message,
                duration: 5000,
            });
        },

        // Helper for Laravel redirects with flash messages
        redirect: (url: string, flashType: 'success' | 'error' | 'warning' | 'info', message: string) => {
            router.visit(url, {
                data: {
                    flash: {
                        [flashType]: message,
                    },
                },
            });
        },
    };

    return flash;
};
