<?php

use App\Models\Setting;
use App\Services\FeatureFlagResolver;
use App\Services\FeatureFlagService;
use Illuminate\Foundation\Testing\RefreshDatabase;

uses(RefreshDatabase::class);

function setupFeatureFlags()
{
    // Create metadata
    Setting::updateOrCreate(
        ['key' => 'feature_flags._metadata'],
        [
            'value' => [
                'pages.layanan' => [
                    'title' => 'Halaman Layanan',
                    'description' => 'Mengaktifkan/menonaktifkan akses ke halaman layanan publik desa',
                    'category' => 'pages',
                    'default' => true,
                    'experimental' => false,
                    'warning' => 'Menonaktifkan halaman ini akan menyembunyikan semua informasi layanan dari warga',
                    'dependencies' => [],
                ],
                'pages.berita' => [
                    'title' => 'Halaman Berita',
                    'description' => 'Mengaktifkan/menonaktifkan akses ke halaman berita dan artikel desa',
                    'category' => 'pages',
                    'default' => true,
                    'experimental' => false,
                    'warning' => 'Menonaktifkan halaman ini akan menyembunyikan semua berita dari warga',
                    'dependencies' => [],
                ],
                'pages.potensi' => [
                    'title' => 'Halaman Potensi',
                    'description' => 'Mengaktifkan/menonaktifkan akses ke halaman potensi wisata dan UMKM desa',
                    'category' => 'pages',
                    'default' => true,
                    'experimental' => false,
                    'warning' => 'Menonaktifkan halaman ini akan menyembunyikan informasi wisata dan UMKM',
                    'dependencies' => [],
                ],
                'pages.pengaduan' => [
                    'title' => 'Halaman Pengaduan',
                    'description' => 'Mengaktifkan/menonaktifkan akses ke sistem pengaduan masyarakat',
                    'category' => 'pages',
                    'default' => true,
                    'experimental' => false,
                    'warning' => 'Menonaktifkan halaman ini akan menutup akses warga untuk menyampaikan pengaduan',
                    'dependencies' => [],
                ],
            ],
            'type' => 'json',
            'description' => 'Metadata dan definisi untuk feature flags halaman publik website desa',
        ]
    );

    // Create individual flag settings
    Setting::updateOrCreate(['key' => 'feature_flags.pages.layanan'], ['value' => true, 'type' => 'boolean']);
    Setting::updateOrCreate(['key' => 'feature_flags.pages.berita'], ['value' => true, 'type' => 'boolean']);
    Setting::updateOrCreate(['key' => 'feature_flags.pages.potensi'], ['value' => true, 'type' => 'boolean']);
    Setting::updateOrCreate(['key' => 'feature_flags.pages.pengaduan'], ['value' => true, 'type' => 'boolean']);
}

beforeEach(function () {
    setupFeatureFlags();
});

describe('Feature Flag System', function () {
    it('can resolve feature flags correctly', function () {
        $resolver = app(FeatureFlagResolver::class);

        // Test default enabled flags
        expect($resolver->resolve('pages.layanan'))->toBeTrue();
        expect($resolver->resolve('pages.berita'))->toBeTrue();
        expect($resolver->resolve('pages.potensi'))->toBeTrue();
        expect($resolver->resolve('pages.pengaduan'))->toBeTrue();

        // Test non-existent flag
        expect($resolver->resolve('pages.nonexistent'))->toBeFalse();
    });

    it('can update feature flags', function () {
        $resolver = app(FeatureFlagResolver::class);

        // Disable a feature flag
        $resolver->update('pages.layanan', false);
        expect($resolver->resolve('pages.layanan'))->toBeFalse();

        // Re-enable it
        $resolver->update('pages.layanan', true);
        expect($resolver->resolve('pages.layanan'))->toBeTrue();
    });

    it('can get all feature flags', function () {
        $resolver = app(FeatureFlagResolver::class);
        $flags = $resolver->getAllFlags();

        expect($flags)->toBeArray();
        expect($flags)->toHaveKey('pages.layanan');
        expect($flags)->toHaveKey('pages.berita');
        expect($flags)->toHaveKey('pages.potensi');
        expect($flags)->toHaveKey('pages.pengaduan');

        // All should be enabled by default
        expect($flags['pages.layanan'])->toBeTrue();
        expect($flags['pages.berita'])->toBeTrue();
        expect($flags['pages.potensi'])->toBeTrue();
        expect($flags['pages.pengaduan'])->toBeTrue();
    });

    it('provides feature flags to frontend via middleware', function () {
        $response = $this->get('/');

        $response->assertStatus(200);

        // Check that feature flags are passed to frontend
        $page = $response->viewData('page');
        expect($page['props'])->toHaveKey('featureFlags');

        $featureFlags = $page['props']['featureFlags'];
        expect($featureFlags)->toBeArray();
        expect($featureFlags)->toHaveKey('pages.layanan');
        expect($featureFlags)->toHaveKey('pages.berita');
        expect($featureFlags)->toHaveKey('pages.potensi');
        expect($featureFlags)->toHaveKey('pages.pengaduan');
    });
});

describe('Feature Flag Service', function () {
    it('can get flags by category', function () {
        $service = app(FeatureFlagService::class);

        // Debug: Check if settings exist
        $metadata = Setting::where('key', 'feature_flags._metadata')->first();
        expect($metadata)->not->toBeNull();

        // Debug: Check resolver
        $resolver = app(\App\Services\FeatureFlagResolver::class);
        $resolverFlags = $resolver->getAllFlags();
        expect($resolverFlags)->toHaveCount(4);

        // Clear cache
        \Illuminate\Support\Facades\Cache::flush();

        // Use service from container
        $service = app(\App\Services\FeatureFlagService::class);

        $allFlags = $service->getAllFlags();
        expect($allFlags)->toHaveCount(4);

        $categories = $service->getFlagsByCategory();

        expect($categories)->toBeArray();
        expect($categories)->toHaveCount(1); // Only 'pages' category

        $pagesCategory = $categories[0];
        expect($pagesCategory['name'])->toBe('pages');
        expect($pagesCategory['title'])->toBe('Halaman Publik');
        expect($pagesCategory['flags'])->toHaveCount(4);
        expect($pagesCategory['enabledCount'])->toBe(4);
        expect($pagesCategory['totalCount'])->toBe(4);
    });

    it('can get summary statistics', function () {
        $service = app(\App\Services\FeatureFlagService::class);
        $summary = $service->getSummary();

        expect($summary)->toHaveKey('total');
        expect($summary)->toHaveKey('enabled');
        expect($summary)->toHaveKey('disabled');
        expect($summary)->toHaveKey('byCategory');

        expect($summary['total'])->toBe(4);
        expect($summary['enabled'])->toBe(4);
        expect($summary['disabled'])->toBe(0);
    });

    it('can update multiple flags at once', function () {
        $service = app(\App\Services\FeatureFlagService::class);

        $updates = [
            'pages.layanan' => false,
            'pages.berita' => false,
        ];

        $results = $service->updateMultipleFlags($updates);

        expect($results)->toHaveKey('pages.layanan');
        expect($results)->toHaveKey('pages.berita');
        expect($results['pages.layanan'])->toBeTrue();
        expect($results['pages.berita'])->toBeTrue();

        // Verify flags are actually disabled
        expect($service->isEnabled('pages.layanan'))->toBeFalse();
        expect($service->isEnabled('pages.berita'))->toBeFalse();

        // Other flags should remain enabled
        expect($service->isEnabled('pages.potensi'))->toBeTrue();
        expect($service->isEnabled('pages.pengaduan'))->toBeTrue();
    });

    it('can reset flags to default values', function () {
        $service = app(\App\Services\FeatureFlagService::class);

        // Disable some flags
        $service->updateFlag('pages.layanan', false);
        $service->updateFlag('pages.berita', false);

        expect($service->isEnabled('pages.layanan'))->toBeFalse();
        expect($service->isEnabled('pages.berita'))->toBeFalse();

        // Reset to defaults
        $service->resetFlag('pages.layanan');
        $service->resetFlag('pages.berita');

        expect($service->isEnabled('pages.layanan'))->toBeTrue();
        expect($service->isEnabled('pages.berita'))->toBeTrue();
    });

    it('can reset all flags to default values', function () {
        $service = app(\App\Services\FeatureFlagService::class);

        // Disable all flags
        $service->updateFlag('pages.layanan', false);
        $service->updateFlag('pages.berita', false);
        $service->updateFlag('pages.potensi', false);
        $service->updateFlag('pages.pengaduan', false);

        // Verify all are disabled
        expect($service->isEnabled('pages.layanan'))->toBeFalse();
        expect($service->isEnabled('pages.berita'))->toBeFalse();
        expect($service->isEnabled('pages.potensi'))->toBeFalse();
        expect($service->isEnabled('pages.pengaduan'))->toBeFalse();

        // Reset all to defaults
        $results = $service->resetAllFlags();

        expect($results)->toHaveKey('pages.layanan');
        expect($results)->toHaveKey('pages.berita');
        expect($results)->toHaveKey('pages.potensi');
        expect($results)->toHaveKey('pages.pengaduan');

        // All should be successful
        expect($results['pages.layanan'])->toBeTrue();
        expect($results['pages.berita'])->toBeTrue();
        expect($results['pages.potensi'])->toBeTrue();
        expect($results['pages.pengaduan'])->toBeTrue();

        // Verify all are enabled again
        expect($service->isEnabled('pages.layanan'))->toBeTrue();
        expect($service->isEnabled('pages.berita'))->toBeTrue();
        expect($service->isEnabled('pages.potensi'))->toBeTrue();
        expect($service->isEnabled('pages.pengaduan'))->toBeTrue();
    });
});

describe('Feature Flag Middleware', function () {
    it('allows access when feature flag is enabled', function () {
        // Ensure layanan flag is enabled
        $service = app(\App\Services\FeatureFlagService::class);
        $service->updateFlag('pages.layanan', true);

        $response = $this->get('/layanan');
        $response->assertStatus(200);
    });

    it('blocks access when feature flag is disabled', function () {
        // Disable layanan flag
        $service = app(\App\Services\FeatureFlagService::class);
        $service->updateFlag('pages.layanan', false);

        $response = $this->get('/layanan');
        $response->assertStatus(403);
    });

    it('blocks access to berita when flag is disabled', function () {
        // Disable berita flag
        $service = app(\App\Services\FeatureFlagService::class);
        $service->updateFlag('pages.berita', false);

        $response = $this->get('/berita');
        $response->assertStatus(403);
    });

    it('blocks access to potensi when flag is disabled', function () {
        // Disable potensi flag
        $service = app(\App\Services\FeatureFlagService::class);
        $service->updateFlag('pages.potensi', false);

        $response = $this->get('/potensi');
        $response->assertStatus(403);
    });

    it('blocks access to pengaduan when flag is disabled', function () {
        // Disable pengaduan flag
        $service = app(\App\Services\FeatureFlagService::class);
        $service->updateFlag('pages.pengaduan', false);

        $response = $this->get('/pengaduan');
        $response->assertStatus(403);
    });

    it('allows access to home page regardless of feature flags', function () {
        // Disable all page flags
        $resolver = app(\App\Services\FeatureFlagResolver::class);
        $service = new \App\Services\FeatureFlagService($resolver);
        $service->updateFlag('pages.layanan', false);
        $service->updateFlag('pages.berita', false);
        $service->updateFlag('pages.potensi', false);
        $service->updateFlag('pages.pengaduan', false);

        // Home page should still be accessible
        $response = $this->get('/');
        $response->assertStatus(200);
    });

    it('allows access to profile page regardless of feature flags', function () {
        // Disable all page flags
        $service = app(\App\Services\FeatureFlagService::class);
        $service->updateFlag('pages.layanan', false);
        $service->updateFlag('pages.berita', false);
        $service->updateFlag('pages.potensi', false);
        $service->updateFlag('pages.pengaduan', false);

        // Profile page should still be accessible
        $response = $this->get('/profil');
        $response->assertStatus(200);
    });
});

describe('Feature Flag Admin Management', function () {
    beforeEach(function () {
        // Create admin user for testing
        $this->admin = \App\Models\User::factory()->create([
            'email' => '<EMAIL>',
            'role' => 'admin',
        ]);
    });

    it('can view feature flags in admin settings', function () {
        $response = $this->actingAs($this->admin)->get('/admin/settings');

        $response->assertStatus(200);
        $response->assertInertia(fn ($page) => $page->component('Admin/Settings/Index')
            ->has('featureFlags')
            ->has('featureFlagsSummary')
        );
    });

    it('can update feature flags via admin interface', function () {
        $response = $this->actingAs($this->admin)->put('/admin/settings/feature-flags', [
            'feature_flags' => [
                'pages.layanan' => false,
                'pages.berita' => true,
            ],
        ]);

        $response->assertRedirect();
        $response->assertSessionHas('success');

        // Verify flags were updated
        $service = app(\App\Services\FeatureFlagService::class);
        expect($service->isEnabled('pages.layanan'))->toBeFalse();
        expect($service->isEnabled('pages.berita'))->toBeTrue();
    });

    it('can update single feature flag via admin interface', function () {
        $response = $this->actingAs($this->admin)->put('/admin/settings/feature-flags/pages.layanan', [
            'feature_flags' => [
                'pages.layanan' => false,
            ],
        ]);

        $response->assertRedirect();
        $response->assertSessionHas('success');

        // Verify flag was updated
        $service = app(FeatureFlagService::class);
        expect($service->isEnabled('pages.layanan'))->toBeFalse();
    });

    it('can reset feature flag to default via admin interface', function () {
        // First disable a flag
        $service = app(\App\Services\FeatureFlagService::class);
        $service->updateFlag('pages.layanan', false);
        expect($service->isEnabled('pages.layanan'))->toBeFalse();

        // Reset via admin interface
        $response = $this->actingAs($this->admin)->post('/admin/settings/feature-flags/pages.layanan/reset');

        $response->assertRedirect();
        $response->assertSessionHas('success');

        // Verify flag was reset to default (true)
        expect($service->isEnabled('pages.layanan'))->toBeTrue();
    });

    it('can reset all feature flags to defaults via admin interface', function () {
        // First disable all flags
        $service = app(\App\Services\FeatureFlagService::class);
        $service->updateFlag('pages.layanan', false);
        $service->updateFlag('pages.berita', false);
        $service->updateFlag('pages.potensi', false);
        $service->updateFlag('pages.pengaduan', false);

        // Reset all via admin interface
        $response = $this->actingAs($this->admin)->post('/admin/settings/feature-flags/reset-all');

        $response->assertRedirect();
        $response->assertSessionHas('success');

        // Verify all flags were reset to default (true)
        expect($service->isEnabled('pages.layanan'))->toBeTrue();
        expect($service->isEnabled('pages.berita'))->toBeTrue();
        expect($service->isEnabled('pages.potensi'))->toBeTrue();
        expect($service->isEnabled('pages.pengaduan'))->toBeTrue();
    });
});
