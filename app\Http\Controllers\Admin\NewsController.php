<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\News;
use App\Services\ImageOptimizationService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use Inertia\Inertia;

class NewsController extends Controller
{
    protected ImageOptimizationService $imageService;

    public function __construct(ImageOptimizationService $imageService)
    {
        $this->imageService = $imageService;
    }

    /**
     * Display a listing of news for admin management
     */
    public function index(Request $request)
    {
        $query = News::query()->latest('created_at');

        // Apply status filter
        if ($request->filled('status')) {
            if ($request->status === 'published') {
                $query->where('is_published', true);
            } elseif ($request->status === 'draft') {
                $query->where('is_published', false);
            }
        }

        // Apply category filter
        if ($request->filled('category')) {
            $query->where('category', $request->category);
        }

        // Apply search filter
        if ($request->filled('search')) {
            $searchTerm = $request->search;
            $query->where(function ($q) use ($searchTerm) {
                $q->where('title', 'like', "%{$searchTerm}%")
                    ->orWhere('content', 'like', "%{$searchTerm}%")
                    ->orWhere('excerpt', 'like', "%{$searchTerm}%");
            });
        }

        // Get paginated results
        $news = $query->select([
            'id', 'title', 'slug', 'excerpt', 'featured_image',
            'category', 'is_published', 'published_at', 'created_at', 'updated_at',
        ])->paginate(10);

        // Define default categories
        $defaultCategories = ['pengumuman', 'kegiatan', 'pembangunan', 'sosial'];

        // Get additional categories from existing news
        $existingCategories = News::distinct()
            ->pluck('category')
            ->filter()
            ->reject(function ($category) use ($defaultCategories) {
                return in_array(strtolower($category), $defaultCategories);
            })
            ->sort()
            ->values();

        // Combine default and existing categories
        $categories = collect($defaultCategories)
            ->map('ucfirst')
            ->merge($existingCategories);

        return Inertia::render('Admin/News/Index', [
            'news' => $news,
            'categories' => $categories,
            'filters' => [
                'status' => $request->status,
                'category' => $request->category,
                'search' => $request->search,
            ],
        ]);
    }

    /**
     * Show the form for creating a new news article
     */
    public function create()
    {
        // Define default categories
        $defaultCategories = ['pengumuman', 'kegiatan', 'pembangunan', 'sosial'];

        // Get additional categories from existing news
        $existingCategories = News::distinct()
            ->pluck('category')
            ->filter()
            ->reject(function ($category) use ($defaultCategories) {
                return in_array(strtolower($category), $defaultCategories);
            })
            ->sort()
            ->values();

        // Combine default and existing categories
        $categories = collect($defaultCategories)
            ->map('ucfirst')
            ->merge($existingCategories);

        return Inertia::render('Admin/News/Create', [
            'categories' => $categories,
            'defaultCategory' => 'Pengumuman', // Set default category
        ]);
    }

    /**
     * Store a newly created news article
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'title' => 'required|string|max:255|unique:news,title',
            'content' => 'required|string',
            'excerpt' => 'nullable|string|max:500',
            'category' => 'required|string|max:100',
            'featured_image' => 'nullable|image|mimes:jpeg,png,jpg,webp|max:2048',
            'is_published' => 'boolean',
            'published_at' => 'nullable|date',
            'meta_title' => 'nullable|string|max:255',
            'meta_description' => 'nullable|string|max:255',
        ]);

        // Sanitize content
        $validated['content'] = $this->sanitizeHtml($validated['content']);

        // Generate slug
        $validated['slug'] = Str::slug($validated['title']);

        // Ensure unique slug
        $originalSlug = $validated['slug'];
        $counter = 1;
        while (News::where('slug', $validated['slug'])->exists()) {
            $validated['slug'] = $originalSlug.'-'.$counter;
            $counter++;
        }

        // Handle featured image upload
        if ($request->hasFile('featured_image')) {
            $validated['featured_image'] = $this->imageService->uploadAndOptimize(
                $request->file('featured_image'),
                'news'
            );
        }

        // Set published_at if publishing
        if (($validated['is_published'] ?? false) && ! ($validated['published_at'] ?? null)) {
            $validated['published_at'] = now();
        }

        $news = News::create($validated);

        return redirect()->route('admin.news.index')
            ->with('success', 'Berita berhasil dibuat.');
    }

    /**
     * Display the specified news article for editing
     */
    public function show(News $news)
    {
        return Inertia::render('Admin/News/Show', [
            'news' => $news,
        ]);
    }

    /**
     * Show the form for editing the specified news article
     */
    public function edit(News $news)
    {
        // Define default categories
        $defaultCategories = ['pengumuman', 'kegiatan', 'pembangunan', 'sosial'];

        // Get additional categories from existing news
        $existingCategories = News::distinct()
            ->pluck('category')
            ->filter()
            ->reject(function ($category) use ($defaultCategories) {
                return in_array(strtolower($category), $defaultCategories);
            })
            ->sort()
            ->values();

        // Combine default and existing categories
        $categories = collect($defaultCategories)
            ->map('ucfirst')
            ->merge($existingCategories);

        return Inertia::render('Admin/News/Edit', [
            'news' => $news,
            'categories' => $categories,
        ]);
    }

    /**
     * Update the specified news article
     */
    public function update(Request $request, News $news)
    {
        $validated = $request->validate([
            'title' => 'required|string|max:255|unique:news,title,'.$news->id,
            'content' => 'required|string',
            'excerpt' => 'nullable|string|max:500',
            'category' => 'required|string|max:100',
            'featured_image' => 'nullable|image|mimes:jpeg,png,jpg,webp|max:2048',
            'is_published' => 'boolean',
            'published_at' => 'nullable|date',
            'meta_title' => 'nullable|string|max:255',
            'meta_description' => 'nullable|string|max:255',
            'remove_featured_image' => 'boolean',
        ]);

        // Sanitize content
        $validated['content'] = $this->sanitizeHtml($validated['content']);

        // Update slug if title changed
        if ($news->title !== $validated['title']) {
            $validated['slug'] = Str::slug($validated['title']);

            // Ensure unique slug (excluding current news)
            $originalSlug = $validated['slug'];
            $counter = 1;
            while (News::where('slug', $validated['slug'])->where('id', '!=', $news->id)->exists()) {
                $validated['slug'] = $originalSlug.'-'.$counter;
                $counter++;
            }
        }

        // Handle featured image removal
        if ($validated['remove_featured_image'] ?? false) {
            // Delete old image if exists
            if ($news->featured_image && is_array($news->featured_image)) {
                foreach ($news->featured_image as $size => $imageData) {
                    if (isset($imageData['path'])) {
                        Storage::disk('public')->delete($imageData['path']);
                    }
                }
            }
            $validated['featured_image'] = null;
        }
        // Handle featured image upload
        elseif ($request->hasFile('featured_image')) {
            // Delete old image if exists
            if ($news->featured_image && is_array($news->featured_image)) {
                foreach ($news->featured_image as $size => $imageData) {
                    if (isset($imageData['path'])) {
                        Storage::disk('public')->delete($imageData['path']);
                    }
                }
            }
            $validated['featured_image'] = $this->imageService->uploadAndOptimize(
                $request->file('featured_image'),
                'news'
            );
        }
        // If no image changes, preserve the existing image
        else {
            // Remove featured_image from validated data to prevent overwriting
            unset($validated['featured_image']);
        }

        // Set published_at if publishing for the first time
        if (($validated['is_published'] ?? false) && ! $news->is_published && ! ($validated['published_at'] ?? null)) {
            $validated['published_at'] = now();
        }

        // Remove the remove_featured_image flag from validated data as it's not a database column
        unset($validated['remove_featured_image']);

        $news->update($validated);

        return redirect()->route('admin.news.index')
            ->with('success', 'Berita berhasil diperbarui.');
    }

    /**
     * Remove the specified news article
     */
    public function destroy(News $news)
    {
        // Delete featured image if exists
        if ($news->featured_image && is_array($news->featured_image)) {
            foreach ($news->featured_image as $size => $imageData) {
                if (isset($imageData['path'])) {
                    Storage::disk('public')->delete($imageData['path']);
                }
            }
        }

        $news->delete();

        return redirect()->route('admin.news.index')
            ->with('success', 'Berita berhasil dihapus.');
    }

    /**
     * Toggle publish status of news article
     */
    public function togglePublish(News $news)
    {
        $news->update([
            'is_published' => ! $news->is_published,
            'published_at' => $news->is_published ? $news->published_at : now(),
        ]);

        $status = $news->is_published ? 'dipublikasikan' : 'dijadikan draft';

        return redirect()->back()
            ->with('success', "Berita berhasil {$status}.");
    }

    /**
     * Preview news article
     */
    public function preview(News $news)
    {
        return Inertia::render('Admin/News/Preview', [
            'news' => $news,
        ]);
    }

    /**
     * Bulk publish news articles
     */
    public function bulkPublish(Request $request)
    {
        $validated = $request->validate([
            'news_ids' => 'required|array',
            'news_ids.*' => 'exists:news,id',
        ]);

        News::whereIn('id', $validated['news_ids'])
            ->update([
                'is_published' => true,
                'published_at' => now(),
            ]);

        return redirect()->back()
            ->with('success', 'Berita berhasil dipublikasikan.');
    }

    /**
     * Bulk unpublish news articles
     */
    public function bulkUnpublish(Request $request)
    {
        $validated = $request->validate([
            'news_ids' => 'required|array',
            'news_ids.*' => 'exists:news,id',
        ]);

        News::whereIn('id', $validated['news_ids'])
            ->update(['is_published' => false]);

        return redirect()->back()
            ->with('success', 'Berita berhasil dijadikan draft.');
    }

    /**
     * Bulk delete news articles
     */
    public function bulkDelete(Request $request)
    {
        $validated = $request->validate([
            'news_ids' => 'required|array',
            'news_ids.*' => 'exists:news,id',
        ]);

        $newsItems = News::whereIn('id', $validated['news_ids'])->get();

        foreach ($newsItems as $news) {
            // Delete featured image if exists
            if ($news->featured_image && is_array($news->featured_image)) {
                foreach ($news->featured_image as $size => $imageData) {
                    if (isset($imageData['path'])) {
                        Storage::disk('public')->delete($imageData['path']);
                    }
                }
            }
            $news->delete();
        }

        return redirect()->back()
            ->with('success', 'Berita berhasil dihapus.');
    }

    /**
     * Sanitize HTML content to prevent XSS attacks
     */
    private function sanitizeHtml(string $content): string
    {
        // List of allowed HTML tags for rich text content
        $allowedTags = '<p><br><strong><b><em><i><u><ul><ol><li><h1><h2><h3><h4><h5><h6><blockquote><a><img>';

        // Strip dangerous tags but keep allowed ones
        $sanitized = strip_tags($content, $allowedTags);

        // Remove dangerous attributes from allowed tags
        $sanitized = preg_replace('/(<[^>]*)\s(on\w+|javascript:|vbscript:|data:)[^>]*>/i', '$1>', $sanitized);

        return $sanitized;
    }
}
