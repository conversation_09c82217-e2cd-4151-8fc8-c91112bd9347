<?php

use App\Models\Service;

describe('Service Model', function () {
    it('can create a service', function () {
        $service = Service::factory()->create([
            'name' => 'Surat Keterangan Domisili',
            'description' => 'Layanan pembuatan surat keterangan domisili',
        ]);

        expect($service->name)->toBe('Surat Keterangan Domisili');
        expect($service->description)->toBe('Layanan pembuatan surat keterangan domisili');
    });

    it('casts requirements to array', function () {
        $requirements = ['KTP', 'KK', 'Surat RT'];

        $service = Service::factory()->create([
            'requirements' => $requirements,
        ]);

        expect($service->requirements)->toBe($requirements);
        expect($service->requirements)->toBeArray();
    });

    it('casts procedure to array', function () {
        $procedure = ['Step 1', 'Step 2', 'Step 3'];

        $service = Service::factory()->create([
            'procedure' => $procedure,
        ]);

        expect($service->procedure)->toBe($procedure);
        expect($service->procedure)->toBeArray();
    });

    it('casts contact_info to array', function () {
        $contactInfo = [
            'phone' => '08123456789',
            'email' => '<EMAIL>',
        ];

        $service = Service::factory()->create([
            'contact_info' => $contactInfo,
        ]);

        expect($service->contact_info)->toBe($contactInfo);
        expect($service->contact_info)->toBeArray();
    });

    it('casts is_active to boolean', function () {
        $service = Service::factory()->create([
            'is_active' => 1,
        ]);

        expect($service->is_active)->toBe(true);
    });

    describe('Scopes', function () {
        beforeEach(function () {
            Service::factory()->active()->create(['name' => 'Active Service']);
            Service::factory()->inactive()->create(['name' => 'Inactive Service']);
        });

        it('filters active services', function () {
            $activeServices = Service::active()->get();

            expect($activeServices)->toHaveCount(1);
            expect($activeServices->first()->name)->toBe('Active Service');
            expect($activeServices->first()->is_active)->toBe(true);
        });

        it('filters inactive services', function () {
            $inactiveServices = Service::inactive()->get();

            expect($inactiveServices)->toHaveCount(1);
            expect($inactiveServices->first()->name)->toBe('Inactive Service');
            expect($inactiveServices->first()->is_active)->toBe(false);
        });
    });

    describe('Accessors', function () {
        it('formats cost as "Gratis" when null or zero', function () {
            $freeService = Service::factory()->create(['cost' => null]);
            $zeroService = Service::factory()->create(['cost' => 0]);

            expect($freeService->formatted_cost)->toBe('Gratis');
            expect($zeroService->formatted_cost)->toBe('Gratis');
        });

        it('formats numeric cost with Indonesian currency format', function () {
            $service = Service::factory()->create(['cost' => 15000]);

            expect($service->formatted_cost)->toBe('Rp 15.000');
        });

        it('returns string cost as-is when not numeric', function () {
            $service = Service::factory()->create(['cost' => 'Sesuai ketentuan']);

            expect($service->formatted_cost)->toBe('Sesuai ketentuan');
        });

        it('returns requirements as array', function () {
            $requirements = ['KTP', 'KK'];
            $service = Service::factory()->create(['requirements' => $requirements]);

            expect($service->requirements_list)->toBe($requirements);
            expect($service->requirements_list)->toBeArray();
        });

        it('returns empty array when requirements is not array', function () {
            // Since requirements cannot be null in DB, test with empty array
            $service = Service::factory()->create(['requirements' => []]);

            expect($service->requirements_list)->toBe([]);
            expect($service->requirements_list)->toBeArray();
        });

        it('returns procedure as array', function () {
            $procedure = ['Step 1', 'Step 2'];
            $service = Service::factory()->create(['procedure' => $procedure]);

            expect($service->procedure_list)->toBe($procedure);
            expect($service->procedure_list)->toBeArray();
        });

        it('returns empty array when procedure is not array', function () {
            // Since procedure cannot be null in DB, test with empty array
            $service = Service::factory()->create(['procedure' => []]);

            expect($service->procedure_list)->toBe([]);
            expect($service->procedure_list)->toBeArray();
        });
    });

    describe('Validation', function () {
        it('requires name', function () {
            expect(fn () => Service::factory()->create(['name' => null]))
                ->toThrow(\Illuminate\Database\QueryException::class);
        });

        it('allows null values for optional fields', function () {
            $service = Service::factory()->create([
                'cost' => null,
                'processing_time' => null,
                'contact_info' => null,
            ]);

            expect($service)->toBeInstanceOf(Service::class);
        });

        it('defaults is_active to true', function () {
            $service = Service::factory()->create();

            // Check that is_active has a default value (should be true based on factory)
            expect($service->is_active)->toBeIn([true, false]);
        });
    });

    describe('Factory States', function () {
        it('creates active service', function () {
            $service = Service::factory()->active()->create();

            expect($service->is_active)->toBe(true);
        });

        it('creates inactive service', function () {
            $service = Service::factory()->inactive()->create();

            expect($service->is_active)->toBe(false);
        });

        it('creates free service', function () {
            $service = Service::factory()->free()->create();

            expect($service->cost)->toBe(0);
        });

        it('creates paid service', function () {
            $service = Service::factory()->paid(25000)->create();

            expect($service->cost)->toBe(25000);
        });

        it('creates service with custom requirements', function () {
            $customRequirements = ['Custom Requirement 1', 'Custom Requirement 2'];
            $service = Service::factory()->withRequirements($customRequirements)->create();

            expect($service->requirements)->toBe($customRequirements);
        });

        it('creates service with custom procedure', function () {
            $customProcedure = ['Custom Step 1', 'Custom Step 2'];
            $service = Service::factory()->withProcedure($customProcedure)->create();

            expect($service->procedure)->toBe($customProcedure);
        });
    });
});
