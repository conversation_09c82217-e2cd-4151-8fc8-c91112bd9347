// Hook for programmatically showing toasts
export function useToast() {
    const showToast = (message: string, type: 'success' | 'error' | 'warning' | 'info' = 'info') => {
        // Dispatch custom event for manual toast triggering
        window.dispatchEvent(
            new CustomEvent('show-toast', {
                detail: { message, type },
            }),
        );
    };

    return {
        showSuccess: (message: string) => showToast(message, 'success'),
        showError: (message: string) => showToast(message, 'error'),
        showWarning: (message: string) => showToast(message, 'warning'),
        showInfo: (message: string) => showToast(message, 'info'),
        show: showToast,
    };
}
