<?php

namespace App\Http\Controllers;

use App\Models\Complaint;
use Illuminate\Http\Request;
use Illuminate\Validation\Rule;
use Inertia\Inertia;
use Inertia\Response;

class ComplaintController extends Controller
{
    /**
     * Display public complaints
     */
    public function publicIndex(Request $request): Response
    {
        $query = Complaint::where('visibility', 'public')
            ->orderBy('created_at', 'desc');

        // Apply filters
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        if ($request->filled('category')) {
            $query->where('category', $request->category);
        }

        if ($request->filled('priority')) {
            $query->where('priority', $request->priority);
        }

        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('ticket_number', 'like', "%{$search}%")
                    ->orWhere('subject', 'like', "%{$search}%");
            });
        }

        $complaints = $query->paginate(10)->withQueryString();

        // Statistics for public complaints only
        $stats = [
            'total' => Complaint::where('visibility', 'public')->count(),
            'pending' => Complaint::where('visibility', 'public')->where('status', 'pending')->count(),
            'in_progress' => Complaint::where('visibility', 'public')->where('status', 'in_progress')->count(),
            'resolved' => Complaint::where('visibility', 'public')->where('status', 'resolved')->count(),
            'closed' => Complaint::where('visibility', 'public')->where('status', 'closed')->count(),
        ];

        return Inertia::render('Public/Complaint/ComplaintsPublic', [
            'complaints' => $complaints,
            'stats' => $stats,
            'filters' => $request->only(['status', 'category', 'priority', 'search']),
            'categories' => Complaint::getCategories(),
            'statuses' => Complaint::getStatuses(),
            'priorities' => Complaint::getPriorities(),
        ]);
    }

    public function index(): Response
    {
        return Inertia::render('Public/Complaint/Complaints', [
            'categories' => Complaint::getCategories(),
            'pageTitle' => 'Pengaduan Masyarakat',
            'pageDescription' => 'Sampaikan pengaduan, saran, atau aspirasi Anda kepada Pemerintah Desa Lemah Duhur',
        ]);
    }

    public function store(Request $request)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'nullable|email|max:255',
            'phone' => 'nullable|string|max:20',
            'category' => ['required', Rule::in(array_keys(Complaint::getCategories()))],
            'subject' => 'required|string|max:255',
            'description' => 'required|string|min:50',
            'attachments' => 'required|array|min:1|max:3',
            'attachments.*' => [
                'required',
                'file',
                'max:5120', // 5MB max
                function ($attribute, $value, $fail) {
                    if (! $value->isValid()) {
                        $fail('File upload tidak valid.');

                        return;
                    }

                    $allowedMimes = ['image/jpeg', 'image/pjpeg', 'image/png', 'image/webp'];
                    $allowedExtensions = ['jpg', 'jpeg', 'png', 'webp'];

                    $mimeType = $value->getMimeType();
                    $extension = strtolower($value->getClientOriginalExtension());

                    if (! in_array($mimeType, $allowedMimes) && ! in_array($extension, $allowedExtensions)) {
                        $fail('Foto harus berformat JPG, JPEG, PNG, atau WebP');
                    }
                },
            ],
        ], [
            'name.required' => 'Nama lengkap wajib diisi',
            'category.required' => 'Kategori pengaduan wajib dipilih',
            'category.in' => 'Kategori pengaduan tidak valid',
            'subject.required' => 'Subjek pengaduan wajib diisi',
            'description.required' => 'Deskripsi pengaduan wajib diisi',
            'description.min' => 'Deskripsi pengaduan minimal 50 karakter',
            'email.email' => 'Format email tidak valid',
            'attachments.required' => 'Foto bukti wajib dilampirkan',
            'attachments.min' => 'Minimal 1 foto bukti harus dilampirkan',
            'attachments.max' => 'Maksimal 3 foto bukti',
            'attachments.*.required' => 'Foto bukti wajib dilampirkan',
            'attachments.*.image' => 'File harus berupa gambar (foto)',
            'attachments.*.mimes' => 'Foto harus berformat JPG, JPEG, PNG, atau WebP',
            'attachments.*.max' => 'Ukuran foto maksimal 5MB',
        ]);

        // Validate that at least email or phone is provided
        if (empty($validated['email']) && empty($validated['phone'])) {
            return back()->withErrors([
                'contact' => 'Email atau nomor telepon wajib diisi minimal salah satu',
            ])->withInput();
        }

        // Generate unique ticket number
        $ticketNumber = Complaint::generateTicketNumber();

        // Handle file uploads
        $attachmentPaths = [];
        if ($request->hasFile('attachments')) {
            foreach ($request->file('attachments') as $file) {
                $filename = time().'_'.uniqid().'.'.$file->getClientOriginalExtension();
                $path = $file->storeAs('complaints', $filename, 'public');
                $attachmentPaths[] = [
                    'path' => $path,
                    'original_name' => $file->getClientOriginalName(),
                    'size' => $file->getSize(),
                    'mime_type' => $file->getMimeType(),
                ];
            }
        }

        // Create complaint
        $complaint = Complaint::create([
            'ticket_number' => $ticketNumber,
            'name' => $validated['name'],
            'email' => $validated['email'] ?? null,
            'phone' => $validated['phone'] ?? null,
            'category' => $validated['category'],
            'subject' => $validated['subject'],
            'description' => $validated['description'],
            'attachments' => $attachmentPaths,
        ]);

        // Note: Email notification will be sent automatically via ComplaintObserver

        return redirect()->route('complaints.success', ['ticket' => $ticketNumber])
            ->with('success', 'Pengaduan Anda telah berhasil dikirim dengan nomor tiket: '.$ticketNumber);
    }

    public function success(Request $request): Response
    {
        $ticketNumber = $request->get('ticket');

        $complaint = null;
        $error = null;

        if (! $ticketNumber) {
            $error = 'Nomor tiket tidak ditemukan';
        } else {
            $complaint = Complaint::where('ticket_number', $ticketNumber)->first();
            if (! $complaint) {
                $error = 'Nomor tiket tidak ditemukan';
            }
        }

        return Inertia::render('Public/Complaint/ComplaintSuccess', [
            'complaint' => $complaint,
            'error' => $error,
            'pageTitle' => 'Pengaduan Berhasil Dikirim',
            'pageDescription' => 'Pengaduan Anda telah berhasil dikirim dan akan diproses dalam 3-7 hari kerja',
        ]);
    }

    public function tracking(): Response
    {
        return Inertia::render('Public/Complaint/ComplaintTracking', [
            'pageTitle' => 'Lacak Pengaduan',
            'pageDescription' => 'Lacak status pengaduan Anda dengan memasukkan nomor tiket',
        ]);
    }

    public function track(Request $request)
    {
        $validated = $request->validate([
            'ticket_number' => 'required|string',
        ], [
            'ticket_number.required' => 'Nomor tiket wajib diisi',
        ]);

        $complaint = Complaint::where('ticket_number', $validated['ticket_number'])->first();

        if (! $complaint) {
            return back()->withErrors([
                'ticket_number' => 'Nomor tiket tidak ditemukan',
            ])->withInput();
        }

        return Inertia::render('Public/Complaint/ComplaintDetail', [
            'complaint' => $complaint->load('respondedBy'),
            'pageTitle' => 'Detail Pengaduan - '.$complaint->ticket_number,
            'pageDescription' => 'Status dan detail pengaduan Anda',
        ]);
    }
}
