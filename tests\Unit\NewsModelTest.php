<?php

use App\Models\News;

describe('News Model', function () {
    it('can create a news article', function () {
        $news = News::factory()->create([
            'title' => 'Berita Test',
            'content' => 'Konten berita test',
        ]);

        expect($news->title)->toBe('Berita Test');
        expect($news->content)->toBe('Konten berita test');
        expect($news->slug)->toBe('berita-test');
    });

    it('automatically generates slug from title on creation', function () {
        $news = News::factory()->create([
            'title' => 'Berita Dengan Judul Panjang',
        ]);

        expect($news->slug)->toBe('berita-dengan-judul-panjang');
    });

    it('updates slug when title changes', function () {
        $news = News::factory()->create([
            'title' => 'Judul Awal',
        ]);

        expect($news->slug)->toBe('judul-awal');

        $news->update(['title' => 'Judul Baru']);

        expect($news->slug)->toBe('judul-baru');
    });

    it('does not override existing slug when updating title', function () {
        $news = News::factory()->create([
            'title' => 'Judul Awal',
            'slug' => 'slug-custom',
        ]);

        $news->update(['title' => 'Judul Baru']);

        expect($news->slug)->toBe('slug-custom');
    });

    it('casts published_at to datetime', function () {
        $news = News::factory()->create([
            'published_at' => '2024-01-01 10:00:00',
        ]);

        expect($news->published_at)->toBeInstanceOf(\Illuminate\Support\Carbon::class);
    });

    it('casts is_published to boolean', function () {
        $news = News::factory()->create([
            'is_published' => 1,
        ]);

        expect($news->is_published)->toBe(true);
    });

    it('casts featured_image to array', function () {
        $imageData = [
            'original' => ['url' => 'test.jpg'],
            'medium' => ['url' => 'test-medium.jpg'],
        ];

        $news = News::factory()->create([
            'featured_image' => $imageData,
        ]);

        expect($news->featured_image)->toBe($imageData);
    });

    describe('Scopes', function () {
        beforeEach(function () {
            // Create published news
            News::factory()->published()->create([
                'title' => 'Published News',
                'published_at' => now()->subDay(),
            ]);

            // Create unpublished news
            News::factory()->unpublished()->create([
                'title' => 'Unpublished News',
            ]);

            // Create future published news
            News::factory()->futurePublished()->create([
                'title' => 'Future News',
                'published_at' => now()->addDay(),
            ]);
        });

        it('filters published news correctly', function () {
            $publishedNews = News::published()->get();

            expect($publishedNews)->toHaveCount(1);
            expect($publishedNews->first()->title)->toBe('Published News');
        });

        it('filters by category', function () {
            News::query()->delete();

            News::factory()->create(['category' => 'pengumuman']);
            News::factory()->create(['category' => 'kegiatan']);

            $pengumumanNews = News::byCategory('pengumuman')->get();

            expect($pengumumanNews)->toHaveCount(1);
            expect($pengumumanNews->first()->category)->toBe('pengumuman');
        });

        it('orders by latest published date', function () {
            // Clear existing news first
            News::query()->delete();

            $older = News::factory()->published()->create([
                'title' => 'Older News',
                'published_at' => now()->subDays(10),
            ]);

            $newer = News::factory()->published()->create([
                'title' => 'Newer News',
                'published_at' => now()->subDays(5),
            ]);

            $latestNews = News::latestPublished()->get();

            expect($latestNews->count())->toBe(2);
            expect($latestNews->first()->title)->toBe('Newer News');
            expect($latestNews->last()->title)->toBe('Older News');
        });
    });

    describe('Accessors', function () {
        it('generates excerpt from content when not provided', function () {
            $longContent = str_repeat('Lorem ipsum dolor sit amet. ', 50);

            $news = News::factory()->create([
                'content' => $longContent,
                'excerpt' => null,
            ]);

            expect($news->excerpt)->not()->toBeNull();
            expect(strlen($news->excerpt))->toBeLessThanOrEqual(150);
        });

        it('uses provided excerpt when available', function () {
            $customExcerpt = 'Custom excerpt text';

            $news = News::factory()->create([
                'excerpt' => $customExcerpt,
            ]);

            expect($news->excerpt)->toBe($customExcerpt);
        });

        it('uses title as meta_title when not provided', function () {
            $news = News::factory()->create([
                'title' => 'Test Title',
                'meta_title' => null,
            ]);

            expect($news->meta_title)->toBe('Test Title');
        });

        it('uses excerpt as meta_description when not provided', function () {
            $news = News::factory()->create([
                'excerpt' => 'Test excerpt',
                'meta_description' => null,
            ]);

            expect($news->meta_description)->toBe('Test excerpt');
        });
    });

    describe('Image Helpers', function () {
        it('returns null for featured image URL when no image', function () {
            $news = News::factory()->create([
                'featured_image' => null,
            ]);

            expect($news->getFeaturedImageUrl())->toBeNull();
        });

        it('returns correct featured image URL for specified size', function () {
            $imageData = [
                'original' => ['url' => 'original.jpg'],
                'medium' => ['url' => 'medium.jpg'],
                'thumbnail' => ['url' => 'thumbnail.jpg'],
            ];

            $news = News::factory()->create([
                'featured_image' => $imageData,
            ]);

            expect($news->getFeaturedImageUrl('medium'))->toBe('medium.jpg');
            expect($news->getFeaturedImageUrl('thumbnail'))->toBe('thumbnail.jpg');
        });

        it('falls back to medium size when requested size not available', function () {
            $imageData = [
                'medium' => ['url' => 'medium.jpg'],
            ];

            $news = News::factory()->create([
                'featured_image' => $imageData,
            ]);

            expect($news->getFeaturedImageUrl('large'))->toBe('medium.jpg');
        });

        it('generates correct srcset for responsive images', function () {
            $imageData = [
                'original' => ['url' => 'original.jpg'],
                'medium' => ['url' => 'medium.jpg'],
                'thumbnail' => ['url' => 'thumbnail.jpg'],
            ];

            $news = News::factory()->create([
                'featured_image' => $imageData,
            ]);

            $srcSet = $news->getFeaturedImageSrcSet();

            expect($srcSet)->toContain('thumbnail.jpg 300w');
            expect($srcSet)->toContain('medium.jpg 800w');
            expect($srcSet)->toContain('original.jpg 1200w');
        });

        it('returns empty string for srcset when no image', function () {
            $news = News::factory()->create([
                'featured_image' => null,
            ]);

            expect($news->getFeaturedImageSrcSet())->toBe('');
        });
    });

    describe('Validation', function () {
        it('requires title', function () {
            expect(fn () => News::factory()->create(['title' => null]))
                ->toThrow(\Illuminate\Database\QueryException::class);
        });

        it('allows null values for optional fields', function () {
            $news = News::factory()->create([
                'excerpt' => null,
                'featured_image' => null,
                'meta_title' => null,
                'meta_description' => null,
            ]);

            expect($news)->toBeInstanceOf(News::class);
        });
    });
});
