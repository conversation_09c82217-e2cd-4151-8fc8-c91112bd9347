<?php

namespace App\Services;

use App\Models\Setting;
use Laravel\Pennant\Contracts\Driver;

class SettingsFeatureStore implements Driver
{
    /**
     * Store for defined feature resolvers
     */
    protected array $defined = [];

    /**
     * Define an initial feature flag state resolver.
     */
    public function define(string $feature, callable $resolver): void
    {
        $this->defined[$feature] = $resolver;
    }

    /**
     * Retrieve the names of all defined features.
     */
    public function defined(): array
    {
        return array_keys($this->defined);
    }

    /**
     * Get multiple feature flag values.
     */
    public function getAll(array $features): array
    {
        $result = [];

        foreach ($features as $feature => $scopes) {
            $result[$feature] = [];
            foreach ($scopes as $scope) {
                $result[$feature][] = $this->get($feature, $scope);
            }
        }

        return $result;
    }

    /**
     * Retrieve a feature flag's value.
     */
    public function get(string $feature, mixed $scope): mixed
    {
        $key = "feature_flags.{$feature}";
        $value = Setting::get($key, null);

        // If no value is stored, use the defined resolver
        if ($value === null && isset($this->defined[$feature])) {
            $resolver = $this->defined[$feature];
            $value = $resolver($scope);

            // Store the resolved value
            if ($value !== null) {
                Setting::set($key, (bool) $value);
            }
        }

        return $value !== null ? (bool) $value : false;
    }

    /**
     * Set a feature flag's value.
     */
    public function set(string $feature, mixed $scope, mixed $value): void
    {
        $key = "feature_flags.{$feature}";
        Setting::set($key, (bool) $value);
    }

    /**
     * Set a feature flag's value for all scopes.
     */
    public function setForAllScopes(string $feature, mixed $value): void
    {
        $key = "feature_flags.{$feature}";
        Setting::set($key, (bool) $value);
    }

    /**
     * Delete a feature flag's value.
     */
    public function delete(string $feature, mixed $scope): void
    {
        $key = "feature_flags.{$feature}";
        Setting::forget($key);
    }

    /**
     * Purge the given features from storage.
     */
    public function purge(?array $features): void
    {
        if ($features === null) {
            // Purge all feature flags
            $allSettings = Setting::getAllSettings();
            foreach ($allSettings as $key => $value) {
                if (str_starts_with($key, 'feature_flags.')) {
                    Setting::forget($key);
                }
            }
        } else {
            foreach ($features as $feature) {
                $key = "feature_flags.{$feature}";
                Setting::forget($key);
            }
        }
    }
}
