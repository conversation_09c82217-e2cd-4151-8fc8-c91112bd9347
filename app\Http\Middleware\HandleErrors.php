<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Symfony\Component\HttpFoundation\Response;

class HandleErrors
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        try {
            $response = $next($request);

            // Handle HTTP error status codes
            if ($response->getStatusCode() >= 400) {
                $this->logError($request, $response->getStatusCode());
            }

            return $response;
        } catch (\Throwable $e) {
            // Log the error
            Log::error('Middleware caught error', [
                'message' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                'url' => $request->fullUrl(),
                'method' => $request->method(),
                'ip' => $request->ip(),
                'user_agent' => $request->userAgent(),
                'user_id' => auth()->id(),
            ]);

            // Re-throw the exception to let the handler deal with it
            throw $e;
        }
    }

    /**
     * Log error information
     */
    private function logError(Request $request, int $statusCode): void
    {
        if (app()->environment('production')) {
            Log::warning("HTTP {$statusCode} Error", [
                'status_code' => $statusCode,
                'url' => $request->fullUrl(),
                'method' => $request->method(),
                'ip' => $request->ip(),
                'user_agent' => $request->userAgent(),
                'user_id' => auth()->id(),
                'referer' => $request->header('referer'),
            ]);
        }
    }
}
