<?php

namespace App\Observers;

use App\Models\Service;
use App\Services\CacheService;

class ServiceObserver
{
    public function __construct(
        private CacheService $cacheService
    ) {}

    /**
     * Handle the Service "created" event.
     */
    public function created(Service $service): void
    {
        $this->clearCache();
    }

    /**
     * Handle the Service "updated" event.
     */
    public function updated(Service $service): void
    {
        $this->clearCache();
    }

    /**
     * Handle the Service "deleted" event.
     */
    public function deleted(Service $service): void
    {
        $this->clearCache();
    }

    /**
     * Clear service-related cache
     */
    private function clearCache(): void
    {
        $this->cacheService->clearServiceCache();
        cache()->forget('homepage_services');
        cache()->forget('services_list');
    }
}
