<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class AddPerformanceHeaders
{
    /**
     * Handle an incoming request.
     */
    public function handle(Request $request, Closure $next): Response
    {
        $startTime = microtime(true);

        $response = $next($request);

        // Calculate response time
        $responseTime = round((microtime(true) - $startTime) * 1000, 2);

        // Add performance headers
        $response->headers->set('X-Response-Time', $responseTime.'ms');
        $response->headers->set('X-Memory-Usage', $this->formatBytes(memory_get_usage(true)));
        $response->headers->set('X-Memory-Peak', $this->formatBytes(memory_get_peak_usage(true)));

        // Add cache headers for static assets
        if ($this->isStaticAsset($request)) {
            $response->headers->set('Cache-Control', 'public, max-age=31536000, immutable');
            $response->headers->set('Expires', now()->addYear()->toRfc7231String());
        }

        // Add security headers
        $response->headers->set('X-Content-Type-Options', 'nosniff');
        $response->headers->set('X-Frame-Options', 'DENY');
        $response->headers->set('X-XSS-Protection', '1; mode=block');
        $response->headers->set('Referrer-Policy', 'strict-origin-when-cross-origin');

        // Add performance hints
        if ($request->is('/')) {
            $response->headers->set('Link', $this->getPreloadLinks());
        }

        return $response;
    }

    /**
     * Check if request is for static asset
     */
    private function isStaticAsset(Request $request): bool
    {
        $path = $request->path();

        return preg_match('/\.(css|js|png|jpg|jpeg|gif|webp|svg|woff|woff2|ttf|eot|ico)$/i', $path);
    }

    /**
     * Get preload links for critical resources
     */
    private function getPreloadLinks(): string
    {
        $links = [];

        // Preload critical CSS
        if (file_exists(public_path('build/assets/app.css'))) {
            $links[] = '</build/assets/app.css>; rel=preload; as=style';
        }

        // Preload critical JS
        if (file_exists(public_path('build/assets/app.js'))) {
            $links[] = '</build/assets/app.js>; rel=preload; as=script';
        }

        // Preload critical fonts
        $fontPath = public_path('fonts');
        if (is_dir($fontPath)) {
            $fonts = glob($fontPath.'/*.woff2');
            foreach (array_slice($fonts, 0, 2) as $font) { // Limit to 2 fonts
                $fontUrl = str_replace(public_path(), '', $font);
                $links[] = "<{$fontUrl}>; rel=preload; as=font; type=font/woff2; crossorigin";
            }
        }

        return implode(', ', $links);
    }

    /**
     * Format bytes to human readable format
     */
    private function formatBytes(int $bytes): string
    {
        $units = ['B', 'KB', 'MB', 'GB'];

        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }

        return round($bytes, 2).$units[$i];
    }
}
