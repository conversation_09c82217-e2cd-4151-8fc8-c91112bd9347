import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { cn } from '@/lib/utils';
import type { FeatureFlagCategory as FeatureFlagCategoryType, FeatureFlagSectionProps } from '@/types/feature-flags';
import { Flag, Search, X } from 'lucide-react';
import { useMemo, useState } from 'react';
import FeatureFlagCategory from './FeatureFlagCategory';

export default function FeatureFlagSection({ flags, onToggle, processing }: FeatureFlagSectionProps) {
    const [searchQuery, setSearchQuery] = useState('');
    const [selectedCategory, setSelectedCategory] = useState<string>('all');

    // Group flags by category
    const categorizedFlags = useMemo(() => {
        const categories: Record<string, FeatureFlagCategoryType> = {};

        // Define category metadata
        const categoryMetadata = {
            pages: {
                title: 'Halaman Publik',
                description: 'Kontrol visibilitas halaman-halaman publik website desa',
            },
        };

        // Filter flags based on search query
        const filteredFlags = flags.filter((flag) => {
            const matchesSearch =
                searchQuery === '' ||
                flag.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
                flag.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
                flag.key.toLowerCase().includes(searchQuery.toLowerCase());

            const matchesCategory = selectedCategory === 'all' || flag.category === selectedCategory;

            return matchesSearch && matchesCategory;
        });

        // Group filtered flags by category
        filteredFlags.forEach((flag) => {
            if (!categories[flag.category]) {
                const metadata = categoryMetadata[flag.category as keyof typeof categoryMetadata] || {
                    title: flag.category.charAt(0).toUpperCase() + flag.category.slice(1),
                    description: `Fitur-fitur kategori ${flag.category}`,
                };

                categories[flag.category] = {
                    name: flag.category,
                    title: metadata.title,
                    description: metadata.description,
                    flags: [],
                    enabledCount: 0,
                    totalCount: 0,
                };
            }

            categories[flag.category].flags.push(flag);
            categories[flag.category].totalCount++;
            if (flag.enabled) {
                categories[flag.category].enabledCount++;
            }
        });

        return Object.values(categories).sort((a, b) => {
            // Sort categories: pages only
            const order = ['pages'];
            return order.indexOf(a.name) - order.indexOf(b.name);
        });
    }, [flags, searchQuery, selectedCategory]);

    // Get unique categories for filter
    const availableCategories = useMemo(() => {
        const categories = [...new Set(flags.map((flag) => flag.category))];
        return categories.sort();
    }, [flags]);

    // Calculate overall statistics
    const totalFlags = flags.length;
    const enabledFlags = flags.filter((flag) => flag.enabled).length;

    const clearSearch = () => {
        setSearchQuery('');
    };

    return (
        <div className="space-y-6">
            {/* Header with statistics */}
            <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
                <div className="flex items-center gap-3">
                    <div className="flex h-10 w-10 items-center justify-center rounded-lg bg-blue-100 dark:bg-blue-900/20">
                        <Flag className="h-5 w-5 text-blue-600 dark:text-blue-400" />
                    </div>
                    <div>
                        <h2 className="text-xl font-semibold text-gray-900 dark:text-white">Pengaturan Fitur</h2>
                        <p className="text-sm text-gray-600 dark:text-gray-400">
                            {enabledFlags} dari {totalFlags} fitur aktif
                        </p>
                    </div>
                </div>
            </div>

            {/* Search and Filter Controls */}
            <div className="flex flex-col gap-4 sm:flex-row sm:items-center">
                {/* Search Input */}
                <div className="relative flex-1">
                    <Label htmlFor="flag-search" className="sr-only">
                        Cari fitur
                    </Label>
                    <div className="relative">
                        <Search className="absolute top-1/2 left-3 h-4 w-4 -translate-y-1/2 text-gray-400" />
                        <Input
                            id="flag-search"
                            type="text"
                            placeholder="Cari fitur berdasarkan nama atau deskripsi..."
                            value={searchQuery}
                            onChange={(e) => setSearchQuery(e.target.value)}
                            className="pr-10 pl-10"
                        />
                        {searchQuery && (
                            <button
                                onClick={clearSearch}
                                className="absolute top-1/2 right-3 -translate-y-1/2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
                            >
                                <X className="h-4 w-4" />
                            </button>
                        )}
                    </div>
                </div>

                {/* Category Filter */}
                <div className="flex flex-wrap gap-2">
                    <button
                        onClick={() => setSelectedCategory('all')}
                        className={cn(
                            'rounded-full px-4 py-2 text-sm font-medium transition-colors',
                            selectedCategory === 'all'
                                ? 'bg-blue-100 text-blue-700 dark:bg-blue-900/20 dark:text-blue-400'
                                : 'bg-gray-100 text-gray-700 hover:bg-gray-200 dark:bg-gray-800 dark:text-gray-300 dark:hover:bg-gray-700',
                        )}
                    >
                        Semua
                    </button>
                    {availableCategories.map((category) => {
                        const categoryTitles = {
                            pages: 'Halaman',
                        };

                        return (
                            <button
                                key={category}
                                onClick={() => setSelectedCategory(category)}
                                className={cn(
                                    'rounded-full px-4 py-2 text-sm font-medium transition-colors',
                                    selectedCategory === category
                                        ? 'bg-blue-100 text-blue-700 dark:bg-blue-900/20 dark:text-blue-400'
                                        : 'bg-gray-100 text-gray-700 hover:bg-gray-200 dark:bg-gray-800 dark:text-gray-300 dark:hover:bg-gray-700',
                                )}
                            >
                                {categoryTitles[category as keyof typeof categoryTitles] || category}
                            </button>
                        );
                    })}
                </div>
            </div>

            {/* Feature Flag Categories */}
            <div className="space-y-4">
                {categorizedFlags.length > 0 ? (
                    categorizedFlags.map((category) => (
                        <FeatureFlagCategory key={category.name} category={category} onToggle={onToggle} processing={processing} />
                    ))
                ) : (
                    <div className="flex flex-col items-center justify-center py-12 text-center">
                        <div className="mb-4 rounded-full bg-gray-100 p-3 dark:bg-gray-800">
                            <Search className="h-6 w-6 text-gray-400" />
                        </div>
                        <h3 className="mb-2 text-lg font-medium text-gray-900 dark:text-white">Tidak ada fitur ditemukan</h3>
                        <p className="text-sm text-gray-600 dark:text-gray-400">
                            {searchQuery
                                ? `Tidak ada fitur yang cocok dengan pencarian "${searchQuery}"`
                                : 'Tidak ada fitur dalam kategori yang dipilih'}
                        </p>
                        {searchQuery && (
                            <button
                                onClick={clearSearch}
                                className="mt-4 text-sm text-blue-600 hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-300"
                            >
                                Hapus pencarian
                            </button>
                        )}
                    </div>
                )}
            </div>
        </div>
    );
}
