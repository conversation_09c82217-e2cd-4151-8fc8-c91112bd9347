# Design Document

## Overview

The global settings system will build upon the existing Setting model and infrastructure to provide administrators with a user-friendly interface for managing village information. The system leverages the current Laravel + Inertia.js + React architecture and extends the existing admin panel with a dedicated settings management interface.

## Architecture

### Backend Architecture
- **Existing Foundation**: Utilize the current `Setting` model with its caching mechanism and helper methods
- **Controller Layer**: Create `Admin\SettingController` to handle settings management with Inertia responses
- **Inertia Integration**: Use Inertia::render() for page responses and redirect() for form submissions
- **Validation**: Laravel Form Request classes for input validation with Inertia error handling
- **Caching**: Leverage existing cache implementation in Setting model for performance

### Frontend Architecture
- **Inertia Integration**: React page components that receive props from Laravel controllers
- **Form Management**: Use Inertia's useForm hook for form handling and validation
- **State Management**: Inertia handles state synchronization between <PERSON><PERSON> and <PERSON>act
- **UI Components**: Consistent with existing admin panel design patterns

## Components and Interfaces

### Backend Components

#### 1. Admin\SettingController
```php
class SettingController extends Controller
{
    public function index(): Response
    public function update(UpdateSettingsRequest $request): RedirectResponse
}
```

**Responsibilities:**
- Render settings page using Inertia::render() with current settings data
- Handle form submissions and update settings in database
- Return Inertia redirect responses with success/error messages
- Pass validation errors automatically through Inertia's error handling

#### 2. UpdateSettingsRequest
```php
class UpdateSettingsRequest extends FormRequest
{
    public function rules(): array
    public function messages(): array
}
```

**Responsibilities:**
- Validate village contact information (phone, email, address)
- Ensure required fields are present
- Format validation error messages in Indonesian

#### 3. SettingSeeder Enhancement
**Responsibilities:**
- Provide comprehensive default settings for Desa Lemah Duhur
- Include all essential village information
- Support easy updates and additions

### Frontend Components

#### 1. Admin/Settings/Index.tsx
**Main settings management interface**

**Props:**
```typescript
interface SettingsPageProps {
  settings: {
    'village.contact_info': ContactInfo;
    'village.profile': VillageProfile;
    'village.operating_hours': OperatingHours;
    // ... other settings
  };
}
```

**Responsibilities:**
- Receive settings data as props from Inertia
- Use Inertia's useForm hook for form state management
- Handle form submission using Inertia's post/put methods
- Display validation errors from Inertia's error bag
- Organize settings into logical sections

#### 2. Components/SettingsForm.tsx
**Reusable form component for settings**

**Responsibilities:**
- Render form fields based on setting type
- Handle input validation and formatting
- Provide consistent UI for different data types
- Support Indonesian language labels and messages

### Data Models

#### Setting Structure
The existing Setting model supports the following structure:
```php
[
    'key' => 'village.contact_info',
    'value' => [
        'phone' => '0',
        'whatsapp' => '0', 
        'email' => '<EMAIL>',
        'address' => 'Kantor Desa Lemah Duhur, Kec. Caringin, Kab. Bogor, Jawa Barat',
        'postal_code' => '16730',
        'maps_link' => 'https://maps.app.goo.gl/F2SWrp4QBYBJXiG6A'
    ],
    'type' => 'json',
    'description' => 'Contact information description'
]
```

#### Key Settings to Manage
1. **village.contact_info** - Phone, email, address, WhatsApp
2. **village.profile** - Basic village information
3. **village.operating_hours** - Service hours
4. **village.emergency_contacts** - Important contact numbers

## Error Handling

### Backend Error Handling
- **Validation Errors**: Return structured validation messages in Indonesian
- **Database Errors**: Log errors and return user-friendly messages
- **Cache Errors**: Graceful degradation with direct database access
- **Permission Errors**: Ensure only admin users can access settings

### Frontend Error Handling
- **Form Validation**: Use Inertia's error handling for server-side validation messages
- **Loading States**: Use Inertia's processing state for loading indicators
- **Success Feedback**: Use Inertia's flash messages for success notifications
- **Error Display**: Leverage Inertia's automatic error bag for field-specific errors

## Testing Strategy

### Backend Testing
1. **Unit Tests**
   - Setting model helper methods
   - Validation rules for different setting types
   - Cache behavior and fallbacks

2. **Feature Tests**
   - Settings controller endpoints
   - Admin authentication and authorization
   - Form submission and validation flows

### Frontend Testing
1. **Component Tests**
   - Settings form rendering and interaction
   - Validation message display
   - Form submission handling

2. **Integration Tests**
   - End-to-end settings update flow
   - Error handling scenarios
   - Admin panel navigation

## Implementation Considerations

### Security
- **Authentication**: Ensure only authenticated admin users can access settings
- **Authorization**: Implement proper role-based access control
- **Input Sanitization**: Validate and sanitize all user inputs
- **CSRF Protection**: Use Laravel's built-in CSRF protection

### Performance
- **Caching**: Leverage existing Setting model caching for read operations
- **Lazy Loading**: Load settings only when needed
- **Optimistic Updates**: Update UI immediately while saving in background
- **Minimal Queries**: Batch setting updates to reduce database calls

### User Experience
- **Indonesian Language**: All labels, messages, and content in Bahasa Indonesia
- **Mobile Responsive**: Ensure settings interface works on mobile devices
- **Clear Feedback**: Provide immediate feedback for all user actions
- **Logical Organization**: Group related settings together

### Extensibility
- **Modular Design**: Easy to add new setting categories
- **Type System**: Support for different data types (text, number, boolean, JSON)
- **Validation Framework**: Extensible validation rules for new settings
- **Admin Integration**: Seamless integration with existing admin panel