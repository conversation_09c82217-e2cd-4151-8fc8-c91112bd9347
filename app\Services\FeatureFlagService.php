<?php

namespace App\Services;

use Illuminate\Support\Facades\Log;

/**
 * Feature Flag Service
 *
 * This service class manages feature flags with CRUD operations,
 * category-based organization, and default flag definitions.
 */
class FeatureFlagService
{
    /**
     * Feature flag resolver instance
     */
    private FeatureFlagResolver $resolver;

    /**
     * Get feature flag metadata from database
     *
     * @return array Feature flag metadata
     */
    private function getMetadata(): array
    {
        try {
            return \App\Models\Setting::get('feature_flags._metadata', []);
        } catch (\Exception $e) {
            Log::error('Failed to get feature flag metadata: '.$e->getMessage());

            return [];
        }
    }

    /**
     * Category definitions with Indonesian labels
     */
    private const CATEGORIES = [
        'pages' => [
            'label' => 'Halaman Publik',
            'description' => 'Kontrol visibilitas halaman-halaman publik website desa',
        ],
    ];

    public function __construct(FeatureFlagResolver $resolver)
    {
        $this->resolver = $resolver;
    }

    /**
     * Get all feature flags with their metadata
     *
     * @return array Array of feature flags with complete information
     */
    public function getAllFlags(): array
    {
        try {
            $flags = [];
            $currentFlags = $this->resolver->getAllFlags();
            $metadata = $this->getMetadata();

            foreach ($metadata as $key => $definition) {
                try {
                    $flags[$key] = [
                        'key' => $key,
                        'title' => $definition['title'],
                        'description' => $definition['description'],
                        'category' => $definition['category'],
                        'enabled' => $currentFlags[$key] ?? $definition['default'],
                        'default' => $definition['default'],
                        'experimental' => $definition['experimental'] ?? false,
                        'warning' => $definition['warning'] ?? null,
                        'dependencies' => $definition['dependencies'] ?? [],
                    ];
                } catch (\Exception $innerE) {
                    if (app()->environment('testing')) {
                        throw $innerE; // Re-throw in testing to see the actual error
                    }
                    Log::error("Failed to process flag '{$key}': ".$innerE->getMessage());
                }
            }

            return $flags;
        } catch (\Exception $e) {
            if (app()->environment('testing')) {
                throw $e; // Re-throw in testing to see the actual error
            }
            Log::error('Failed to get all feature flags: '.$e->getMessage());

            return [];
        }
    }

    /**
     * Get feature flags organized by category
     *
     * @return array Feature flags grouped by category
     */
    public function getFlagsByCategory(): array
    {
        try {
            $flags = $this->getAllFlags();
            $categorized = [];

            foreach (self::CATEGORIES as $categoryKey => $categoryInfo) {
                $categorized[] = [
                    'name' => $categoryKey,
                    'title' => $categoryInfo['label'],
                    'description' => $categoryInfo['description'],
                    'flags' => [],
                    'enabledCount' => 0,
                    'totalCount' => 0,
                ];
            }

            foreach ($flags as $flag) {
                $category = $flag['category'];
                // Find the category in the array
                foreach ($categorized as &$cat) {
                    if ($cat['name'] === $category) {
                        $cat['flags'][] = $flag;
                        $cat['totalCount']++;

                        if ($flag['enabled']) {
                            $cat['enabledCount']++;
                        }
                        break;
                    }
                }
            }

            return $categorized;
        } catch (\Exception $e) {
            Log::error('Failed to get flags by category: '.$e->getMessage());

            return [];
        }
    }

    /**
     * Update a feature flag
     *
     * @param  string  $key  The feature flag key
     * @param  bool  $enabled  The new flag state
     * @return bool True if update was successful
     */
    public function updateFlag(string $key, bool $enabled): bool
    {
        try {
            // Validate that the flag exists in our metadata
            $metadata = $this->getMetadata();
            if (! isset($metadata[$key])) {
                Log::warning("Attempted to update undefined feature flag: {$key}");

                return false;
            }

            $this->resolver->update($key, $enabled);

            Log::info('Feature flag updated', [
                'flag' => $key,
                'enabled' => $enabled,
            ]);

            return true;
        } catch (\Exception $e) {
            Log::error("Failed to update feature flag '{$key}': ".$e->getMessage());

            return false;
        }
    }

    /**
     * Update multiple feature flags at once
     *
     * @param  array  $flags  Array of flag key => enabled pairs
     * @return array Results with success/failure for each flag
     */
    public function updateMultipleFlags(array $flags): array
    {
        $results = [];

        foreach ($flags as $key => $enabled) {
            $results[$key] = $this->updateFlag($key, (bool) $enabled);
        }

        return $results;
    }

    /**
     * Get a specific feature flag
     *
     * @param  string  $key  The feature flag key
     * @return array|null Feature flag data or null if not found
     */
    public function getFlag(string $key): ?array
    {
        try {
            $metadata = $this->getMetadata();
            if (! isset($metadata[$key])) {
                return null;
            }

            $definition = $metadata[$key];
            $enabled = $this->resolver->resolve($key);

            return [
                'key' => $key,
                'title' => $definition['title'],
                'description' => $definition['description'],
                'category' => $definition['category'],
                'enabled' => $enabled,
                'default' => $definition['default'],
                'experimental' => $definition['experimental'] ?? false,
                'warning' => $definition['warning'] ?? null,
                'dependencies' => $definition['dependencies'] ?? [],
            ];
        } catch (\Exception $e) {
            Log::error("Failed to get feature flag '{$key}': ".$e->getMessage());

            return null;
        }
    }

    /**
     * Initialize default feature flags
     */
    public function initializeDefaultFlags(): void
    {
        try {
            $metadata = $this->getMetadata();
            foreach ($metadata as $key => $definition) {
                $this->resolver->define(
                    $key,
                    $definition['default'],
                    $definition['description']
                );
            }

            Log::info('Default feature flags initialized');
        } catch (\Exception $e) {
            Log::error('Failed to initialize default feature flags: '.$e->getMessage());
            throw $e;
        }
    }

    /**
     * Get default feature flag definitions
     *
     * @return array Default flag definitions
     */
    public function getDefaultFlags(): array
    {
        return $this->getMetadata();
    }

    /**
     * Get category definitions
     *
     * @return array Category definitions
     */
    public function getCategories(): array
    {
        return self::CATEGORIES;
    }

    /**
     * Check if a feature flag is enabled
     *
     * @param  string  $key  The feature flag key
     * @return bool True if the flag is enabled
     */
    public function isEnabled(string $key): bool
    {
        return $this->resolver->resolve($key);
    }

    /**
     * Get summary statistics for all flags
     *
     * @return array Summary statistics
     */
    public function getSummary(): array
    {
        try {
            $flags = $this->getAllFlags();
            $categories = $this->getFlagsByCategory();

            $totalFlags = count($flags);
            $enabledFlags = count(array_filter($flags, fn ($flag) => $flag['enabled']));
            $experimentalFlags = count(array_filter($flags, fn ($flag) => $flag['experimental']));

            return [
                'total' => $totalFlags,
                'enabled' => $enabledFlags,
                'disabled' => $totalFlags - $enabledFlags,
                'byCategory' => array_reduce($categories, function ($carry, $category) {
                    $carry[$category['name']] = [
                        'enabled' => $category['enabledCount'],
                        'total' => $category['totalCount'],
                    ];

                    return $carry;
                }, []),
            ];
        } catch (\Exception $e) {
            Log::error('Failed to get feature flags summary: '.$e->getMessage());

            return [
                'total' => 0,
                'enabled' => 0,
                'disabled' => 0,
                'byCategory' => [],
            ];
        }
    }

    /**
     * Reset a flag to its default value
     *
     * @param  string  $key  The feature flag key
     * @return bool True if reset was successful
     */
    public function resetFlag(string $key): bool
    {
        try {
            $metadata = $this->getMetadata();
            if (! isset($metadata[$key])) {
                return false;
            }

            $defaultValue = $metadata[$key]['default'];

            return $this->updateFlag($key, $defaultValue);
        } catch (\Exception $e) {
            Log::error("Failed to reset feature flag '{$key}': ".$e->getMessage());

            return false;
        }
    }

    /**
     * Reset all flags to their default values
     *
     * @return array Results with success/failure for each flag
     */
    public function resetAllFlags(): array
    {
        $results = [];
        $metadata = $this->getMetadata();

        foreach ($metadata as $key => $definition) {
            $results[$key] = $this->updateFlag($key, $definition['default']);
        }

        return $results;
    }
}
