import '../css/app.css';

import { createInertiaApp } from '@inertiajs/react';
import { resolvePageComponent } from 'laravel-vite-plugin/inertia-helpers';
import { createRoot } from 'react-dom/client';
import { ErrorBoundary } from './components/Common/ErrorBoundary';
import { GlobalErrorProvider } from './components/Common/GlobalErrorProvider';
import { PreloaderProvider } from './components/Common/PreloaderProvider';
import { ToastProvider } from './components/Common/ToastProvider';
import { initializeTheme } from './hooks/use-appearance';
import { setupGlobalErrorHandling } from './utils/errorHandler';
import { initializeApp } from './utils/appInitialization';

const appName = import.meta.env.VITE_APP_NAME || 'Laravel';

createInertiaApp({
    title: (title) => (title ? \\ - \\ : appName),
    resolve: (name) => resolvePageComponent(\./pages/\.tsx\, import.meta.glob('./pages/**/*.tsx')),
    setup({ el, App, props }) {
        const root = createRoot(el);

        root.render(
            <ErrorBoundary>
                <PreloaderProvider>
                    <GlobalErrorProvider>
                        <ToastProvider>
                            <App {...props} />
                        </ToastProvider>
                    </GlobalErrorProvider>
                </PreloaderProvider>
            </ErrorBoundary>,
        );
    },
    progress: {
        color: '#4B5563',
    },
});

// Initialize application features
initializeApp();

// Setup global error handling
setupGlobalErrorHandling();

// This will set light / dark mode on load...
initializeTheme();

// Initialize browser extension defense
import('./utils/appInitialization').then(({ initializeApp }) => { initializeApp(); });
