import GoogleMaps from '@/components/Common/GoogleMaps';
import SEOHead from '@/components/Common/SEOHead';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { useFeatureFlags } from '@/hooks/useFeatureFlags';
import { useSettings } from '@/hooks/useSettings';
import PublicLayout from '@/layouts/PublicLayout';
import { getImageUrl, type ImageVariants } from '@/lib/image-utils';
import { Link } from '@inertiajs/react';
import { ArrowRight, Calendar, Clock, Mail, MapPin, Phone } from 'lucide-react';

interface News {
    id: number;
    title: string;
    slug: string;
    excerpt: string;
    featured_image: ImageVariants | string | null;
    published_at: string;
    category: string;
}

interface Service {
    id: number;
    name: string;
    description: string;
    cost: string | number;
}

interface Potential {
    id: number;
    name: string;
    type: 'tourism' | 'umkm';
    description: string;
    images: ImageVariants[] | string[];
}

interface HomeProps {
    latestNews: News[];
    featuredServices: Service[];
    featuredPotentials: Potential[];
    contactInfo: {
        address: string;
        phone: string;
        email: string;
        office_hours: string;
        maps_url: string;
        postal_code: string;
    };
    villageStats: {
        population: number;
        families: number;
        area: string;
        villages: number;
        established: string;
    };
    seoMeta?: {
        title: string;
        description: string;
        image?: string;
        url?: string;
        type?: string;
        siteName?: string;
        locale?: string;
    };
    structuredData?: object;
}

export default function Home({ latestNews, featuredServices, featuredPotentials, contactInfo, villageStats, seoMeta, structuredData }: HomeProps) {
    const { getVillageProfile } = useSettings();
    const { isPageEnabled } = useFeatureFlags();
    const formatDate = (dateString: string) => {
        return new Date(dateString).toLocaleDateString('id-ID', {
            year: 'numeric',
            month: 'long',
            day: 'numeric',
        });
    };

    const formatCost = (cost: string | number) => {
        if (!cost || cost === 0 || cost === '0') return 'Gratis';
        const num = typeof cost === 'number' ? cost : Number(cost);
        return isNaN(num) ? String(cost) : `Rp ${num.toLocaleString('id-ID')}`;
    };

    return (
        <>
            <SEOHead
                title={seoMeta?.title}
                description={seoMeta?.description}
                image={seoMeta?.image}
                url={seoMeta?.url}
                type={seoMeta?.type}
                siteName={seoMeta?.siteName}
                locale={seoMeta?.locale}
                structuredData={structuredData}
            />
            <PublicLayout
                title="Beranda"
                description={`Selamat datang di website resmi ${getVillageProfile()?.name || 'Desa Lemah Duhur'}, ${getVillageProfile()?.regency || 'Kabupaten Bogor'}`}
            >
                {/* Hero Section */}
                <section
                    className="relative bg-gradient-to-br from-green-50/90 via-green-50/90 to-blue-50/90 py-12 sm:py-16 lg:py-32 dark:from-green-950/40 dark:via-green-950/40 dark:to-blue-950/40"
                    style={{
                        backgroundImage: "url('/images/Foto_Desa_Depan.jpg')",
                        backgroundSize: 'cover',
                        backgroundPosition: 'center',
                    }}
                >
                    {/* overlay sits on top of the image but under the text */}
                    <div className="pointer-events-none absolute inset-0 bg-black/20 dark:bg-black/40" />
                    <div className="relative z-10 container mx-auto px-3 sm:px-4 lg:px-8">
                        <div className="grid grid-cols-1 items-center gap-8 sm:gap-12 lg:grid-cols-2">
                            <div className="text-center lg:text-left">
                                <h1 className="mb-4 text-2xl leading-tight font-bold text-white sm:mb-6 sm:text-3xl md:text-4xl lg:text-5xl xl:text-6xl">
                                    Selamat Datang di
                                    <span className="block text-green-400">{getVillageProfile()?.name || 'Desa Lemah Duhur'}</span>
                                </h1>
                                <p className="mb-6 text-base leading-relaxed text-white sm:mb-8 sm:text-lg lg:text-xl">
                                    {getVillageProfile()?.district || 'Kecamatan Caringin'}, {getVillageProfile()?.regency || 'Kabupaten Bogor'},{' '}
                                    {getVillageProfile()?.province || 'Jawa Barat'}
                                </p>
                                <div className="flex flex-col justify-center gap-3 sm:flex-row sm:gap-4 lg:justify-start">
                                    <Button
                                        asChild
                                        size="lg"
                                        className="h-12 touch-manipulation bg-green-600 text-base hover:bg-green-700 sm:h-auto dark:bg-green-700 dark:hover:bg-green-600"
                                    >
                                        <Link href="/profil">
                                            Profil Desa
                                            <ArrowRight className="ml-2 h-4 w-4" />
                                        </Link>
                                    </Button>
                                    {isPageEnabled('layanan') && (
                                        <Button asChild variant="outline" size="lg" className="h-12 touch-manipulation text-base sm:h-auto">
                                            <Link href="/layanan">Layanan Publik</Link>
                                        </Button>
                                    )}
                                </div>
                            </div>

                            {/* Village Info Card */}
                            <div className="rounded-2xl border bg-card p-4 shadow-xl sm:p-6 lg:p-8">
                                <h3 className="mb-4 text-xl font-bold text-card-foreground sm:mb-6 sm:text-2xl">Portal Desa Lemah Duhur</h3>
                                <p className="mb-4 text-sm leading-relaxed text-muted-foreground sm:mb-6 sm:text-base">
                                    Selamat datang di website resmi {getVillageProfile()?.name || 'Desa Lemah Duhur'}. Kami berkomitmen untuk
                                    memberikan pelayanan terbaik kepada masyarakat dan membangun desa yang maju, mandiri, dan sejahtera. Mari
                                    bersama-sama membangun {getVillageProfile()?.name || 'Desa Lemah Duhur'} yang lebih baik.
                                </p>
                                <div className="space-y-3 text-xs text-muted-foreground sm:text-sm">
                                    <div className="flex items-start sm:items-center">
                                        <MapPin className="mt-0.5 mr-2 h-4 w-4 flex-shrink-0 sm:mt-0" />
                                        <span className="break-words">{contactInfo.address}</span>
                                    </div>
                                    {contactInfo?.phone && (
                                        <div className="flex items-start sm:items-center">
                                            <Phone className="mt-0.5 mr-2 h-4 w-4 flex-shrink-0 sm:mt-0" />
                                            <span className="break-all">Kantor Desa: {contactInfo.phone}</span>
                                        </div>
                                    )}

                                    <div className="flex items-start sm:items-center">
                                        <Clock className="mt-0.5 mr-2 h-4 w-4 flex-shrink-0 sm:mt-0" />
                                        <span>{contactInfo.office_hours}</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>

                {/* Latest News Section */}
                {isPageEnabled('berita') && (
                    <section className="bg-background py-12 sm:py-16">
                        <div className="container mx-auto px-3 sm:px-4 lg:px-8">
                            <div className="mb-8 text-center sm:mb-12">
                                <h2 className="mb-3 text-2xl font-bold text-foreground sm:mb-4 sm:text-3xl md:text-4xl">Berita Terbaru</h2>
                                <p className="mx-auto max-w-2xl px-4 text-base text-muted-foreground sm:px-0 sm:text-lg lg:text-xl">
                                    Ikuti perkembangan dan kegiatan terbaru di {getVillageProfile()?.name || 'Desa Lemah Duhur'}
                                </p>
                            </div>

                            {latestNews.length > 0 ? (
                                <div className="mb-6 grid grid-cols-1 gap-4 sm:mb-8 sm:grid-cols-2 sm:gap-6 lg:grid-cols-3 xl:grid-cols-4">
                                    {latestNews.map((news) => (
                                        <Card key={news.id} className="group touch-manipulation transition-shadow duration-300 hover:shadow-lg">
                                            {news.featured_image && (
                                                <div className="aspect-video overflow-hidden rounded-t-lg bg-gray-200">
                                                    <img
                                                        src={getImageUrl(news.featured_image, 'medium')}
                                                        alt={news.title}
                                                        className="h-full w-full object-cover"
                                                        loading="lazy"
                                                    />
                                                </div>
                                            )}
                                            <CardHeader className="p-4 pb-2 sm:p-6">
                                                <div className="mb-2 flex flex-wrap items-center gap-2 text-xs text-muted-foreground sm:text-sm">
                                                    <div className="flex items-center">
                                                        <Calendar className="mr-1 h-3 w-3 sm:h-4 sm:w-4" />
                                                        <span>{formatDate(news.published_at)}</span>
                                                    </div>
                                                    <span className="rounded-full bg-green-100 px-2 py-1 text-xs text-green-800 dark:bg-green-900/30 dark:text-green-300">
                                                        {news.category}
                                                    </span>
                                                </div>
                                                <CardTitle className="line-clamp-2 text-base transition-colors group-hover:text-green-600 sm:text-lg dark:group-hover:text-green-400">
                                                    <Link href={`/berita/${news.slug}`} className="touch-manipulation">
                                                        {news.title}
                                                    </Link>
                                                </CardTitle>
                                            </CardHeader>
                                            <CardContent className="p-4 pt-0 sm:p-6">
                                                <p className="line-clamp-3 text-sm text-muted-foreground">{news.excerpt}</p>
                                            </CardContent>
                                        </Card>
                                    ))}
                                </div>
                            ) : (
                                <div className="py-8 text-center sm:py-12">
                                    <p className="text-muted-foreground">Belum ada berita terbaru</p>
                                </div>
                            )}

                            <div className="text-center">
                                <Button asChild variant="outline" size="lg" className="h-12 touch-manipulation text-base sm:h-auto">
                                    <Link href="/berita">
                                        Lihat Semua Berita
                                        <ArrowRight className="ml-2 h-4 w-4" />
                                    </Link>
                                </Button>
                            </div>
                        </div>
                    </section>
                )}

                {/* Featured Services Section */}
                {isPageEnabled('layanan') && (
                    <section className="bg-muted/50 py-12 sm:py-16">
                        <div className="container mx-auto px-3 sm:px-4 lg:px-8">
                            <div className="mb-8 text-center sm:mb-12">
                                <h2 className="mb-3 text-2xl font-bold text-foreground sm:mb-4 sm:text-3xl md:text-4xl">Layanan Unggulan</h2>
                                <p className="mx-auto max-w-2xl px-4 text-base text-muted-foreground sm:px-0 sm:text-lg lg:text-xl">
                                    Akses berbagai layanan administrasi dan pelayanan publik desa
                                </p>
                            </div>

                            {featuredServices.length > 0 ? (
                                <div className="mb-6 grid grid-cols-1 gap-4 sm:mb-8 sm:grid-cols-2 sm:gap-6 lg:grid-cols-3">
                                    {featuredServices.map((service) => (
                                        <Card key={service.id} className="touch-manipulation transition-shadow duration-300 hover:shadow-lg">
                                            <CardHeader className="p-4 sm:p-6">
                                                <CardTitle className="text-base text-green-700 sm:text-lg dark:text-green-400">
                                                    {service.name}
                                                </CardTitle>
                                                <CardDescription className="text-sm leading-relaxed">{service.description}</CardDescription>
                                            </CardHeader>
                                            <CardContent className="p-4 pt-0 sm:p-6">
                                                <div className="flex items-center justify-between">
                                                    <span className="text-sm text-muted-foreground">Biaya:</span>
                                                    <span className="text-sm font-semibold text-green-600 sm:text-base dark:text-green-400">
                                                        {formatCost(service.cost)}
                                                    </span>
                                                </div>
                                            </CardContent>
                                        </Card>
                                    ))}
                                </div>
                            ) : (
                                <div className="py-8 text-center sm:py-12">
                                    <p className="text-muted-foreground">Belum ada layanan tersedia</p>
                                </div>
                            )}

                            <div className="text-center">
                                <Button asChild variant="outline" size="lg" className="h-12 touch-manipulation text-base sm:h-auto">
                                    <Link href="/layanan">
                                        Lihat Semua Layanan
                                        <ArrowRight className="ml-2 h-4 w-4" />
                                    </Link>
                                </Button>
                            </div>
                        </div>
                    </section>
                )}

                {/* Village Statistics Section */}
                <section
                    className="bg-muted py-12 sm:py-16"
                    style={{
                        backgroundImage: "url('/images/Foto_Desa_Depan.jpg')",
                        backgroundSize: 'cover',
                        backgroundPosition: 'center',
                    }}
                >
                    <div className="container mx-auto px-3 sm:px-4 lg:px-8">
                        <div className="mb-8 text-center sm:mb-12">
                            <h2 className="mb-3 text-2xl font-bold text-white sm:mb-4 sm:text-3xl md:text-4xl">Statistik Desa</h2>
                            <p className="mx-auto max-w-2xl px-4 text-base text-white sm:px-0 sm:text-lg lg:text-xl">
                                Informasi demografis {getVillageProfile()?.name || 'Desa Lemah Duhur'}
                            </p>
                            <small className="text-white">Tahun 2022/2023</small>
                        </div>

                        <div className="grid grid-cols-2 gap-4 sm:grid-cols-3 md:grid-cols-5 md:gap-6">
                            <Card className="text-center">
                                <CardHeader className="p-4 pb-0 sm:p-6">
                                    <CardTitle className="text-xl text-green-600 sm:text-2xl dark:text-green-400">
                                        {villageStats.population.toLocaleString('id-ID')}
                                    </CardTitle>
                                </CardHeader>
                                <CardContent className="p-4 pt-0 sm:p-6">
                                    <p className="text-sm text-muted-foreground">Penduduk</p>
                                </CardContent>
                            </Card>

                            <Card className="text-center">
                                <CardHeader className="p-4 pb-0 sm:p-6">
                                    <CardTitle className="text-xl text-green-600 sm:text-2xl dark:text-green-400">
                                        {villageStats.families.toLocaleString('id-ID')}
                                    </CardTitle>
                                </CardHeader>
                                <CardContent className="p-4 pt-0 sm:p-6">
                                    <p className="text-sm text-muted-foreground">Keluarga</p>
                                </CardContent>
                            </Card>

                            <Card className="text-center">
                                <CardHeader className="p-4 pb-0 sm:p-6">
                                    <CardTitle className="text-xl text-green-600 sm:text-2xl dark:text-green-400">{villageStats.area}</CardTitle>
                                </CardHeader>
                                <CardContent className="p-4 pt-0 sm:p-6">
                                    <p className="text-sm text-muted-foreground">Luas Wilayah</p>
                                </CardContent>
                            </Card>

                            <Card className="text-center">
                                <CardHeader className="p-4 pb-0 sm:p-6">
                                    <CardTitle className="text-xl text-green-600 sm:text-2xl dark:text-green-400">{villageStats.villages}</CardTitle>
                                </CardHeader>
                                <CardContent className="p-4 pt-0 sm:p-6">
                                    <p className="text-sm text-muted-foreground">Dusun</p>
                                </CardContent>
                            </Card>

                            <Card className="text-center">
                                <CardHeader className="p-4 pb-0 sm:p-6">
                                    <CardTitle className="text-xl text-green-600 sm:text-2xl dark:text-green-400">
                                        {villageStats.established}
                                    </CardTitle>
                                </CardHeader>
                                <CardContent className="p-4 pt-0 sm:p-6">
                                    <p className="text-sm text-muted-foreground">Tahun Berdiri</p>
                                </CardContent>
                            </Card>
                        </div>
                    </div>
                </section>

                {/* Location & Contact Section */}
                <section className="bg-background py-12 sm:py-16">
                    <div className="container mx-auto px-3 sm:px-4 lg:px-8">
                        <div className="mb-8 text-center sm:mb-12">
                            <h2 className="mb-3 text-2xl font-bold text-foreground sm:mb-4 sm:text-3xl md:text-4xl">Lokasi & Kontak</h2>
                            <p className="mx-auto max-w-2xl px-4 text-base text-muted-foreground sm:px-0 sm:text-lg lg:text-xl">
                                Temukan lokasi dan informasi kontak {getVillageProfile()?.name || 'Desa Lemah Duhur'}
                            </p>
                        </div>

                        <div className="grid grid-cols-1 gap-6 lg:grid-cols-2">
                            {/* Google Maps */}
                            <GoogleMaps mapsUrl={contactInfo.maps_url} address={contactInfo.address} />

                            {/* Contact Information */}
                            <div className="rounded-lg border bg-card p-4 sm:p-6">
                                <h3 className="mb-4 text-lg font-semibold text-card-foreground">Informasi Kontak</h3>
                                <div className="space-y-3">
                                    <div className="flex items-start">
                                        <MapPin className="mt-0.5 mr-3 h-4 w-4 flex-shrink-0 text-green-600 dark:text-green-400" />
                                        <div>
                                            <p className="text-sm font-medium text-card-foreground">Alamat</p>
                                            <p className="text-sm text-muted-foreground">{contactInfo.address}</p>
                                            <p className="text-sm text-muted-foreground">Kode Pos: {contactInfo.postal_code}</p>
                                        </div>
                                    </div>

                                    {contactInfo?.phone && (
                                        <div className="flex items-start">
                                            <Phone className="mt-0.5 mr-3 h-4 w-4 flex-shrink-0 text-green-600 dark:text-green-400" />
                                            <div>
                                                <p className="text-sm font-medium text-card-foreground">Telepon</p>
                                                <p className="text-sm text-muted-foreground">{contactInfo.phone}</p>
                                            </div>
                                        </div>
                                    )}
                                    {contactInfo?.email && (
                                        <div className="flex items-start">
                                            <Mail className="mt-0.5 mr-3 h-4 w-4 flex-shrink-0 text-green-600 dark:text-green-400" />
                                            <div>
                                                <p className="text-sm font-medium text-card-foreground">Email</p>
                                                <p className="text-sm text-muted-foreground">{contactInfo.email}</p>
                                            </div>
                                        </div>
                                    )}
                                    <div className="flex items-start">
                                        <Clock className="mt-0.5 mr-3 h-4 w-4 flex-shrink-0 text-green-600 dark:text-green-400" />
                                        <div>
                                            <p className="text-sm font-medium text-card-foreground">Jam Pelayanan</p>
                                            <p className="text-sm text-muted-foreground">{contactInfo.office_hours}</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>

                {/* Featured Potentials Section */}
                {isPageEnabled('potensi') && (
                    <section className="bg-muted/50 py-12 sm:py-16">
                        <div className="container mx-auto px-3 sm:px-4 lg:px-8">
                            <div className="mb-8 text-center sm:mb-12">
                                <h2 className="mb-3 text-2xl font-bold text-foreground sm:mb-4 sm:text-3xl md:text-4xl">Potensi Desa</h2>
                                <p className="mx-auto max-w-2xl px-4 text-base text-muted-foreground sm:px-0 sm:text-lg lg:text-xl">
                                    Jelajahi wisata dan produk UMKM unggulan {getVillageProfile()?.name || 'Desa Lemah Duhur'}
                                </p>
                            </div>

                            {featuredPotentials.length > 0 ? (
                                <div className="mb-6 grid grid-cols-1 gap-4 sm:mb-8 sm:grid-cols-2 sm:gap-6 lg:grid-cols-3 xl:grid-cols-4">
                                    {featuredPotentials.map((potential) => (
                                        <Card key={potential.id} className="group touch-manipulation transition-shadow duration-300 hover:shadow-lg">
                                            {potential.images.length > 0 && (
                                                <div className="aspect-square overflow-hidden rounded-t-lg bg-gray-200">
                                                    <img
                                                        src={'storage/' + getImageUrl(potential.images[0], 'medium')}
                                                        alt={potential.name}
                                                        className="h-full w-full object-cover"
                                                        loading="lazy"
                                                    />
                                                </div>
                                            )}
                                            <CardHeader className="p-4 pb-2 sm:p-6">
                                                <div className="mb-2 flex items-center justify-between">
                                                    <span
                                                        className={`rounded-full px-2 py-1 text-xs font-medium ${
                                                            potential.type === 'tourism'
                                                                ? 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300'
                                                                : 'bg-orange-100 text-orange-800 dark:bg-orange-900/30 dark:text-orange-300'
                                                        }`}
                                                    >
                                                        {potential.type === 'tourism' ? 'Wisata' : 'UMKM'}
                                                    </span>
                                                </div>
                                                <CardTitle className="line-clamp-2 text-base sm:text-lg">{potential.name}</CardTitle>
                                            </CardHeader>
                                            <CardContent className="p-4 pt-0 sm:p-6">
                                                <p className="line-clamp-3 text-sm text-gray-600">{potential.description.replace(/<[^>]+>/g, '')}</p>
                                            </CardContent>
                                        </Card>
                                    ))}
                                </div>
                            ) : (
                                <div className="py-8 text-center sm:py-12">
                                    <p className="text-gray-500">Belum ada potensi unggulan</p>
                                </div>
                            )}

                            <div className="text-center">
                                <Button asChild variant="outline" size="lg" className="h-12 touch-manipulation text-base sm:h-auto">
                                    <Link href="/potensi">
                                        Lihat Semua Potensi
                                        <ArrowRight className="ml-2 h-4 w-4" />
                                    </Link>
                                </Button>
                            </div>
                        </div>
                    </section>
                )}
            </PublicLayout>
        </>
    );
}
