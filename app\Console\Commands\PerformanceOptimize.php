<?php

namespace App\Console\Commands;

use App\Services\PerformanceService;
use Illuminate\Console\Command;

class PerformanceOptimize extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'performance:optimize {--clear : Clear all caches before optimization}';

    /**
     * The console command description.
     */
    protected $description = 'Optimize application performance';

    /**
     * Execute the console command.
     */
    public function handle(PerformanceService $performanceService)
    {
        $this->info('Starting performance optimization...');

        // Clear caches if requested
        if ($this->option('clear')) {
            $this->info('Clearing existing caches...');
            $performanceService->clearAllCache();
        }

        // Optimize configuration
        $this->info('Optimizing configuration...');
        $this->call('config:cache');

        // Optimize routes
        $this->info('Optimizing routes...');
        $this->call('route:cache');

        // Optimize views
        $this->info('Optimizing views...');
        $this->call('view:cache');

        // Warm up cache
        $this->info('Warming up cache...');
        $performanceService->warmUpCache();

        // Optimize database
        $this->info('Optimizing database...');
        $performanceService->optimizeDatabase();

        // Optimize assets
        $this->info('Optimizing assets...');
        $this->call('assets:optimize');

        $this->info('Performance optimization completed!');

        // Show performance metrics
        $metrics = $performanceService->getPerformanceMetrics();

        $this->newLine();
        $this->info('Performance Metrics:');
        $this->table(
            ['Metric', 'Value'],
            [
                ['Memory Usage', $this->formatBytes($metrics['memory_usage']['current'])],
                ['Peak Memory', $this->formatBytes($metrics['memory_usage']['peak'])],
                ['Cache Driver', $metrics['cache_stats']['cache_driver']],
                ['Cache Hit Ratio', ($metrics['cache_stats']['cache_hit_ratio'] ?? 0).'%'],
                ['Database Driver', $metrics['database_stats']['driver'] ?? 'Unknown'],
            ]
        );
    }

    /**
     * Format bytes to human readable format
     */
    private function formatBytes(int $bytes, int $precision = 2): string
    {
        $units = ['B', 'KB', 'MB', 'GB', 'TB'];

        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }

        return round($bytes, $precision).' '.$units[$i];
    }
}
