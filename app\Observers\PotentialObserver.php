<?php

namespace App\Observers;

use App\Models\Potential;
use App\Services\CacheService;

class PotentialObserver
{
    public function __construct(
        private CacheService $cacheService
    ) {}

    /**
     * Handle the Potential "created" event.
     */
    public function created(Potential $potential): void
    {
        $this->clearCache($potential);
    }

    /**
     * Handle the Potential "updated" event.
     */
    public function updated(Potential $potential): void
    {
        $this->clearCache($potential);

        // Clear specific related potentials cache if type changed
        if ($potential->wasChanged('type')) {
            cache()->forget("related_potentials_{$potential->getOriginal('type')}_{$potential->id}");
            cache()->forget("related_potentials_{$potential->type}_{$potential->id}");
        }
    }

    /**
     * Handle the Potential "deleted" event.
     */
    public function deleted(Potential $potential): void
    {
        $this->clearCache($potential);
        cache()->forget("related_potentials_{$potential->type}_{$potential->id}");
    }

    /**
     * Clear potential-related cache
     */
    private function clearCache(Potential $potential): void
    {
        $this->cacheService->clearPotentialCache();
        cache()->forget('homepage_potentials');
        cache()->forget('potentials_list_all');
        cache()->forget('potentials_list_tourism');
        cache()->forget('potentials_list_umkm');
    }
}
