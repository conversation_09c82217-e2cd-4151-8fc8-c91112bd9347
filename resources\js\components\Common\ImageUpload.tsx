import { cn } from '@/lib/utils';
import { Image as ImageIcon, Upload, X } from 'lucide-react';
import { useCallback, useMemo, useRef, useState } from 'react';

interface ImageUploadProps {
    value?: File | File[] | string | string[];
    onChange: (files: File | File[] | null) => void;
    multiple?: boolean;
    accept?: string;
    maxSize?: number; // in MB
    maxFiles?: number;
    className?: string;
    placeholder?: string;
    preview?: boolean;
    disabled?: boolean;
}

export default function ImageUpload({
    value,
    onChange,
    multiple = false,
    accept = 'image/jpeg,image/png,image/jpg,image/gif,image/webp',
    maxSize = 10,
    maxFiles = 5,
    className,
    placeholder = 'Klik atau seret gambar ke sini',
    preview = true,
    disabled = false,
}: ImageUploadProps) {
    const [dragActive, setDragActive] = useState(false);
    const [error, setError] = useState<string | null>(null);
    const inputRef = useRef<HTMLInputElement>(null);

    // Convert value to array for consistent handling
    const files = useMemo(() => {
        return Array.isArray(value) ? value : value ? [value] : [];
    }, [value]);

    const previewUrls = useMemo(() => {
        return files.map((file) => {
            if (typeof file === 'string') return file;
            return URL.createObjectURL(file);
        });
    }, [files]);

    const validateFile = useCallback(
        (file: File): string | null => {
            // Check file type
            const acceptedTypes = accept.split(',').map((type) => type.trim());
            if (!acceptedTypes.includes(file.type)) {
                return `Format file tidak didukung. Gunakan: ${acceptedTypes.join(', ')}`;
            }

            // Check file size
            const fileSizeMB = file.size / (1024 * 1024);
            if (fileSizeMB > maxSize) {
                return `Ukuran file terlalu besar. Maksimal ${maxSize}MB`;
            }

            return null;
        },
        [accept, maxSize],
    );

    const handleFiles = useCallback(
        (newFiles: FileList | null) => {
            if (!newFiles || newFiles.length === 0) return;

            const fileArray = Array.from(newFiles);
            const validFiles: File[] = [];
            let errorMessage = '';

            // Validate each file
            for (const file of fileArray) {
                const validation = validateFile(file);
                if (validation) {
                    errorMessage = validation;
                    break;
                }
                validFiles.push(file);
            }

            if (errorMessage) {
                setError(errorMessage);
                return;
            }

            // Check max files limit
            const currentFiles = files.filter((f) => f instanceof File) as File[];
            const totalFiles = currentFiles.length + validFiles.length;

            if (multiple && totalFiles > maxFiles) {
                setError(`Maksimal ${maxFiles} file`);
                return;
            }

            setError(null);

            if (multiple) {
                const allFiles = [...currentFiles, ...validFiles];
                onChange(allFiles.length > 0 ? allFiles : null);
            } else {
                onChange(validFiles[0] || null);
            }
        },
        [files, multiple, maxFiles, onChange, validateFile],
    );

    const handleDrag = useCallback((e: React.DragEvent) => {
        e.preventDefault();
        e.stopPropagation();
        if (e.type === 'dragenter' || e.type === 'dragover') {
            setDragActive(true);
        } else if (e.type === 'dragleave') {
            setDragActive(false);
        }
    }, []);

    const handleDrop = useCallback(
        (e: React.DragEvent) => {
            e.preventDefault();
            e.stopPropagation();
            setDragActive(false);

            if (disabled) return;

            const droppedFiles = e.dataTransfer.files;
            handleFiles(droppedFiles);
        },
        [handleFiles, disabled],
    );

    const handleInputChange = useCallback(
        (e: React.ChangeEvent<HTMLInputElement>) => {
            handleFiles(e.target.files);
        },
        [handleFiles],
    );

    const removeFile = useCallback(
        (index: number) => {
            if (multiple) {
                const currentFiles = files.filter((f) => f instanceof File) as File[];
                const newFiles = currentFiles.filter((_, i) => i !== index);
                onChange(newFiles.length > 0 ? newFiles : null);
            } else {
                onChange(null);
            }
        },
        [files, multiple, onChange],
    );

    const openFileDialog = () => {
        if (!disabled) {
            inputRef.current?.click();
        }
    };

    return (
        <div className={cn('space-y-4', className)}>
            {/* Upload Area */}
            <div
                className={cn(
                    'relative cursor-pointer rounded-lg border-2 border-dashed p-6 transition-colors',
                    dragActive ? 'border-blue-400 bg-blue-50' : 'border-gray-300 hover:border-gray-400',
                    disabled && 'cursor-not-allowed opacity-50',
                    error && 'border-red-300 bg-red-50',
                )}
                onDragEnter={handleDrag}
                onDragLeave={handleDrag}
                onDragOver={handleDrag}
                onDrop={handleDrop}
                onClick={openFileDialog}
            >
                <input
                    ref={inputRef}
                    type="file"
                    multiple={multiple}
                    accept={accept}
                    onChange={handleInputChange}
                    className="hidden"
                    disabled={disabled}
                />

                <div className="flex flex-col items-center justify-center space-y-2 text-center">
                    <Upload className="h-8 w-8 text-gray-400" />
                    <div className="text-sm text-gray-600">
                        <span className="font-medium text-blue-600 hover:text-blue-500">{placeholder}</span>
                    </div>
                    <p className="text-xs text-gray-500">
                        {accept.split(',').join(', ')} hingga {maxSize}MB
                        {multiple && ` (maks. ${maxFiles} file)`}
                    </p>
                </div>
            </div>

            {/* Error Message */}
            {error && <div className="rounded-md bg-red-50 p-3 text-sm text-red-600">{error}</div>}

            {/* Preview */}
            {preview && previewUrls.length > 0 && (
                <div className="grid grid-cols-2 gap-4 md:grid-cols-3 lg:grid-cols-4">
                    {previewUrls.map((url, index) => (
                        <div key={index} className="group relative">
                            <div className="aspect-square overflow-hidden rounded-lg bg-gray-100">
                                <img src={url} alt={`Preview ${index + 1}`} className="h-full w-full object-cover" />
                            </div>

                            {!disabled && (
                                <button
                                    type="button"
                                    onClick={(e) => {
                                        e.stopPropagation();
                                        removeFile(index);
                                    }}
                                    className="absolute -top-2 -right-2 rounded-full bg-red-500 p-1 text-white opacity-0 transition-opacity group-hover:opacity-100 hover:bg-red-600"
                                >
                                    <X className="h-4 w-4" />
                                </button>
                            )}
                        </div>
                    ))}
                </div>
            )}

            {/* File List (for non-preview mode) */}
            {!preview && files.length > 0 && (
                <div className="space-y-2">
                    {files.map((file, index) => (
                        <div key={index} className="flex items-center justify-between rounded-md bg-gray-50 p-3">
                            <div className="flex items-center space-x-3">
                                <ImageIcon className="h-5 w-5 text-gray-400" />
                                <span className="text-sm text-gray-700">{typeof file === 'string' ? file.split('/').pop() : file.name}</span>
                            </div>

                            {!disabled && (
                                <button type="button" onClick={() => removeFile(index)} className="text-red-500 hover:text-red-700">
                                    <X className="h-4 w-4" />
                                </button>
                            )}
                        </div>
                    ))}
                </div>
            )}
        </div>
    );
}
