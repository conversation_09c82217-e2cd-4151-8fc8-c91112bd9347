import SEOHead from '@/components/Common/SEOHead';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import PublicLayout from '@/layouts/PublicLayout';
import { getImageUrl } from '@/lib/image-utils';
import { Link } from '@inertiajs/react';
import { ArrowLeft, Camera, ExternalLink, Globe, Mail, MapPin, Phone, Star, Users } from 'lucide-react';
import { useState } from 'react';

interface ContactInfo {
    type: string;
    value: string;
    label?: string;
}

interface Potential {
    id: number;
    name: string;
    type: 'tourism' | 'umkm';
    description: string;
    images: string[];
    contact_info: ContactInfo | null;
    location?: string;
    is_featured: boolean;
    featured_image?: string;
    type_name: string;
}

interface Props {
    potential: Potential;
    relatedPotentials: Potential[];
    seoMeta?: {
        title: string;
        description: string;
        image?: string;
        url?: string;
        type?: string;
        siteName?: string;
        locale?: string;
    };
    structuredData?: object;
}

export default function PotentialShow({ potential, relatedPotentials, seoMeta, structuredData }: Props) {
    const [selectedImageIndex, setSelectedImageIndex] = useState(0);
    const images = potential.images || [];

    const getContactIcon = (type: string) => {
        switch (type.toLowerCase()) {
            case 'phone':
            case 'telepon':
            case 'whatsapp':
                return <Phone className="h-4 w-4" />;
            case 'email':
                return <Mail className="h-4 w-4" />;
            case 'website':
            case 'web':
                return <Globe className="h-4 w-4" />;
            case 'instagram':
            case 'facebook':
                return <Users className="h-4 w-4" />;
            default:
                return <ExternalLink className="h-4 w-4" />;
        }
    };

    const formatContactValue = (contact: ContactInfo) => {
        if (contact.type.toLowerCase() === 'whatsapp' || contact.type.toLowerCase() === 'phone') {
            // Format phone number for WhatsApp link
            const cleanNumber = contact.value.replace(/\D/g, '');
            const formattedNumber = cleanNumber.startsWith('0') ? '62' + cleanNumber.substring(1) : cleanNumber;
            return `https://wa.me/${formattedNumber}`;
        }
        if (contact.type.toLowerCase() === 'email') {
            return `mailto:${contact.value}`;
        }
        if (contact.type.toLowerCase().includes('website') || contact.type.toLowerCase() === 'web') {
            return contact.value.startsWith('http') ? contact.value : `https://${contact.value}`;
        }
        return contact.value;
    };

    return (
        <>
            <SEOHead
                title={seoMeta?.title}
                description={seoMeta?.description}
                image={seoMeta?.image}
                url={seoMeta?.url}
                type={seoMeta?.type}
                siteName={seoMeta?.siteName}
                locale={seoMeta?.locale}
                structuredData={structuredData}
            />
            <PublicLayout>
                <div className="min-h-screen bg-gray-50 dark:bg-gray-950">
                    {/* Breadcrumb */}
                    <div className="border-b bg-white dark:bg-gray-900">
                        <div className="container mx-auto px-4 py-4">
                            <div className="flex items-center space-x-2 text-sm text-gray-600">
                                <Link href="/" className="hover:text-green-600">
                                    Beranda
                                </Link>
                                <span>/</span>
                                <Link href="/potensi" className="hover:text-green-600">
                                    Potensi
                                </Link>
                                <span>/</span>
                                <span className="text-gray-900">{potential.name}</span>
                            </div>
                        </div>
                    </div>

                    <div className="container mx-auto px-4 py-8">
                        {/* Back Button */}
                        <Link href="/potensi">
                            <Button variant="outline" className="mb-6">
                                <ArrowLeft className="mr-2 h-4 w-4" />
                                Kembali ke Potensi
                            </Button>
                        </Link>

                        <div className="grid grid-cols-1 gap-8 lg:grid-cols-3">
                            {/* Main Content */}
                            <div className="lg:col-span-2">
                                {/* Header */}
                                <div className="mb-6">
                                    <div className="mb-4 flex flex-wrap items-center gap-2">
                                        <Badge className={`${potential.type === 'tourism' ? 'bg-green-500' : 'bg-blue-500'} text-white`}>
                                            {potential.type_name}
                                        </Badge>
                                        {potential.is_featured && (
                                            <Badge className="bg-yellow-500 text-white">
                                                <Star className="mr-1 h-3 w-3" />
                                                Unggulan
                                            </Badge>
                                        )}
                                    </div>

                                    <h1 className="mb-2 text-3xl font-bold text-gray-900 md:text-4xl dark:text-white">{potential.name}</h1>

                                    {potential.location && (
                                        <div className="flex items-center text-gray-600 dark:text-gray-300">
                                            <MapPin className="mr-2 h-4 w-4" />
                                            <span>{potential.location}</span>
                                        </div>
                                    )}
                                </div>

                                {/* Image Gallery */}
                                {images.length > 0 && (
                                    <div className="mb-8">
                                        <div className="relative mb-4 rounded-lg bg-gray-100">
                                            <img
                                                src={`/storage/${images[selectedImageIndex]}`}
                                                alt={`${potential.name} - Gambar ${selectedImageIndex + 1}`}
                                                className="h-64 w-full rounded-lg object-cover shadow-lg md:h-96"
                                                onError={(e) => {
                                                    const target = e.target as HTMLImageElement;
                                                    target.style.display = 'none';
                                                    const parent = target.parentElement;
                                                    if (parent) {
                                                        parent.innerHTML = `
                                                            <div class="h-64 md:h-96 flex flex-col items-center justify-center text-gray-400">
                                                                <svg class="h-12 w-12 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                                                                </svg>
                                                                <div class="text-center">Gagal memuat gambar</div>
                                                            </div>
                                                        `;
                                                    }
                                                }}
                                            />
                                        </div>

                                        {images.length > 1 && (
                                            <div className="flex gap-2 overflow-x-auto pb-2">
                                                {images.map((image, index) => (
                                                    <button
                                                        key={index}
                                                        onClick={() => setSelectedImageIndex(index)}
                                                        className={`h-20 w-20 flex-shrink-0 overflow-hidden rounded-lg border-2 transition-all ${
                                                            selectedImageIndex === index
                                                                ? 'border-green-500'
                                                                : 'border-gray-200 hover:border-gray-300'
                                                        }`}
                                                    >
                                                        <img
                                                            src={`/storage/${image}`}
                                                            alt={`Thumbnail ${index + 1}`}
                                                            className="h-full w-full object-cover"
                                                            onError={(e) => {
                                                                const target = e.target as HTMLImageElement;
                                                                target.style.display = 'none';
                                                                const parent = target.parentElement;
                                                                if (parent) {
                                                                    parent.innerHTML = `
                                                                        <div class="h-full w-full flex items-center justify-center text-gray-400 bg-gray-100">
                                                                            <svg class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                                                                            </svg>
                                                                        </div>
                                                                    `;
                                                                }
                                                            }}
                                                        />
                                                    </button>
                                                ))}
                                            </div>
                                        )}
                                    </div>
                                )}

                                {/* Description */}
                                <Card className="mb-8 dark:bg-gray-800">
                                    <CardHeader>
                                        <CardTitle className="dark:text-white">Deskripsi</CardTitle>
                                    </CardHeader>
                                    <CardContent>
                                        <div
                                            className="prose prose-gray dark:prose-invert max-w-none"
                                            dangerouslySetInnerHTML={{ __html: potential.description }}
                                        />
                                    </CardContent>
                                </Card>
                            </div>

                            {/* Sidebar */}
                            <div className="lg:col-span-1">
                                {/* Contact Information */}
                                {potential.contact_info && (
                                    <Card className="mb-6 dark:bg-gray-800">
                                        <CardHeader>
                                            <CardTitle className="dark:text-white">Informasi Kontak</CardTitle>
                                            <CardDescription className="dark:text-gray-300">Hubungi untuk informasi lebih lanjut</CardDescription>
                                        </CardHeader>
                                        <CardContent>
                                            <div className="flex items-center justify-between">
                                                <div className="flex items-center space-x-3">
                                                    {getContactIcon(potential.contact_info.type)}
                                                    <div>
                                                        <div className="text-sm font-medium dark:text-white">
                                                            {potential.contact_info.label || potential.contact_info.type}
                                                        </div>
                                                        <div className="text-sm text-gray-600 dark:text-gray-300">{potential.contact_info.value}</div>
                                                    </div>
                                                </div>

                                                <a
                                                    href={formatContactValue(potential.contact_info)}
                                                    target="_blank"
                                                    rel="noopener noreferrer"
                                                    className="text-green-600 hover:text-green-700"
                                                >
                                                    <ExternalLink className="h-4 w-4" />
                                                </a>
                                            </div>
                                        </CardContent>
                                    </Card>
                                )}

                                {/* Call to Action */}
                                <Card className="mb-6 dark:bg-gray-800">
                                    <CardContent className="pt-6">
                                        <h3 className="mb-3 font-semibold dark:text-white">Tertarik untuk berkunjung?</h3>
                                        <p className="mb-4 text-sm text-gray-600 dark:text-gray-300">
                                            Hubungi kami untuk informasi lebih lanjut atau bantuan perencanaan kunjungan.
                                        </p>
                                        <div className="space-y-4">
                                            <Link href="/layanan">
                                                <Button className="mb-2 w-full bg-green-600 hover:bg-green-700">Layanan Desa</Button>
                                            </Link>
                                            <Link href="/profil">
                                                <Button variant="outline" className="w-full">
                                                    Profil Desa
                                                </Button>
                                            </Link>
                                        </div>
                                    </CardContent>
                                </Card>
                            </div>
                        </div>

                        {/* Related Potentials */}
                        {relatedPotentials.length > 0 && (
                            <div className="mt-12">
                                <h2 className="mb-6 text-2xl font-bold dark:text-white">
                                    {potential.type === 'tourism' ? 'Wisata Lainnya' : 'UMKM Lainnya'}
                                </h2>

                                <div className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-4">
                                    {relatedPotentials.map((related) => (
                                        <Card key={related.id} className="group transition-all duration-300 hover:shadow-lg dark:bg-gray-800">
                                            <div className="relative">
                                                {related.featured_image ? (
                                                    <img
                                                        src={getImageUrl(related.featured_image, 'medium')}
                                                        alt={related.name}
                                                        className="h-32 w-full object-cover transition-transform duration-300 group-hover:scale-105"
                                                        loading="lazy"
                                                    />
                                                ) : (
                                                    <div className="flex h-32 w-full items-center justify-center bg-gray-200">
                                                        <Camera className="h-8 w-8 text-gray-400" />
                                                    </div>
                                                )}
                                                {related.is_featured && (
                                                    <Badge className="absolute top-2 right-2 bg-yellow-500 text-xs text-white">
                                                        <Star className="mr-1 h-2 w-2" />
                                                        Unggulan
                                                    </Badge>
                                                )}
                                            </div>

                                            <CardContent className="p-4">
                                                <h3 className="mb-2 text-sm font-semibold transition-colors group-hover:text-green-600 dark:text-white">
                                                    {related.name}
                                                </h3>
                                                <p className="mb-3 line-clamp-2 text-xs text-gray-600 dark:text-gray-300">
                                                    {related.description.replace(/<[^>]*>/g, '').substring(0, 80)}...
                                                </p>
                                                <Link href={route('potential.show', related.id)}>
                                                    <Button size="sm" className="w-full bg-green-600 hover:bg-green-700">
                                                        Lihat Detail
                                                    </Button>
                                                </Link>
                                            </CardContent>
                                        </Card>
                                    ))}
                                </div>
                            </div>
                        )}
                    </div>
                </div>
            </PublicLayout>
        </>
    );
}
