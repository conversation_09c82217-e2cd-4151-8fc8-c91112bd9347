import { useEffect, useState } from 'react';

interface PreloaderProps {
    onComplete?: () => void;
    minDuration?: number;
}

export default function Preloader({ onComplete, minDuration = 2000 }: PreloaderProps) {
    const [isVisible, setIsVisible] = useState(true);
    const [progress, setProgress] = useState(0);

    useEffect(() => {
        // Simulate loading progress
        const progressInterval = setInterval(() => {
            setProgress((prev) => {
                if (prev >= 100) {
                    clearInterval(progressInterval);
                    return 100;
                }
                return prev + Math.random() * 15;
            });
        }, 100);

        // Minimum duration timer
        const minTimer = setTimeout(() => {
            setProgress(100);
        }, minDuration);

        return () => {
            clearInterval(progressInterval);
            clearTimeout(minTimer);
        };
    }, [minDuration]);

    useEffect(() => {
        if (progress >= 100) {
            const fadeTimer = setTimeout(() => {
                setIsVisible(false);
                setTimeout(() => {
                    onComplete?.();
                }, 500); // Wait for fade out animation
            }, 300);

            return () => clearTimeout(fadeTimer);
        }
    }, [progress, onComplete]);

    if (!isVisible) return null;

    return (
        <div
            className={`fixed inset-0 z-50 flex flex-col items-center justify-center bg-gradient-to-br from-blue-50 via-white to-green-50 transition-opacity duration-500 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900 ${
                progress >= 100 ? 'opacity-0' : 'opacity-100'
            }`}
        >
            {/* Animated background waves */}
            <div className="absolute inset-0 overflow-hidden">
                <div className="absolute -top-40 -left-40 h-80 w-80 animate-pulse rounded-full bg-blue-200/30 dark:bg-blue-800/20"></div>
                <div className="animation-delay-1000 absolute -right-40 -bottom-40 h-80 w-80 animate-pulse rounded-full bg-green-200/30 dark:bg-green-800/20"></div>
                <div className="animation-delay-500 absolute top-1/2 left-1/2 h-60 w-60 -translate-x-1/2 -translate-y-1/2 transform animate-ping rounded-full bg-blue-100/20 dark:bg-blue-700/10"></div>
            </div>

            {/* Main content */}
            <div className="relative z-10 flex flex-col items-center space-y-8">
                {/* Logo with animation */}
                <div className="relative">
                    <div className="absolute inset-0 animate-ping rounded-full bg-blue-500/20 dark:bg-blue-400/20"></div>
                    {/* <div className="relative flex h-50 w-50 animate-bounce items-center justify-center rounded-full bg-gradient-to-br from-blue-600 to-green-600 shadow-2xl dark:from-blue-500 dark:to-green-500">
                        <AppLogoIcon className="h-12 w-12 animate-pulse fill-white" />
                    </div> */}
                    <img className="w-20 animate-pulse fill-white" src="/images/logo-kabupaten-bogor.png" />
                </div>

                {/* App name */}
                <div className="space-y-2 text-center">
                    <h1 className="animate-fade-in text-3xl font-bold text-gray-800 dark:text-white">
                        {import.meta.env.VITE_APP_NAME || 'Desa Lemah Duhur'}
                    </h1>
                    <p className="animate-fade-in animation-delay-300 text-lg text-gray-600 dark:text-gray-300">Portal Digital Desa</p>
                </div>

                {/* Progress bar */}
                <div className="h-2 w-64 overflow-hidden rounded-full bg-gray-200 dark:bg-gray-700">
                    <div
                        className="h-full rounded-full bg-gradient-to-r from-blue-500 to-green-500 transition-all duration-300 ease-out"
                        style={{ width: `${Math.min(progress, 100)}%` }}
                    ></div>
                </div>

                {/* Loading text */}
                <div className="flex items-center space-x-2 text-gray-600 dark:text-gray-400">
                    <div className="flex space-x-1">
                        <div className="h-2 w-2 animate-bounce rounded-full bg-blue-500"></div>
                        <div className="animation-delay-200 h-2 w-2 animate-bounce rounded-full bg-blue-500"></div>
                        <div className="animation-delay-400 h-2 w-2 animate-bounce rounded-full bg-blue-500"></div>
                    </div>
                    <span className="text-sm font-medium">Memuat...</span>
                </div>
            </div>

            {/* Floating particles */}
            <div className="pointer-events-none absolute inset-0">
                {[...Array(6)].map((_, i) => (
                    <div
                        key={i}
                        className={`animate-float absolute h-2 w-2 rounded-full bg-blue-400/30 dark:bg-blue-300/20`}
                        style={{
                            left: `${20 + i * 15}%`,
                            top: `${30 + (i % 3) * 20}%`,
                            animationDelay: `${i * 0.5}s`,
                            animationDuration: `${3 + i * 0.5}s`,
                        }}
                    ></div>
                ))}
            </div>
        </div>
    );
}
