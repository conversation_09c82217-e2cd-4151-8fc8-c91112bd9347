# Design Document - Website Profil Desa Lemah Duhur

## Overview

Website profil Desa Lemah Duhur akan dibangun menggunakan arsitektur modern dengan Laravel sebagai backend API, Inertia.js sebagai bridge, dan <PERSON>act dengan TailwindCSS untuk frontend. Design ini mengutamakan kesederhanaan, performa optimal untuk 100 pengguna/hari, kemudahan maintenance, dan penghormatan terhadap kekayaan budaya dan sejarah desa.

**Design Rationale**: Pemilihan teknologi stack ini didasarkan pada kebutuhan untuk menciptakan website yang mudah dikelola oleh perangkat desa, responsif untuk pengguna mobile (prioritas utama), dan mampu menampilkan konten budaya dengan cara yang menghormati tradisi lokal. SQLite dipilih untuk kesederhanaan deployment di hosting shared, sementara Inertia.js memungkinkan pengalaman SPA tanpa kompleksitas API terpisah.

## Architecture

### Technology Stack
- **Backend**: Laravel 12 (PHP 8.2+)
- **Frontend**: React 19 dengan TypeScript
- **Bridge**: Inertia.js 2.0
- **Styling**: TailwindCSS 4.0 + Radix UI components
- **Database**: SQLite (sesuai untuk skala kecil)
- **Build Tool**: Vite 6.0
- **Icons**: Lucide React

### System Architecture
```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   React Client  │◄──►│   Inertia.js     │◄──►│  Laravel API    │
│   (Frontend)    │    │   (Bridge)       │    │   (Backend)     │
└─────────────────┘    └──────────────────┘    └─────────────────┘
                                                        │
                                                        ▼
                                                ┌─────────────────┐
                                                │  SQLite DB      │
                                                │  (Data Layer)   │
                                                └─────────────────┘
```

### Skalabilitas Masa Depan
1. **Database Migration Path**: SQLite → MySQL/PostgreSQL jika traffic meningkat
2. **Caching Strategy**: File-based cache → Redis jika diperlukan
3. **CDN Integration**: Siap untuk integrasi dengan CDN untuk aset statis
4. **API-First Design**: Memungkinkan pengembangan mobile app di masa depan

## Components and Interfaces

### Frontend Components Structure
```
src/
├── Components/
│   ├── Layout/
│   │   ├── Header.tsx
│   │   ├── Footer.tsx
│   │   ├── Navigation.tsx
│   │   └── Sidebar.tsx (Admin)
│   ├── UI/ (Radix UI wrappers)
│   │   ├── Button.tsx
│   │   ├── Card.tsx
│   │   ├── Dialog.tsx
│   │   └── Form/
│   ├── Features/
│   │   ├── News/
│   │   ├── Profile/
│   │   ├── Services/
│   │   ├── Potential/
│   │   └── Complaints/
│   └── Common/
│       ├── SEOHead.tsx
│       ├── ImageOptimized.tsx
│       └── LoadingSpinner.tsx
├── Pages/
│   ├── Public/
│   │   ├── Home.tsx
│   │   ├── Profile.tsx
│   │   ├── Services.tsx
│   │   ├── News/
│   │   ├── Potential.tsx
│   │   ├── Complaints.tsx
│   │   └── ComplaintTracking.tsx
│   └── Admin/
│       ├── Dashboard.tsx
│       ├── NewsManagement.tsx
│       └── ContentManagement.tsx
└── Types/
    ├── News.ts
    ├── Profile.ts
    ├── Services.ts
    └── Complaints.ts
```

### Backend API Structure
```
app/
├── Http/
│   ├── Controllers/
│   │   ├── PublicController.php
│   │   ├── NewsController.php
│   │   ├── ProfileController.php
│   │   ├── ComplaintController.php
│   │   └── Admin/
│   │       ├── DashboardController.php
│   │       └── ContentController.php
│   └── Middleware/
│       └── (Standard Laravel middleware)
├── Models/
│   ├── News.php
│   ├── Profile.php
│   ├── Service.php
│   ├── Potential.php
│   ├── Complaint.php
│   └── User.php
└── Services/
    ├── ImageOptimizationService.php
    ├── SEOService.php
    └── ContactInfoService.php
```

## Data Models

### News Model
```php
class News extends Model
{
    protected $fillable = [
        'title',
        'slug',
        'content',
        'excerpt',
        'featured_image',
        'category',
        'published_at',
        'is_published',
        'meta_title',
        'meta_description'
    ];
    
    protected $casts = [
        'published_at' => 'datetime',
        'is_published' => 'boolean'
    ];
}
```

### Profile Model
```php
class Profile extends Model
{
    protected $fillable = [
        'section', // 'history', 'vision_mission', 'demographics', etc.
        'title',
        'content',
        'image',
        'order'
    ];
}
```

### Service Model
```php
class Service extends Model
{
    protected $fillable = [
        'name',
        'description',
        'requirements',
        'procedure',
        'cost',
        'processing_time',
        'contact_info',
        'is_active'
    ];
    
    protected $casts = [
        'requirements' => 'array',
        'procedure' => 'array',
        'is_active' => 'boolean'
    ];
}
```

### Potential Model
```php
class Potential extends Model
{
    protected $fillable = [
        'name',
        'type', // 'tourism', 'umkm'
        'description',
        'images',
        'contact_info',
        'location',
        'is_featured'
    ];
    
    protected $casts = [
        'images' => 'array',
        'contact_info' => 'array',
        'is_featured' => 'boolean'
    ];
}
```

### Complaint Model
```php
class Complaint extends Model
{
    protected $fillable = [
        'ticket_number',
        'name',
        'email',
        'phone',
        'category',
        'subject',
        'description',
        'attachments',
        'status',
        'priority',
        'admin_response',
        'responded_at',
        'responded_by'
    ];
    
    protected $casts = [
        'attachments' => 'array',
        'responded_at' => 'datetime'
    ];
    
    // Status: pending, in_progress, resolved, closed
    // Priority: low, medium, high, urgent
    // Category: infrastruktur, pelayanan, lingkungan, sosial, lainnya
}
```

## Arsitektur Halaman

### 1. Halaman Beranda (/)
- **Hero Section**: Gambar desa + sambutan kepala desa
- **Berita Terbaru**: 3-4 artikel terbaru dengan thumbnail
- **Layanan Unggulan**: Quick access ke layanan populer
- **Potensi Desa**: Showcase wisata/UMKM unggulan
- **Kontak & Lokasi**: Info dasar kontak desa

### 2. Profil Desa (/profil)
- **Etimologi & Legenda**: Penjelasan nama desa dan cerita kuda terbang
- **Sejarah Desa**: Timeline sejarah sejak 1910-1920 dengan proses "lelemah di luhur"
- **Visi & Misi**: Statement visi misi desa
- **Struktur Organisasi**: Foto + jabatan perangkat desa
- **Data Demografis**: Statistik penduduk dalam chart sederhana
- **Geografis**: Peta lokasi + alamat lengkap + link Google Maps

### 3. Layanan (/layanan)
- **Daftar Layanan**: Grid card layanan tersedia
- **Detail Layanan**: Modal/page dengan prosedur lengkap
- **Jam Operasional**: Info waktu pelayanan
- **Kontak Pelayanan**: Info kontak untuk setiap layanan

### 4. Berita (/berita)
- **Daftar Berita**: List dengan pagination
- **Kategori Filter**: Filter berdasarkan kategori
- **Detail Berita**: Full article dengan sharing buttons
- **Berita Terkait**: Suggestion di akhir artikel

### 5. Potensi Desa (/potensi)
- **Wisata**: Gallery foto + deskripsi tempat wisata
- **UMKM**: Showcase produk lokal + kontak
- **Peta Potensi**: Lokasi wisata/UMKM di peta
- **Kontak Kerjasama**: Info untuk investor/partner

### 6. Pengaduan Masyarakat (/pengaduan)
- **Form Pengaduan**: Form dengan field nama, kontak, kategori, subjek, deskripsi
- **Upload Lampiran**: Fitur upload foto/dokumen pendukung (opsional)
- **Tracking Pengaduan**: Input nomor tiket untuk cek status pengaduan
- **Kategori Pengaduan**: Infrastruktur, pelayanan, lingkungan, sosial, lainnya
- **Konfirmasi Pengiriman**: Tampilan nomor tiket dan estimasi waktu respon

### 7. Dashboard Pengelolaan (/dashboard)
- **Dashboard**: Statistik konten + quick actions untuk pengelolaan
- **Manajemen Berita**: CRUD berita dengan rich editor
- **Manajemen Konten**: Edit profil, layanan, potensi
- **Manajemen Pengaduan**: Dashboard pengaduan dengan filter status dan prioritas
- **Media Library**: Upload & manage images
- **SEO Settings**: Meta tags untuk setiap halaman

## Isi Konten Setiap Halaman

### Konten Beranda
- Sambutan Kepala Desa (max 200 kata)
- 4 berita terbaru dengan thumbnail 300x200px
- 6 layanan unggulan dengan icon
- 4 potensi unggulan dengan foto 400x300px
- Info kontak: alamat, telepon, email, jam pelayanan

### Konten Profil
- **Etimologi & Legenda**: Penjelasan nama "Lemah Duhur" dan legenda kuda terbang (200-300 kata)
- **Sejarah Desa**: Timeline sejarah sejak 1910-1920 dengan proses "lelemah di luhur" (500-800 kata) dengan 2-3 foto historis
- **Visi & Misi**: Statement visi misi desa (1 paragraf visi, 3-5 poin misi)
- **Struktur Organisasi**: Foto 200x250px + nama + jabatan untuk 8-12 perangkat desa
- **Data Demografis**: Statistik penduduk, KK, usia, pendidikan, pekerjaan dalam format chart
- **Geografis**: Alamat lengkap Kecamatan Caringin, Kabupaten Bogor, Jawa Barat + link Google Maps + peta wilayah

### Konten Layanan
- 10-15 layanan utama meliputi:
  - Surat keterangan domisili
  - Surat keterangan tidak mampu
  - Surat pengantar KTP/KK
  - Surat keterangan usaha
  - Surat keterangan kelahiran/kematian
- Setiap layanan: prosedur (3-5 langkah), persyaratan (list), biaya, waktu proses

### Konten Berita
- Target 2-3 artikel per minggu
- Kategori: pengumuman, kegiatan, pembangunan, sosial
- Panjang artikel 300-600 kata
- Foto artikel 800x450px
- Meta description 150-160 karakter

### Konten Potensi
- Wisata: 3-5 lokasi dengan foto gallery, deskripsi, akses, fasilitas
- UMKM: 8-12 produk/usaha dengan foto produk, kontak, deskripsi
- Setiap item: foto utama 600x400px + gallery 3-5 foto

### Konten Pengaduan
- **Form Pengaduan**:
  - Field wajib: nama lengkap, email/telepon, kategori, subjek, deskripsi
  - Field opsional: lampiran foto/dokumen (max 5MB, format JPG/PNG/PDF)
  - Dropdown kategori: Infrastruktur, Pelayanan Publik, Lingkungan, Sosial Kemasyarakatan, Lainnya
  - Text area deskripsi dengan minimum 50 karakter
- **Tracking System**:
  - Generate nomor tiket unik format: LDH-YYYYMMDD-XXX
  - Status tracking: Diterima, Diproses, Ditindaklanjuti, Selesai
  - Estimasi waktu respon: 3-7 hari kerja
- **Admin Dashboard Pengaduan**:
  - Filter berdasarkan status, kategori, prioritas, tanggal
  - Bulk actions untuk update status
  - Template respon untuk kategori umum
  - Export laporan pengaduan bulanan

## Cultural Context and Content Guidelines

### Etimologi dan Legenda Desa (Requirement 7)
**Design Rationale**: Konten budaya dan sejarah harus disajikan dengan pendekatan yang menghormati tradisi lokal dan menggunakan bahasa yang tepat untuk menceritakan warisan budaya desa.

#### Konten Etimologi
- **Nama "Lemah Duhur"**: Penjelasan makna "Lemah" (tanah datar) dan "Duhur/Luhur" (tempat tinggi)
- **Proses Historis**: Dokumentasi proses "lelemah di luhur" - perataan dataran tinggi berbukit untuk pemukiman
- **Konteks Geografis**: Hubungan nama dengan kondisi geografis wilayah Kecamatan Caringin, Kabupaten Bogor

#### Legenda Lokal
- **Cerita Kuda Terbang**: Narasi tentang makhluk mistis yang menggunakan area sebagai tempat pendaratan kuda terbang
- **Pendekatan Naratif**: Menggunakan bahasa yang menghormati kepercayaan lokal tanpa mengurangi nilai historis
- **Integrasi dengan Sejarah**: Menghubungkan legenda dengan perkembangan administratif desa

#### Sejarah Administratif
- **Periode Pendirian**: Dokumentasi pendirian desa pada 1910-1920
- **Evolusi Wilayah**: Penjelasan tentang pemisahan wilayah menjadi Desa Cimande Jaya dan Desa Lemah Duhur
- **Perkembangan Modern**: Timeline perkembangan desa hingga saat ini

### Bahasa dan Tone Guidelines
1. **Bahasa Indonesia Formal**: Semua konten menggunakan Bahasa Indonesia yang baik dan benar
2. **Tone Menghormati**: Penggunaan bahasa yang menghormati tradisi dan adat istiadat
3. **Terminologi Desa**: Penggunaan istilah administratif desa yang tepat
4. **Konteks Budaya**: Penyajian informasi dengan mempertimbangkan sensitivitas budaya lokal

## Error Handling

### Frontend Error Handling
```typescript
// Global error boundary untuk React
class ErrorBoundary extends Component {
  // Handle JavaScript errors
}

// API error handling dengan Inertia
const handleApiError = (error: AxiosError) => {
  if (error.response?.status === 404) {
    // Redirect to 404 page
  }
  if (error.response?.status === 500) {
    // Show generic error message
  }
}
```

### Backend Error Handling
```php
// Custom exception handler
class Handler extends ExceptionHandler
{
    public function render($request, Throwable $exception)
    {
        if ($request->wantsJson()) {
            return response()->json([
                'message' => 'Terjadi kesalahan sistem'
            ], 500);
        }
        
        return parent::render($request, $exception);
    }
}
```

### Error Pages
- **404**: Halaman tidak ditemukan dengan navigasi kembali
- **500**: Error server dengan kontak admin
- **403**: Akses ditolak untuk area admin

## Authentication and Security

### Admin Authentication System
```php
// Middleware untuk proteksi area admin
class AdminAuth
{
    public function handle($request, Closure $next)
    {
        if (!auth()->check() || !auth()->user()->is_admin) {
            return redirect()->route('login');
        }
        return $next($request);
    }
}
```

### Security Measures
1. **CSRF Protection**: Token validation untuk semua form submissions
2. **Session Management**: Secure session handling dengan timeout
3. **File Upload Security**: Validation dan sanitization untuk upload gambar
4. **SQL Injection Prevention**: Eloquent ORM dengan prepared statements
5. **XSS Protection**: Input sanitization dan output escaping
6. **Access Control**: Role-based access untuk area admin

### Data Validation
- **Frontend Validation**: Real-time validation dengan React Hook Form
- **Backend Validation**: Laravel Form Request validation
- **Image Validation**: File type, size, dan dimension validation
- **Content Validation**: HTML sanitization untuk rich text content

## Testing Strategy

### Unit Testing (Backend)
- Model validation testing
- Service class testing
- API endpoint testing
- Database seeder testing

### Feature Testing (Full Stack)
- User journey testing dengan Pest
- Admin functionality testing
- Form submission testing
- File upload testing

### Performance Testing
- Page load speed testing (target < 3 detik)
- Image optimization testing
- Database query optimization
- Mobile responsiveness testing

### SEO Testing
- Meta tags validation
- Structured data testing
- Sitemap generation testing
- Page speed insights monitoring

## Konten & SEO Blueprint

### SEO Strategy
1. **On-Page SEO**:
   - Title tags: "Judul Halaman | Desa Lemah Duhur"
   - Meta descriptions unik untuk setiap halaman
   - H1-H6 hierarchy yang proper
   - Alt text untuk semua gambar
   - Internal linking strategy

2. **Technical SEO**:
   - XML sitemap auto-generated
   - Robots.txt optimization
   - Schema.org markup untuk organization
   - Open Graph tags untuk social sharing
   - Canonical URLs

3. **Content SEO**:
   - Target keywords: "desa lemah duhur", "profil desa", "layanan desa"
   - Long-tail keywords untuk setiap layanan
   - Local SEO optimization
   - Regular content updates melalui berita

### Performance Optimization
1. **Image Optimization**:
   - WebP format dengan fallback
   - Lazy loading untuk images
   - Responsive images dengan srcset
   - Compression otomatis saat upload

2. **Code Optimization**:
   - CSS/JS minification
   - Tree shaking untuk unused code
   - Code splitting untuk pages
   - Gzip compression

3. **Caching Strategy**:
   - Browser caching untuk static assets
   - Page caching untuk public pages
   - Database query caching
   - Image caching dengan proper headers

### Accessibility
- WCAG 2.1 AA compliance
- Keyboard navigation support
- Screen reader compatibility
- Color contrast ratio > 4.5:1
- Focus indicators yang jelas
- Alt text untuk semua images

### Mobile-First Design
- Responsive breakpoints: 320px, 768px, 1024px, 1280px
- Touch-friendly button sizes (min 44px)
- Optimized mobile navigation
- Fast mobile loading (< 2 detik)
- Mobile-specific optimizations