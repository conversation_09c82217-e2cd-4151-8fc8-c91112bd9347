<?php

namespace Tests\Unit;

use App\Services\ImageOptimizationService;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;
use Tests\TestCase;

class ImageOptimizationServiceTest extends TestCase
{
    protected ImageOptimizationService $imageService;

    protected function setUp(): void
    {
        parent::setUp();
        Storage::fake('public');
        $this->imageService = new ImageOptimizationService;
    }

    public function test_can_validate_image_file_correctly()
    {
        // Create a fake image file
        $file = UploadedFile::fake()->image('test.jpg', 800, 600)->size(1024); // 1MB

        // This should not throw an exception
        $result = $this->imageService->uploadAndOptimize($file, 'test');

        $this->assertIsArray($result);
        $this->assertArrayHasKey('original', $result);
        $this->assertArrayHasKey('medium', $result);
        $this->assertArrayHasKey('thumbnail', $result);
    }

    public function test_rejects_invalid_file_types()
    {
        // Create a fake non-image file
        $file = UploadedFile::fake()->create('test.txt', 100);

        $this->expectException(\InvalidArgumentException::class);
        $this->expectExceptionMessage('Format file tidak didukung');

        $this->imageService->uploadAndOptimize($file, 'test');
    }

    public function test_rejects_oversized_files()
    {
        // Create a fake large image file (15MB)
        $file = UploadedFile::fake()->image('large.jpg', 2000, 2000)->size(15360);

        $this->expectException(\InvalidArgumentException::class);
        $this->expectExceptionMessage('Ukuran file terlalu besar');

        $this->imageService->uploadAndOptimize($file, 'test');
    }

    public function test_generates_correct_file_paths()
    {
        $file = UploadedFile::fake()->image('test.jpg', 800, 600)->size(1024);

        $result = $this->imageService->uploadAndOptimize($file, 'news');

        $this->assertStringContainsString('news/original/', $result['original']['path']);
        $this->assertStringContainsString('news/medium/', $result['medium']['path']);
        $this->assertStringContainsString('news/thumbnail/', $result['thumbnail']['path']);
    }

    public function test_can_delete_image_variants()
    {
        $file = UploadedFile::fake()->image('test.jpg', 800, 600)->size(1024);

        $result = $this->imageService->uploadAndOptimize($file, 'test');
        $imagePath = $result['original']['path'];

        // Verify files exist
        $this->assertTrue(Storage::disk('public')->exists($imagePath));

        // Delete the image
        $deleted = $this->imageService->deleteImage($imagePath);

        $this->assertTrue($deleted);
    }

    public function test_generates_srcset_correctly()
    {
        $imagePaths = [
            'thumbnail' => ['url' => '/storage/test/thumbnail/image.webp'],
            'medium' => ['url' => '/storage/test/medium/image.webp'],
            'original' => ['url' => '/storage/test/original/image.webp'],
        ];

        $srcSet = $this->imageService->generateSrcSet($imagePaths);

        $this->assertStringContainsString('300w', $srcSet);
        $this->assertStringContainsString('800w', $srcSet);
        $this->assertStringContainsString('1200w', $srcSet);
    }
}
