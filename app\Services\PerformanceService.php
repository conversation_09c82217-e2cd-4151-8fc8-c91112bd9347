<?php

namespace App\Services;

use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class PerformanceService
{
    /**
     * Warm up application cache
     */
    public function warmUpCache(): void
    {
        Log::info('Starting cache warm-up...');

        // Cache common queries
        $this->cacheCommonQueries();

        // Cache configuration
        $this->cacheConfiguration();

        Log::info('Cache warm-up completed.');
    }

    /**
     * Cache common database queries
     */
    private function cacheCommonQueries(): void
    {
        // Cache latest news
        Cache::remember('homepage_news', 1800, function () {
            return \App\Models\News::published()
                ->latestPublished()
                ->select(['id', 'title', 'slug', 'excerpt', 'featured_image', 'published_at', 'category'])
                ->limit(4)
                ->get();
        });

        // Cache active services
        Cache::remember('homepage_services', 3600, function () {
            return \App\Models\Service::active()
                ->select(['id', 'name', 'description', 'cost'])
                ->limit(6)
                ->get();
        });

        // Cache featured potentials
        Cache::remember('homepage_potentials', 3600, function () {
            return \App\Models\Potential::featured()
                ->select(['id', 'name', 'type', 'description', 'images'])
                ->limit(4)
                ->get();
        });

        // Cache news categories
        Cache::remember('news_categories', 3600, function () {
            return \App\Models\News::published()
                ->distinct()
                ->pluck('category')
                ->filter()
                ->sort()
                ->values();
        });

        // Cache services list
        Cache::remember('services_list', 3600, function () {
            return \App\Models\Service::active()
                ->orderBy('name')
                ->get()
                ->map(function ($service) {
                    return [
                        'id' => $service->id,
                        'name' => $service->name,
                        'description' => $service->description,
                        'cost' => $service->formatted_cost,
                        'processing_time' => $service->processing_time,
                    ];
                });
        });

        // Cache potentials by type
        foreach (['all', 'tourism', 'umkm'] as $type) {
            Cache::remember("potentials_list_{$type}", 3600, function () use ($type) {
                $query = \App\Models\Potential::query();

                if ($type === 'tourism') {
                    $query->tourism();
                } elseif ($type === 'umkm') {
                    $query->umkm();
                }

                return $query->orderBy('is_featured', 'desc')
                    ->orderBy('created_at', 'desc')
                    ->get();
            });
        }
    }

    /**
     * Cache application configuration
     */
    private function cacheConfiguration(): void
    {
        // Cache route list
        Cache::remember('route_list', 86400, function () {
            return app(\Illuminate\Routing\Router::class)->getRoutes();
        });

        // Cache view paths
        Cache::remember('view_paths', 86400, function () {
            return config('view.paths');
        });
    }

    /**
     * Clear all performance caches
     */
    public function clearAllCache(): void
    {
        Log::info('Clearing all performance caches...');

        // Clear application cache
        Cache::flush();

        // Clear config cache
        \Artisan::call('config:clear');

        // Clear route cache
        \Artisan::call('route:clear');

        // Clear view cache
        \Artisan::call('view:clear');

        Log::info('All caches cleared.');
    }

    /**
     * Get cache statistics
     */
    public function getCacheStats(): array
    {
        $stats = [
            'cache_driver' => config('cache.default'),
            'cached_items' => 0,
            'cache_size' => 0,
        ];

        try {
            // Get basic cache info
            $cacheKeys = [
                'homepage_news',
                'homepage_services',
                'homepage_potentials',
                'news_categories',
                'services_list',
                'potentials_list_all',
                'potentials_list_tourism',
                'potentials_list_umkm',
            ];

            $cachedCount = 0;
            foreach ($cacheKeys as $key) {
                if (Cache::has($key)) {
                    $cachedCount++;
                }
            }

            $stats['cached_items'] = $cachedCount;
            $stats['total_keys'] = count($cacheKeys);
            $stats['cache_hit_ratio'] = $cachedCount > 0 ? round(($cachedCount / count($cacheKeys)) * 100, 1) : 0;

        } catch (\Exception $e) {
            Log::warning('Could not retrieve cache stats: '.$e->getMessage());
        }

        return $stats;
    }

    /**
     * Optimize database queries
     */
    public function optimizeDatabase(): void
    {
        Log::info('Starting database optimization...');

        try {
            // Analyze tables for SQLite
            if (config('database.default') === 'sqlite') {
                DB::statement('ANALYZE');
                Log::info('SQLite database analyzed.');
            }

            // Clean up old cache entries
            if (DB::getDriverName() === 'sqlite') {
                DB::table('cache')->where('expiration', '<', time())->delete();
                Log::info('Expired cache entries cleaned up.');
            }

        } catch (\Exception $e) {
            Log::error('Database optimization failed: '.$e->getMessage());
        }

        Log::info('Database optimization completed.');
    }

    /**
     * Get performance metrics
     */
    public function getPerformanceMetrics(): array
    {
        $metrics = [
            'memory_usage' => [
                'current' => memory_get_usage(true),
                'peak' => memory_get_peak_usage(true),
                'limit' => ini_get('memory_limit'),
            ],
            'cache_stats' => $this->getCacheStats(),
            'database_stats' => $this->getDatabaseStats(),
        ];

        return $metrics;
    }

    /**
     * Get database statistics
     */
    private function getDatabaseStats(): array
    {
        try {
            $stats = [
                'driver' => DB::getDriverName(),
                'connection' => config('database.default'),
            ];

            if (DB::getDriverName() === 'sqlite') {
                $dbPath = database_path('database.sqlite');
                if (file_exists($dbPath)) {
                    $stats['size'] = filesize($dbPath);
                    $stats['size_formatted'] = $this->formatBytes($stats['size']);
                }
            }

            // Get table counts
            $tables = ['news', 'services', 'potentials', 'profiles'];
            foreach ($tables as $table) {
                try {
                    $stats['table_counts'][$table] = DB::table($table)->count();
                } catch (\Exception $e) {
                    $stats['table_counts'][$table] = 0;
                }
            }

            return $stats;

        } catch (\Exception $e) {
            Log::warning('Could not retrieve database stats: '.$e->getMessage());

            return ['error' => 'Could not retrieve database statistics'];
        }
    }

    /**
     * Format bytes to human readable format
     */
    private function formatBytes(int $bytes, int $precision = 2): string
    {
        $units = ['B', 'KB', 'MB', 'GB', 'TB'];

        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }

        return round($bytes, $precision).' '.$units[$i];
    }
}
