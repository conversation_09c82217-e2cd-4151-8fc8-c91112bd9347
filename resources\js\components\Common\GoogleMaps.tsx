import { Button } from '@/components/ui/button';
import { MapPin } from 'lucide-react';

interface GoogleMapsProps {
    mapsUrl: string;
    address: string;
    className?: string;
}

export default function GoogleMaps({ mapsUrl, address, className = '' }: GoogleMapsProps) {
    const handleOpenMaps = () => {
        window.open(mapsUrl, '_blank', 'noopener,noreferrer');
    };

    return (
        <div className={`rounded-lg border bg-card p-4 sm:p-6 ${className}`}>
            <div className="mb-4 flex items-center">
                <MapPin className="mr-2 h-5 w-5 text-green-600 dark:text-green-400" />
                <h3 className="text-lg font-semibold text-card-foreground">Lokasi Desa</h3>
            </div>

            <div className="mb-4">
                <p className="text-sm leading-relaxed text-muted-foreground">{address}</p>
            </div>

            <div className="aspect-video w-full overflow-hidden rounded-lg bg-gray-200 dark:bg-gray-800">
                <iframe
                    src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d31699.153229870455!2d106.83014780668466!3d-6.721665019543816!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x2e69cbc5075fff4d%3A0x58b5b338c609fce4!2sLemah%20Duhur%2C%20Caringin%2C%20Bogor%20Regency%2C%20West%20Java!5e0!3m2!1sen!2sid!4v1753147004951!5m2!1sen!2sid"
                    width="100%"
                    height="100%"
                    style={{ border: 0 }}
                    allowFullScreen
                    loading="lazy"
                    referrerPolicy="no-referrer-when-downgrade"
                    title="Peta Lokasi Desa Lemah Duhur"
                    className="h-full w-full"
                />
            </div>

            <div className="mt-4">
                <Button onClick={handleOpenMaps} variant="outline" size="sm" className="w-full touch-manipulation">
                    <MapPin className="mr-2 h-4 w-4" />
                    Buka di Google Maps
                </Button>
            </div>
        </div>
    );
}
