<?php

namespace Database\Seeders;

use App\Models\Setting;
use Illuminate\Database\Seeder;

class SettingSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Operating hours configuration for Desa Lemah Duhur
        Setting::updateOrCreate(
            ['key' => 'village.operating_hours'],
            [
                'value' => [
                    'weekdays' => 'Senin - Jumat: 08:00 - 15:00 WIB',
                    'saturday' => 'Sabtu: 08:00 - 12:00 WIB',
                    'sunday' => 'Minggu: Tutup',
                    'break' => 'Istirahat: 12:00 - 13:00 WIB',
                    'holidays' => 'Hari libur nasional: Tutup',
                ],
                'type' => 'json',
                'description' => 'Jam operasional pelayanan Kantor Desa Lemah Duhur untuk layanan administrasi kepada masyarakat',
            ]
        );

        // Contact information for Desa Lemah Duhur
        Setting::updateOrCreate(
            ['key' => 'village.contact_info'],
            [
                'value' => [
                    'phone' => '0',
                    'whatsapp' => '0',
                    'email' => 'wargadesal<PERSON>h<PERSON><EMAIL>',
                    'address' => 'Kantor <PERSON>, Kec. Caringin, Kab. Bogor, Jawa Barat',
                    'postal_code' => '16730',
                    'maps_link' => 'https://maps.app.goo.gl/F2SWrp4QBYBJXiG6A',
                ],
                'type' => 'json',
                'description' => 'Informasi kontak resmi Desa Lemah Duhur untuk keperluan komunikasi dan layanan masyarakat',
            ]
        );

        // Village profile information
        Setting::updateOrCreate(
            ['key' => 'village.profile'],
            [
                'value' => [
                    'name' => 'Desa Lemah Duhur',
                    'full_name' => 'Desa Lemah Duhur, Kecamatan Caringin, Kabupaten Bogor, Jawa Barat',
                    'district' => 'Kecamatan Caringin',
                    'regency' => 'Kabupaten Bogor',
                    'province' => 'Jawa Barat',
                    'established_year' => '1910-1920',
                    'area' => 'Dataran tinggi pegunungan',
                    'population' => 'Data akan diperbarui sesuai sensus terbaru',
                    'village_code' => '3201062001',
                    'postal_code' => '16730',
                    'timezone' => 'WIB (UTC+7)',
                    'coordinates' => [
                        'latitude' => '-6.5833',
                        'longitude' => '106.8333',
                    ],
                ],
                'type' => 'json',
                'description' => 'Profil lengkap Desa Lemah Duhur meliputi informasi geografis, administratif, dan demografis',
            ]
        );

        // Village history and etymology
        Setting::updateOrCreate(
            ['key' => 'village.history'],
            [
                'value' => [
                    'etymology' => [
                        'lemah' => 'Tanah yang rata/datar',
                        'duhur_luhur' => 'Tempat yang tinggi/terhormat',
                        'meaning' => 'Proses perataan tanah di daerah pegunungan tinggi untuk pemukiman',
                    ],
                    'legend' => 'Menurut legenda setempat, daerah ini pernah digunakan sebagai tempat pendaratan kuda terbang oleh sosok-sosok mistis',
                    'administrative_history' => 'Awalnya merupakan wilayah yang lebih luas, kemudian terbagi menjadi Desa Cimande Jaya dan Desa Lemah Duhur',
                    'cultural_significance' => 'Memiliki nilai historis dan budaya yang tinggi dalam perkembangan wilayah Caringin',
                ],
                'type' => 'json',
                'description' => 'Sejarah, etimologi, dan legenda Desa Lemah Duhur yang mencerminkan kekayaan budaya lokal',
            ]
        );

        // Service availability settings
        Setting::updateOrCreate(
            ['key' => 'village.service_settings'],
            [
                'value' => [
                    'online_services_available' => true,
                    'mobile_optimized' => true,
                    'emergency_contact_available' => true,
                    'multilingual_support' => false,
                    'digital_signature_enabled' => false,
                ],
                'type' => 'json',
                'description' => 'Pengaturan ketersediaan layanan digital dan fitur-fitur yang tersedia untuk masyarakat',
            ]
        );

        // Emergency and important contacts
        Setting::updateOrCreate(
            ['key' => 'village.emergency_contacts'],
            [
                'value' => [
                    'village_head' => [
                        'title' => 'Kepala Desa',
                        'name' => 'Akan diperbarui sesuai periode jabatan',
                        'phone' => '081234567891',
                        'office_hours' => 'Senin - Jumat: 08:00 - 15:00 WIB',
                    ],
                    'village_secretary' => [
                        'title' => 'Sekretaris Desa',
                        'name' => 'Akan diperbarui sesuai periode jabatan',
                        'phone' => '081234567892',
                        'office_hours' => 'Senin - Jumat: 08:00 - 15:00 WIB',
                    ],
                    'security' => [
                        'title' => 'Keamanan Desa (Hansip)',
                        'phone' => '081234567893',
                        'availability' => '24 jam',
                    ],
                    'health_center' => [
                        'title' => 'Puskesmas Caringin',
                        'phone' => '0',
                        'address' => 'Kecamatan Caringin, Kabupaten Bogor',
                        'emergency_hours' => '24 jam untuk kasus darurat',
                    ],
                    'police' => [
                        'title' => 'Polsek Caringin',
                        'phone' => '110',
                        'local_phone' => '0',
                        'availability' => '24 jam',
                    ],
                    'fire_department' => [
                        'title' => 'Pemadam Kebakaran',
                        'phone' => '113',
                        'local_phone' => '0',
                    ],
                ],
                'type' => 'json',
                'description' => 'Kontak darurat dan penting untuk keperluan mendesak masyarakat Desa Lemah Duhur',
            ]
        );

        // Village government structure
        Setting::updateOrCreate(
            ['key' => 'village.government_structure'],
            [
                'value' => [
                    'village_head' => [
                        'position' => 'Kepala Desa',
                        'name' => 'Akan diperbarui sesuai periode jabatan',
                        'period' => '2019-2025',
                        'responsibilities' => 'Memimpin penyelenggaraan pemerintahan desa',
                    ],
                    'village_secretary' => [
                        'position' => 'Sekretaris Desa',
                        'name' => 'Akan diperbarui sesuai periode jabatan',
                        'responsibilities' => 'Membantu kepala desa dalam bidang administrasi pemerintahan',
                    ],
                    'sections' => [
                        'government' => 'Seksi Pemerintahan',
                        'welfare' => 'Seksi Kesejahteraan',
                        'services' => 'Seksi Pelayanan',
                    ],
                    'bpd' => [
                        'name' => 'Badan Permusyawaratan Desa (BPD)',
                        'function' => 'Lembaga yang melaksanakan fungsi pemerintahan yang anggotanya merupakan wakil dari penduduk desa',
                    ],
                ],
                'type' => 'json',
                'description' => 'Struktur pemerintahan dan organisasi Desa Lemah Duhur',
            ]
        );

        // Village facilities and infrastructure
        Setting::updateOrCreate(
            ['key' => 'village.facilities'],
            [
                'value' => [
                    'public_facilities' => [
                        'village_office' => 'Kantor Desa Lemah Duhur',
                        'community_hall' => 'Balai Desa',
                        'mosque' => 'Masjid Al-Ikhlas',
                        'elementary_school' => 'SDN Lemah Duhur',
                        'health_post' => 'Posyandu dan Polindes',
                    ],
                    'infrastructure' => [
                        'roads' => 'Jalan desa beraspal dan berbatu',
                        'water_supply' => 'PDAM dan sumur bor',
                        'electricity' => 'PLN tersedia di seluruh wilayah',
                        'internet' => 'Jaringan seluler dan internet tersedia',
                    ],
                    'transportation' => [
                        'public_transport' => 'Angkutan umum ke Caringin dan Bogor',
                        'parking' => 'Area parkir tersedia di kantor desa',
                    ],
                ],
                'type' => 'json',
                'description' => 'Fasilitas umum dan infrastruktur yang tersedia di Desa Lemah Duhur',
            ]
        );

        // Village services and programs
        Setting::updateOrCreate(
            ['key' => 'village.services'],
            [
                'value' => [
                    'administrative_services' => [
                        'ktp' => 'Pengurusan KTP dan KK',
                        'birth_certificate' => 'Akta kelahiran',
                        'death_certificate' => 'Surat kematian',
                        'marriage_certificate' => 'Surat nikah',
                        'business_permit' => 'Surat izin usaha',
                        'land_certificate' => 'Surat tanah dan bangunan',
                    ],
                    'social_programs' => [
                        'pkh' => 'Program Keluarga Harapan (PKH)',
                        'blt' => 'Bantuan Langsung Tunai (BLT)',
                        'raskin' => 'Beras untuk Keluarga Miskin',
                        'posyandu' => 'Posyandu untuk ibu dan anak',
                        'elderly_care' => 'Program lansia',
                    ],
                    'community_development' => [
                        'gotong_royong' => 'Kegiatan gotong royong rutin',
                        'youth_programs' => 'Program pemuda dan karang taruna',
                        'womens_programs' => 'Program PKK dan pemberdayaan perempuan',
                        'religious_activities' => 'Kegiatan keagamaan dan pengajian',
                    ],
                ],
                'type' => 'json',
                'description' => 'Layanan administrasi dan program pembangunan yang tersedia di Desa Lemah Duhur',
            ]
        );

        // Website and system settings
        Setting::updateOrCreate(
            ['key' => 'village.website_settings'],
            [
                'value' => [
                    'site_title' => 'Website Resmi Desa Lemah Duhur',
                    'site_description' => 'Portal informasi dan layanan digital Desa Lemah Duhur, Kecamatan Caringin, Kabupaten Bogor, Jawa Barat',
                    'keywords' => 'desa lemah duhur, caringin, bogor, jawa barat, pemerintahan desa, layanan publik',
                    'logo_text' => 'Desa Lemah Duhur',
                    'footer_text' => 'Desa Lemah Duhur - Melayani dengan Hati untuk Masyarakat',
                    'social_media' => [
                        'facebook' => '',
                        'instagram' => '',
                        'youtube' => '',
                        'twitter' => '',
                    ],
                    'maintenance_mode' => false,
                    'visitor_counter' => true,
                ],
                'type' => 'json',
                'description' => 'Pengaturan website dan sistem informasi Desa Lemah Duhur',
            ]
        );
    }
}
