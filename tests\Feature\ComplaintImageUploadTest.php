<?php

namespace Tests\Feature;

use App\Models\Complaint;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;
use Tests\TestCase;

class ComplaintImageUploadTest extends TestCase
{
    use RefreshDatabase;

    public function test_complaint_can_be_created_with_image_attachments()
    {
        Storage::fake('public');

        $image1 = UploadedFile::fake()->image('proof1.jpg', 800, 600)->size(1024); // 1MB
        $image2 = UploadedFile::fake()->image('proof2.png', 800, 600)->size(1024); // 1MB

        $response = $this->post(route('complaints.store'), [
            'name' => '<PERSON>',
            'email' => '<EMAIL>',
            'phone' => '0',
            'category' => 'infrastruktur',
            'subject' => 'Jalan Rusak',
            'description' => 'Jalan di depan rumah saya kondisinya sangat rusak dengan banyak lubang yang berbahaya untuk kendaraan.',
            'attachments' => [$image1, $image2],
        ]);

        $response->assertRedirect();
        $response->assertSessionHas('success');

        // Check that complaint was created
        $complaint = Complaint::first();
        $this->assertNotNull($complaint);
        $this->assertEquals('John Doe', $complaint->name);
        $this->assertEquals('Jalan Rusak', $complaint->subject);

        // Check that attachments were stored
        $this->assertNotNull($complaint->attachments);
        $this->assertCount(2, $complaint->attachments);

        // Check that files were uploaded to storage
        foreach ($complaint->attachments as $attachment) {
            Storage::disk('public')->assertExists($attachment['path']);
            $this->assertTrue(str_starts_with($attachment['mime_type'], 'image/'));
        }
    }

    public function test_complaint_validates_file_types()
    {
        Storage::fake('public');

        // Test with PDF file (should be rejected)
        $invalidFile = UploadedFile::fake()->create('document.pdf', 1024, 'application/pdf');

        $response = $this->post(route('complaints.store'), [
            'name' => 'John Doe',
            'email' => '<EMAIL>',
            'category' => 'infrastruktur',
            'subject' => 'Test Subject',
            'description' => 'This is a test description that is longer than fifty characters to meet the minimum requirement.',
            'attachments' => [$invalidFile],
        ]);

        $response->assertSessionHasErrors('attachments.0');
    }

    public function test_complaint_validates_file_size()
    {
        Storage::fake('public');

        // Create a file larger than 5MB
        $largeFile = UploadedFile::fake()->create('large.jpg', 6144, 'image/jpeg'); // 6MB

        $response = $this->post(route('complaints.store'), [
            'name' => 'John Doe',
            'email' => '<EMAIL>',
            'category' => 'infrastruktur',
            'subject' => 'Test Subject',
            'description' => 'This is a test description that is longer than fifty characters to meet the minimum requirement.',
            'attachments' => [$largeFile],
        ]);

        $response->assertSessionHasErrors('attachments.0');
    }

    public function test_complaint_validates_maximum_file_count()
    {
        Storage::fake('public');

        $files = [
            UploadedFile::fake()->image('image1.jpg'),
            UploadedFile::fake()->image('image2.jpg'),
            UploadedFile::fake()->image('image3.jpg'),
            UploadedFile::fake()->image('image4.jpg'), // 4th file should fail
        ];

        $response = $this->post(route('complaints.store'), [
            'name' => 'John Doe',
            'email' => '<EMAIL>',
            'category' => 'infrastruktur',
            'subject' => 'Test Subject',
            'description' => 'This is a test description that is longer than fifty characters to meet the minimum requirement.',
            'attachments' => $files,
        ]);

        $response->assertSessionHasErrors('attachments');
    }

    public function test_complaint_requires_attachments()
    {
        $response = $this->post(route('complaints.store'), [
            'name' => 'John Doe',
            'email' => '<EMAIL>',
            'category' => 'infrastruktur',
            'subject' => 'Test Subject',
            'description' => 'This is a test description that is longer than fifty characters to meet the minimum requirement.',
        ]);

        $response->assertSessionHasErrors('attachments');
    }

    public function test_complaint_can_be_created_with_single_image()
    {
        Storage::fake('public');

        $image = UploadedFile::fake()->image('proof.jpg', 800, 600)->size(1024); // 1MB

        $response = $this->post(route('complaints.store'), [
            'name' => 'John Doe',
            'email' => '<EMAIL>',
            'phone' => '0',
            'category' => 'infrastruktur',
            'subject' => 'Jalan Rusak',
            'description' => 'Jalan di depan rumah saya kondisinya sangat rusak dengan banyak lubang yang berbahaya untuk kendaraan.',
            'attachments' => [$image],
        ]);

        $response->assertRedirect();
        $response->assertSessionHas('success');

        $complaint = Complaint::first();
        $this->assertNotNull($complaint);
        $this->assertCount(1, $complaint->attachments);

        // Check that file was uploaded to storage
        Storage::disk('public')->assertExists($complaint->attachments[0]['path']);
    }
}
