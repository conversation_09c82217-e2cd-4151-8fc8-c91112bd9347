import SEOHead from '@/components/Common/SEOHead';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { useSettings } from '@/hooks/useSettings';
import PublicLayout from '@/layouts/PublicLayout';
import { Link } from '@inertiajs/react';
import { Camera, MapPin, Star } from 'lucide-react';
import { useState } from 'react';

interface Potential {
    id: number;
    name: string;
    type: 'tourism' | 'umkm';
    description: string;
    images: string[];
    location?: string;
    is_featured: boolean;
    featured_image?: string;
    type_name: string;
}

interface Props {
    tourism: Potential[];
    umkm: Potential[];
    currentType: string;
    seoMeta?: {
        title: string;
        description: string;
        image?: string;
        url?: string;
        type?: string;
        siteName?: string;
        locale?: string;
    };
    structuredData?: object;
}

export default function PotentialIndex({ tourism, umkm, currentType, seoMeta, structuredData }: Props) {
    const [activeTab, setActiveTab] = useState<'all' | 'tourism' | 'umkm'>(currentType as 'all' | 'tourism' | 'umkm');

    const getDisplayData = () => {
        switch (activeTab) {
            case 'tourism':
                return tourism;
            case 'umkm':
                return umkm;
            default:
                return [...tourism, ...umkm];
        }
    };

    const PotentialCard = ({ potential }: { potential: Potential }) => (
        <Card className="group touch-manipulation overflow-hidden transition-all duration-300 hover:shadow-lg">
            <div className="relative">
                {potential.featured_image ? (
                    <img
                        src={potential.featured_image}
                        alt={potential.name}
                        className="h-40 w-full object-cover transition-transform duration-300 group-hover:scale-105 sm:h-48"
                    />
                ) : (
                    <div className="flex h-40 w-full items-center justify-center bg-gray-200 sm:h-48">
                        <Camera className="h-8 w-8 text-gray-400 sm:h-12 sm:w-12" />
                    </div>
                )}
                {potential.is_featured && (
                    <Badge className="absolute top-2 right-2 bg-yellow-500 text-xs text-white">
                        <Star className="mr-1 h-3 w-3" />
                        Unggulan
                    </Badge>
                )}
                <Badge className={`absolute top-2 left-2 text-xs ${potential.type === 'tourism' ? 'bg-green-500' : 'bg-blue-500'} text-white`}>
                    {potential.type_name}
                </Badge>
            </div>

            <CardHeader className="p-4 sm:p-6">
                <CardTitle className="text-base transition-colors group-hover:text-green-600 sm:text-lg">{potential.name}</CardTitle>
                {potential.location && (
                    <CardDescription className="flex items-start text-sm text-gray-600">
                        <MapPin className="mt-0.5 mr-1 h-4 w-4 flex-shrink-0" />
                        <span className="break-words">{potential.location}</span>
                    </CardDescription>
                )}
            </CardHeader>

            <CardContent className="p-4 pt-0 sm:p-6">
                <p className="mb-4 line-clamp-3 text-sm text-gray-700">{potential.description.replace(/<[^>]*>/g, '').substring(0, 150)}...</p>

                <Link href={route('potential.show', potential.id)}>
                    <Button className="h-12 w-full touch-manipulation bg-green-600 text-base hover:bg-green-700 sm:h-auto">Lihat Detail</Button>
                </Link>
            </CardContent>
        </Card>
    );

    return (
        <>
            <SEOHead
                title={seoMeta?.title}
                description={seoMeta?.description}
                image={seoMeta?.image}
                url={seoMeta?.url}
                type={seoMeta?.type}
                siteName={seoMeta?.siteName}
                locale={seoMeta?.locale}
                structuredData={structuredData}
            />
            <PublicLayout>
                <div className="min-h-screen bg-gray-50 dark:bg-gray-950">
                    {/* Hero Section */}
                    <div className="bg-gradient-to-r from-green-600 to-green-800 py-12 text-white sm:py-16 dark:from-green-900 dark:to-green-950 dark:text-green-100">
                        <div className="container mx-auto px-3 sm:px-4">
                            <div className="text-center">
                                <h1 className="mb-4 text-2xl font-bold sm:text-3xl md:text-4xl lg:text-5xl">
                                    Potensi {useSettings().getVillageProfile()?.name || 'Desa Lemah Duhur'}
                                </h1>
                                <p className="mx-auto max-w-3xl px-4 text-base text-green-100 sm:px-0 sm:text-lg md:text-xl lg:text-2xl dark:text-green-200">
                                    Jelajahi keindahan wisata dan keunggulan produk UMKM lokal yang menjadi kebanggaan desa kami
                                </p>
                            </div>
                        </div>
                    </div>

                    {/* Filter Tabs */}
                    <div className="container mx-auto px-3 py-6 sm:px-4 sm:py-8">
                        <div className="mb-6 flex flex-wrap justify-center gap-2 sm:mb-8 sm:gap-4">
                            <Button
                                variant={activeTab === 'all' ? 'default' : 'outline'}
                                onClick={() => setActiveTab('all')}
                                className={`h-12 touch-manipulation text-sm sm:h-auto sm:text-base ${activeTab === 'all' ? 'bg-green-600 hover:bg-green-700' : ''}`}
                            >
                                Semua ({tourism.length + umkm.length})
                            </Button>
                            <Button
                                variant={activeTab === 'tourism' ? 'default' : 'outline'}
                                onClick={() => setActiveTab('tourism')}
                                className={`h-12 touch-manipulation text-sm sm:h-auto sm:text-base ${activeTab === 'tourism' ? 'bg-green-600 hover:bg-green-700' : ''}`}
                            >
                                Wisata ({tourism.length})
                            </Button>
                            <Button
                                variant={activeTab === 'umkm' ? 'default' : 'outline'}
                                onClick={() => setActiveTab('umkm')}
                                className={`h-12 touch-manipulation text-sm sm:h-auto sm:text-base ${activeTab === 'umkm' ? 'bg-green-600 hover:bg-green-700' : ''}`}
                            >
                                UMKM ({umkm.length})
                            </Button>
                        </div>

                        {/* Content Grid */}
                        <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 sm:gap-6 lg:grid-cols-3">
                            {getDisplayData().map((potential) => (
                                <PotentialCard key={potential.id} potential={potential} />
                            ))}
                        </div>

                        {getDisplayData().length === 0 && (
                            <div className="py-16 text-center">
                                <div className="mb-4 text-gray-400 dark:text-gray-600">
                                    <Camera className="mx-auto h-16 w-16" />
                                </div>
                                <h3 className="mb-2 text-xl font-semibold text-gray-600 dark:text-gray-300">Belum Ada Data</h3>
                                <p className="text-gray-500 dark:text-gray-400">
                                    {activeTab === 'tourism'
                                        ? 'Belum ada data wisata yang tersedia'
                                        : activeTab === 'umkm'
                                          ? 'Belum ada data UMKM yang tersedia'
                                          : 'Belum ada data potensi yang tersedia'}
                                </p>
                            </div>
                        )}
                    </div>

                    {/* Call to Action */}
                    <div className="bg-green-600 py-8 text-white sm:py-12 dark:bg-green-900 dark:text-green-100">
                        <div className="container mx-auto px-3 text-center sm:px-4">
                            <h2 className="mb-4 text-xl font-bold sm:text-2xl md:text-3xl">Tertarik untuk Berkunjung atau Berkolaborasi?</h2>
                            <p className="mx-auto mb-6 max-w-2xl px-4 text-sm text-green-100 sm:px-0 sm:text-base dark:text-green-200">
                                Hubungi kami untuk informasi lebih lanjut tentang wisata dan produk UMKM{' '}
                                {useSettings().getVillageProfile()?.name || 'Desa Lemah Duhur'}. Kami siap membantu Anda!
                            </p>
                            <div className="flex flex-col justify-center gap-3 sm:flex-row sm:gap-4">
                                <Link href="/profil">
                                    <Button
                                        variant="outline"
                                        className="h-12 w-full touch-manipulation bg-white text-base text-green-600 hover:bg-gray-100 sm:h-auto sm:w-auto"
                                    >
                                        Lihat Profil Desa
                                    </Button>
                                </Link>
                                <Link href="/layanan">
                                    <Button
                                        variant="outline"
                                        className="h-12 w-full touch-manipulation bg-white text-base text-green-600 hover:bg-gray-100 sm:h-auto sm:w-auto"
                                    >
                                        Layanan Desa
                                    </Button>
                                </Link>
                            </div>
                        </div>
                    </div>
                </div>
            </PublicLayout>
        </>
    );
}
