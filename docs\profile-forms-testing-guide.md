# Profile Content Forms Testing Guide

## Overview

This guide provides step-by-step instructions for testing the new section-specific profile content forms.

## Test Scenarios

### 1. Organization Section Testing

**Test Case: Create New Organization Entry**

1. Navigate to Admin → Profiles → Create
2. Select "Struktur Organisasi" from the section dropdown
3. Verify that the Organization Form appears with:
   - Name field
   - Position field  
   - Period field (optional)
   - Description field (optional)
   - Live preview section

**Expected Input:**
```
Name: UJANG NAJMUDIN
Position: Kepala Desa Lemah Duhur
Period: 2019-2025
Description: (leave empty or add additional info)
```

**Expected HTML Output:**
```html
<div class="text-center">
    <h4>UJANG NAJMUDIN</h4>
    <p>Kepala Desa Lemah Duhur</p>
    <p>Periode: 2019-2025</p>
</div>
```

**Test Case: Edit Existing Organization Entry**

1. Navigate to Admin → Profiles → Index
2. Find an existing organization entry (e.g., "Kepala Desa")
3. Click Edit
4. Verify that:
   - Form loads with existing data populated
   - Name, position, and period are correctly parsed
   - Preview shows current formatting
   - Changes update the preview in real-time

### 2. Demographics Section Testing

**Test Case: Create Demographics with Grid Layout**

1. Navigate to Admin → Profiles → Create
2. Select "Data Demografis" from the section dropdown
3. Select "Grid (2 kolom)" as display type
4. Add multiple data items:

**Sample Data:**
```
Item 1: Label: "Jumlah Penduduk", Value: "2.847", Unit: "jiwa"
Item 2: Label: "Jumlah KK", Value: "892", Unit: "KK"
Item 3: Label: "Laki-laki", Value: "1.456", Unit: "jiwa (51%)"
Item 4: Label: "Perempuan", Value: "1.391", Unit: "jiwa (49%)"
```

**Expected HTML Output:**
```html
<div class="grid grid-cols-2 gap-4">
    <div><h4>Jumlah Penduduk</h4><p>2.847 jiwa</p></div>
    <div><h4>Jumlah KK</h4><p>892 KK</p></div>
    <div><h4>Laki-laki</h4><p>1.456 jiwa (51%)</p></div>
    <div><h4>Perempuan</h4><p>1.391 jiwa (49%)</p></div>
</div>
```

**Test Case: Create Demographics with List Layout**

1. Select "List (bullet points)" as display type
2. Add data items:

**Sample Data:**
```
Item 1: Label: "Usia 0-14 tahun", Value: "687", Unit: "jiwa (24%)"
Item 2: Label: "Usia 15-64 tahun", Value: "1.823", Unit: "jiwa (64%)"
Item 3: Label: "Usia 65+ tahun", Value: "337", Unit: "jiwa (12%)"
```

**Expected HTML Output:**
```html
<ul>
    <li>Usia 0-14 tahun: 687 jiwa (24%)</li>
    <li>Usia 15-64 tahun: 1.823 jiwa (64%)</li>
    <li>Usia 65+ tahun: 337 jiwa (12%)</li>
</ul>
```

### 3. Rich Text Editor Sections Testing

**Test Case: History Section**

1. Select "Sejarah Desa" from the section dropdown
2. Verify that Rich Text Editor appears with:
   - Purple-themed information box
   - Appropriate placeholder text
   - Full formatting toolbar

**Test Case: Vision Mission Section**

1. Select "Visi & Misi" from the section dropdown
2. Test creating content with:
   - Headings for "VISI:" and "MISI:"
   - Paragraphs for vision statement
   - Ordered list for mission points

**Test Case: Geography Section**

1. Select "Data Geografis" from the section dropdown
2. Test creating mixed content:
   - Paragraphs for descriptions
   - Unordered lists for boundary information
   - Mixed formatting

### 4. Backward Compatibility Testing

**Test Case: Edit Existing Organization Content**

1. Find existing organization entry with HTML like:
   ```html
   <div class="text-center"><h4>Dwi Hartono, S.Sos</h4><p>Sekretaris Desa</p></div>
   ```
2. Click Edit
3. Verify that:
   - Name field shows "Dwi Hartono, S.Sos"
   - Position field shows "Sekretaris Desa"
   - Period field is empty (as expected)
   - Preview matches original formatting

**Test Case: Edit Existing Demographics Content**

1. Find existing demographics entry with HTML like:
   ```html
   <div class="grid grid-cols-2 gap-4">
       <div><h4>Jumlah Penduduk</h4><p>2.847 jiwa</p></div>
       <div><h4>Jumlah KK</h4><p>892 KK</p></div>
   </div>
   ```
2. Click Edit
3. Verify that:
   - Form type is set to "Grid"
   - Data items are correctly parsed
   - Values and units are separated properly

### 5. Form Validation Testing

**Test Case: Required Field Validation**

1. Try to submit forms with empty required fields
2. Verify appropriate error messages appear
3. Test that forms cannot be submitted with invalid data

**Test Case: Data Integrity**

1. Create content with special characters
2. Verify HTML encoding is handled properly
3. Test that saved content displays correctly on public pages

## Checklist for Complete Testing

### Organization Form
- [ ] Form appears when organization section is selected
- [ ] All fields work correctly (name, position, period, description)
- [ ] Preview updates in real-time
- [ ] HTML output is correctly formatted
- [ ] Existing content loads and parses correctly
- [ ] Form validation works

### Demographics Form
- [ ] Form appears when demographics section is selected
- [ ] Display type selection works (grid vs list)
- [ ] Add/remove data items works
- [ ] All fields work (label, value, unit)
- [ ] Preview updates for both display types
- [ ] HTML output matches selected format
- [ ] Existing content loads and parses correctly

### Rich Text Editor Sections
- [ ] Editor appears for history, vision_mission, geography sections
- [ ] Section-specific information boxes appear
- [ ] Appropriate placeholders are shown
- [ ] Full editing functionality works
- [ ] Existing content loads correctly

### General Functionality
- [ ] Section selection updates form type immediately
- [ ] Form data persists when switching between fields
- [ ] Save functionality works for all section types
- [ ] Public pages display content correctly
- [ ] No console errors during usage
- [ ] TypeScript compilation succeeds
- [ ] Build process completes successfully

## Common Issues and Solutions

### Issue: Form doesn't switch when section changes
**Solution:** Check that ProfileContentForm component is receiving updated section prop

### Issue: Existing content doesn't load in structured forms
**Solution:** Verify parsing functions handle the specific HTML format correctly

### Issue: Preview doesn't match final output
**Solution:** Ensure preview rendering matches the HTML generation functions

### Issue: Validation errors persist after fixing
**Solution:** Check that form state is properly updated and validation is re-triggered

## Performance Considerations

- Forms should load quickly even with existing content
- Real-time preview should not cause lag
- Large demographic datasets should be handled efficiently
- Memory usage should remain reasonable during editing

## Browser Compatibility

Test in:
- [ ] Chrome (latest)
- [ ] Firefox (latest)  
- [ ] Safari (latest)
- [ ] Edge (latest)

## Mobile Responsiveness

Test forms on:
- [ ] Desktop (1920x1080)
- [ ] Tablet (768px width)
- [ ] Mobile (375px width)

Verify that:
- Forms are usable on all screen sizes
- Touch interactions work properly
- Text is readable without zooming
