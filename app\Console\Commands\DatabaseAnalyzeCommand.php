<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class DatabaseAnalyzeCommand extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'db:analyze {--vacuum : Run VACUUM on SQLite database}';

    /**
     * The console command description.
     */
    protected $description = 'Analyze and optimize database tables for better performance';

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $this->info('🔍 Starting database analysis and optimization...');

        $driver = config('database.default');
        $connection = DB::connection();

        if ($driver === 'sqlite') {
            $this->optimizeSQLite();
        } else {
            $this->optimizeGeneric();
        }

        $this->info('✅ Database optimization completed!');

        return Command::SUCCESS;
    }

    /**
     * Optimize SQLite database
     */
    private function optimizeSQLite(): void
    {
        $this->info('📊 Optimizing SQLite database...');

        try {
            // Analyze all tables
            $tables = DB::select("SELECT name FROM sqlite_master WHERE type='table' AND name NOT LIKE 'sqlite_%'");

            foreach ($tables as $table) {
                DB::statement("ANALYZE {$table->name}");
                $this->line("  • Analyzed table: {$table->name}");
            }

            // Run VACUUM if requested
            if ($this->option('vacuum')) {
                $this->info('🧹 Running VACUUM to reclaim space...');
                DB::statement('VACUUM');
                $this->line('  • Database vacuumed successfully');
            }

            // Update statistics
            DB::statement('ANALYZE');
            $this->line('  • Database statistics updated');

            // Enable query optimization
            DB::statement('PRAGMA optimize');
            $this->line('  • Query optimization enabled');

            // Show database info
            $dbSize = DB::select('PRAGMA page_count')[0]->page_count ?? 0;
            $pageSize = DB::select('PRAGMA page_size')[0]->page_size ?? 0;
            $totalSize = round(($dbSize * $pageSize) / 1024 / 1024, 2);

            $this->info('📈 Database Statistics:');
            $this->line("  • Total pages: {$dbSize}");
            $this->line("  • Page size: {$pageSize} bytes");
            $this->line("  • Database size: {$totalSize} MB");

        } catch (\Exception $e) {
            $this->error("Database optimization failed: {$e->getMessage()}");

            return;
        }
    }

    /**
     * Optimize generic database
     */
    private function optimizeGeneric(): void
    {
        $this->info('📊 Optimizing database tables...');

        try {
            $tables = ['news', 'profiles', 'services', 'potentials', 'users'];

            foreach ($tables as $table) {
                if ($this->tableExists($table)) {
                    // For MySQL/PostgreSQL, we would run ANALYZE TABLE
                    // For now, just report the table exists
                    $count = DB::table($table)->count();
                    $this->line("  • Table {$table}: {$count} records");
                }
            }

        } catch (\Exception $e) {
            $this->error("Database analysis failed: {$e->getMessage()}");

            return;
        }
    }

    /**
     * Check if table exists
     */
    private function tableExists(string $table): bool
    {
        try {
            return DB::getSchemaBuilder()->hasTable($table);
        } catch (\Exception $e) {
            return false;
        }
    }
}
