<?php

namespace App\Http\Middleware;

use App\Services\FeatureFlagService;
use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Symfony\Component\HttpFoundation\Response;

/**
 * Middleware untuk memeriksa feature flag pada level route
 *
 * Middleware ini memungkinkan perlindungan route berdasarkan status feature flag.
 * Mendukung pemeriksaan multiple flag dengan logika AND/OR.
 */
class RequireFeatureFlag
{
    /**
     * Feature flag service instance
     */
    private FeatureFlagService $featureFlagService;

    public function __construct(FeatureFlagService $featureFlagService)
    {
        $this->featureFlagService = $featureFlagService;
    }

    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     * @param  string  $feature  Feature flag requirement (supports multiple flags with logic)
     * @param  string  $logic  Logic operator for multiple flags ('and' or 'or', default: 'and')
     * @param  string  $action  Action when flag is disabled ('abort', 'redirect', 'json', default: 'abort')
     * @param  string|null  $redirect  Redirect URL when action is 'redirect'
     */
    public function handle(Request $request, Closure $next, string $feature, string $logic = 'and', string $action = 'abort', ?string $redirect = null): Response
    {
        try {
            // Parse feature flags (support comma-separated flags)
            $flags = array_map('trim', explode(',', $feature));

            // Check feature flags based on logic
            $isAllowed = $this->checkFeatureFlags($flags, $logic);

            if (! $isAllowed) {
                return $this->handleDisabledFeature($request, $action, $redirect, $flags);
            }

            return $next($request);
        } catch (\Exception $e) {
            Log::error('Feature flag middleware error: '.$e->getMessage(), [
                'feature' => $feature,
                'logic' => $logic,
                'action' => $action,
                'url' => $request->url(),
            ]);

            // On error, allow access by default (fail-open approach)
            return $next($request);
        }
    }

    /**
     * Check multiple feature flags with specified logic
     *
     * @param  array  $flags  Array of feature flag keys
     * @param  string  $logic  Logic operator ('and' or 'or')
     * @return bool True if access should be allowed
     */
    private function checkFeatureFlags(array $flags, string $logic): bool
    {
        if (empty($flags)) {
            return true;
        }

        $results = [];
        foreach ($flags as $flag) {
            $results[] = $this->featureFlagService->isEnabled($flag);
        }

        return match (strtolower($logic)) {
            'or' => in_array(true, $results, true),
            'and' => ! in_array(false, $results, true),
            default => ! in_array(false, $results, true), // Default to 'and' logic
        };
    }

    /**
     * Handle the case when feature is disabled
     *
     * @param  \Illuminate\Http\Request  $request  The request instance
     * @param  string  $action  Action to take ('abort', 'redirect', 'json')
     * @param  string|null  $redirect  Redirect URL for redirect action
     * @param  array  $flags  Array of feature flag keys that were checked
     */
    private function handleDisabledFeature(Request $request, string $action, ?string $redirect, array $flags): Response
    {
        $message = $this->getDisabledFeatureMessage($flags);

        return match (strtolower($action)) {
            'redirect' => $this->handleRedirect($redirect, $message),
            'json' => $this->handleJsonResponse($message, $flags),
            'abort' => $this->handleAbort($request, $message),
            default => $this->handleAbort($request, $message),
        };
    }

    /**
     * Handle redirect action
     *
     * @param  string|null  $redirect  Redirect URL
     * @param  string  $message  Error message
     * @return \Illuminate\Http\RedirectResponse
     */
    private function handleRedirect(?string $redirect, string $message): Response
    {
        $redirectUrl = $redirect ?: route('admin.dashboard');

        return redirect($redirectUrl)->with('error', $message);
    }

    /**
     * Handle JSON response action
     *
     * @param  string  $message  Error message
     * @param  array  $flags  Feature flags that were checked
     * @return \Illuminate\Http\JsonResponse
     */
    private function handleJsonResponse(string $message, array $flags): Response
    {
        return response()->json([
            'error' => true,
            'message' => $message,
            'feature_flags' => $flags,
            'code' => 'FEATURE_DISABLED',
        ], 403);
    }

    /**
     * Handle abort action
     *
     * @param  \Illuminate\Http\Request  $request  The request instance
     * @param  string  $message  Error message
     * @return \Illuminate\Http\Response
     */
    private function handleAbort(Request $request, string $message): Response
    {
        // For API requests, return JSON response
        if ($request->expectsJson() || $request->is('api/*')) {
            return response()->json([
                'error' => true,
                'message' => $message,
                'code' => 'FEATURE_DISABLED',
            ], 403);
        }

        // For web requests, return 403 response
        return response()->view('errors.403', ['message' => $message], 403);
    }

    /**
     * Get appropriate message for disabled feature
     *
     * @param  array  $flags  Feature flags that were checked
     * @return string Localized error message
     */
    private function getDisabledFeatureMessage(array $flags): string
    {
        if (count($flags) === 1) {
            return 'Fitur ini sedang tidak tersedia. Silakan hubungi administrator untuk informasi lebih lanjut.';
        }

        return 'Beberapa fitur yang diperlukan sedang tidak tersedia. Silakan hubungi administrator untuk informasi lebih lanjut.';
    }

    /**
     * Create middleware instance with specific configuration
     *
     * @param  string  $feature  Feature flag requirement
     * @param  string  $logic  Logic operator ('and' or 'or')
     * @param  string  $action  Action when disabled ('abort', 'redirect', 'json')
     * @param  string|null  $redirect  Redirect URL
     * @return string Middleware string for route definition
     */
    public static function with(string $feature, string $logic = 'and', string $action = 'abort', ?string $redirect = null): string
    {
        $params = [$feature, $logic, $action];

        if ($redirect) {
            $params[] = $redirect;
        }

        return 'feature:'.implode(',', $params);
    }

    /**
     * Helper method to create AND logic middleware
     *
     * @param  string|array  $features  Feature flag(s) - all must be enabled
     * @param  string  $action  Action when disabled
     * @param  string|null  $redirect  Redirect URL
     * @return string Middleware string
     */
    public static function requireAll($features, string $action = 'abort', ?string $redirect = null): string
    {
        $featureString = is_array($features) ? implode(',', $features) : $features;

        return self::with($featureString, 'and', $action, $redirect);
    }

    /**
     * Helper method to create OR logic middleware
     *
     * @param  string|array  $features  Feature flag(s) - at least one must be enabled
     * @param  string  $action  Action when disabled
     * @param  string|null  $redirect  Redirect URL
     * @return string Middleware string
     */
    public static function requireAny($features, string $action = 'abort', ?string $redirect = null): string
    {
        $featureString = is_array($features) ? implode(',', $features) : $features;

        return self::with($featureString, 'or', $action, $redirect);
    }
}
