<?php

namespace App\Http\Controllers;

use App\Models\Service;
use Inertia\Inertia;

class ResponsiveDesignController extends Controller
{
    public function index()
    {
        // Get services from database instead of hardcoded data
        $services = Service::active()
            ->take(3)
            ->get()
            ->map(function ($service) {
                return [
                    'id' => $service->id,
                    'title' => $service->name,
                    'description' => $service->description,
                    'condensed' => true,
                    'mobileOptimized' => true,
                ];
            });

        return Inertia::render('ResponsiveDesign/Index', [
            'layout' => [
                'type' => 'responsive',
                'columns' => [
                    'desktop' => 3,
                    'tablet' => 2,
                    'mobile' => 1,
                ],
                'responsive' => true,
                'breakpoints' => [
                    'mobile' => 768,
                    'tablet' => 1024,
                    'desktop' => 1200,
                ],
            ],
            'services' => $services,
            'galleryLayout' => [
                'mobile' => true,
                'tablet' => true,
                'desktop' => true,
                'type' => 'grid',
                'columns' => [
                    'mobile' => 2,
                    'tablet' => 3,
                    'desktop' => 4,
                ],
            ],
            'accessibility' => [
                'mobileNavigation' => [
                    'enabled' => true,
                    'ariaLabels' => true,
                    'touchFriendly' => true,
                    'swipeGestures' => true,
                ],
                'keyboardNavigation' => true,
                'colorContrast' => [
                    'enabled' => true,
                    'ratio' => 'AA',
                    'darkMode' => true,
                ],
                'screenReader' => [
                    'enabled' => true,
                    'skipLinks' => true,
                    'altText' => true,
                ],
            ],
            'imageOptimization' => [
                'desktop' => true,
                'tablet' => true,
                'mobile' => true,
                'responsive' => true,
                'srcset' => [
                    'thumbnail' => [150, 150],
                    'medium' => [300, 200],
                    'large' => [600, 400],
                    'xlarge' => [1200, 800],
                ],
                'lazyLoading' => true,
                'webp' => true,
                'compression' => 85,
            ],
            'seo' => [
                'title' => 'Desain Responsif - Desa Lemah Duhur',
                'description' => 'Pengalaman pengguna yang optimal di semua perangkat dengan desain responsif yang adaptif',
                'keywords' => ['responsive design', 'mobile friendly', 'adaptive layout', 'user experience'],
                'viewport' => 'width=device-width, initial-scale=1.0',
                'themeColor' => '#2563eb',
            ],
        ]);
    }
}
