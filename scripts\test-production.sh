#!/bin/bash

# Production Testing Script for Website Profil Desa Le<PERSON>
# Comprehensive testing before and after deployment

set -e  # Exit on any error

echo "🧪 Starting comprehensive production testing..."
echo "📅 Testing started at: $(date)"
echo ""

# Color codes for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Counters
TESTS_PASSED=0
TESTS_FAILED=0
TESTS_TOTAL=0

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[PASS]${NC} $1"
    ((TESTS_PASSED++))
    ((TESTS_TOTAL++))
}

print_failure() {
    echo -e "${RED}[FAIL]${NC} $1"
    ((TESTS_FAILED++))
    ((TESTS_TOTAL++))
}

print_warning() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

# Function to run a test
run_test() {
    local test_name="$1"
    local test_command="$2"
    
    if eval "$test_command" > /dev/null 2>&1; then
        print_success "$test_name"
        return 0
    else
        print_failure "$test_name"
        return 1
    fi
}

# Check if we're in the right directory
if [ ! -f "artisan" ]; then
    print_failure "artisan file not found. Please run this script from the Laravel project root."
    exit 1
fi

echo "🔍 SYSTEM REQUIREMENTS TESTING"
echo "================================"

# Test PHP version
run_test "PHP version >= 8.2" "php -r 'exit(version_compare(PHP_VERSION, \"8.2.0\", \">=\") ? 0 : 1);'"

# Test required PHP extensions
run_test "PHP extension: mbstring" "php -m | grep -q mbstring"
run_test "PHP extension: openssl" "php -m | grep -q openssl"
run_test "PHP extension: pdo" "php -m | grep -q pdo"
run_test "PHP extension: tokenizer" "php -m | grep -q tokenizer"
run_test "PHP extension: xml" "php -m | grep -q xml"
run_test "PHP extension: ctype" "php -m | grep -q ctype"
run_test "PHP extension: json" "php -m | grep -q json"
run_test "PHP extension: bcmath" "php -m | grep -q bcmath"

# Test Composer
run_test "Composer available" "command -v composer"

# Test Node.js and npm
run_test "Node.js available" "command -v node"
run_test "npm available" "command -v npm"

echo ""
echo "📁 FILE SYSTEM TESTING"
echo "======================"

# Test file permissions
run_test "Storage directory writable" "[ -w storage ]"
run_test "Bootstrap cache writable" "[ -w bootstrap/cache ]"
run_test "Database file exists" "[ -f database/database.sqlite ]"
run_test ".env file exists" "[ -f .env ]"
run_test ".env file readable" "[ -r .env ]"

# Test required directories
run_test "Storage/logs directory exists" "[ -d storage/logs ]"
run_test "Storage/framework/cache exists" "[ -d storage/framework/cache ]"
run_test "Storage/framework/sessions exists" "[ -d storage/framework/sessions ]"
run_test "Storage/framework/views exists" "[ -d storage/framework/views ]"

echo ""
echo "⚙️  LARAVEL APPLICATION TESTING"
echo "==============================="

# Test Laravel application
run_test "Laravel application boots" "php artisan about"
run_test "Application key is set" "grep -q 'APP_KEY=base64:' .env"
run_test "Environment is production" "php artisan env | grep -q production"

# Test database connectivity
run_test "Database connection works" "php artisan migrate:status"
run_test "Database has migrations" "php artisan migrate:status | grep -q 'Y'"

# Test cache functionality
run_test "Cache store works" "php artisan cache:clear"
run_test "Config cache works" "php artisan config:cache && php artisan config:clear"
run_test "Route cache works" "php artisan route:cache && php artisan route:clear"
run_test "View cache works" "php artisan view:cache && php artisan view:clear"

echo ""
echo "🗄️  DATABASE TESTING"
echo "==================="

# Test database tables and data
run_test "Users table has data" "php artisan tinker --execute='echo App\Models\User::count() > 0 ? \"true\" : \"false\";' | grep -q true"
run_test "News table exists" "php artisan tinker --execute='echo Schema::hasTable(\"news\") ? \"true\" : \"false\";' | grep -q true"
run_test "Services table exists" "php artisan tinker --execute='echo Schema::hasTable(\"services\") ? \"true\" : \"false\";' | grep -q true"
run_test "Profiles table exists" "php artisan tinker --execute='echo Schema::hasTable(\"profiles\") ? \"true\" : \"false\";' | grep -q true"
run_test "Potentials table exists" "php artisan tinker --execute='echo Schema::hasTable(\"potentials\") ? \"true\" : \"false\";' | grep -q true"

echo ""
echo "🎨 FRONTEND ASSETS TESTING"
echo "=========================="

# Test built assets
run_test "Vite manifest exists" "[ -f public/build/manifest.json ]"
run_test "CSS assets built" "[ -f public/build/assets/*.css ] 2>/dev/null || ls public/build/assets/*.css >/dev/null 2>&1"
run_test "JS assets built" "[ -f public/build/assets/*.js ] 2>/dev/null || ls public/build/assets/*.js >/dev/null 2>&1"

# Test asset optimization
if [ -f "public/build/manifest.json" ]; then
    run_test "Assets are versioned" "grep -q '\"file\":.*-[a-f0-9]' public/build/manifest.json"
fi

echo ""
echo "🔒 SECURITY TESTING"
echo "==================="

# Test security configurations
run_test "Debug mode disabled" "php artisan env | grep -q 'APP_DEBUG.*false'"
run_test "Secure session cookies" "grep -q 'SESSION_SECURE_COOKIE=true' .env"
run_test "HTTP-only session cookies" "grep -q 'SESSION_HTTP_ONLY=true' .env"
run_test ".env file permissions secure" "[ \$(stat -c '%a' .env) = '600' ]"

# Test file permissions
run_test "Storage permissions secure" "find storage -type f -perm 644 | wc -l | grep -q '[0-9]'"
run_test "Bootstrap cache permissions secure" "find bootstrap/cache -type f -perm 644 | wc -l | grep -q '[0-9]'"

echo ""
echo "🚀 PERFORMANCE TESTING"
echo "======================"

# Test performance optimizations
run_test "Autoloader optimized" "[ -f vendor/composer/autoload_classmap.php ]"
run_test "Config cached" "[ -f bootstrap/cache/config.php ]"
run_test "Routes cached" "[ -f bootstrap/cache/routes-v7.php ]"

# Test cache warming
run_test "Cache warm-up command works" "php artisan cache:warm-up"

# Test database optimization
run_test "Database analyze command works" "php artisan db:analyze"

echo ""
echo "🌐 SEO TESTING"
echo "=============="

# Test SEO features
run_test "Sitemap generation works" "php artisan sitemap:generate"
run_test "Sitemap file created" "[ -f public/sitemap.xml ]"
run_test "Sitemap contains URLs" "grep -q '<url>' public/sitemap.xml"

echo ""
echo "📊 CONTENT TESTING"
echo "=================="

# Test content availability
run_test "Admin user exists" "php artisan tinker --execute='echo App\Models\User::where(\"email\", \"<EMAIL>\")->exists() ? \"true\" : \"false\";' | grep -q true"
run_test "Village profile content exists" "php artisan tinker --execute='echo App\Models\Profile::count() > 0 ? \"true\" : \"false\";' | grep -q true"
run_test "Services content exists" "php artisan tinker --execute='echo App\Models\Service::count() > 0 ? \"true\" : \"false\";' | grep -q true"
run_test "Sample news exists" "php artisan tinker --execute='echo App\Models\News::count() > 0 ? \"true\" : \"false\";' | grep -q true"
run_test "Village potentials exist" "php artisan tinker --execute='echo App\Models\Potential::count() > 0 ? \"true\" : \"false\";' | grep -q true"

echo ""
echo "🔧 MAINTENANCE TESTING"
echo "======================"

# Test maintenance commands
run_test "Log files can be cleared" "php artisan log:clear 2>/dev/null || true"
run_test "Cache can be cleared" "php artisan cache:clear"
run_test "Stale cache can be pruned" "php artisan cache:prune-stale-tags"

echo ""
echo "📈 FINAL PERFORMANCE METRICS"
echo "============================"

# Measure application performance
print_status "Measuring application boot time..."
BOOT_TIME=$(php -r "
\$start = microtime(true);
require 'vendor/autoload.php';
\$app = require_once 'bootstrap/app.php';
\$kernel = \$app->make(Illuminate\Contracts\Http\Kernel::class);
\$end = microtime(true);
echo round((\$end - \$start) * 1000, 2);
")

print_status "Measuring memory usage..."
MEMORY_USAGE=$(php -r "
require 'vendor/autoload.php';
\$app = require_once 'bootstrap/app.php';
echo round(memory_get_peak_usage(true) / 1024 / 1024, 2);
")

# Database size
if [ -f "database/database.sqlite" ]; then
    DB_SIZE=$(du -h database/database.sqlite | cut -f1)
    print_status "Database size: $DB_SIZE"
fi

# Asset sizes
if [ -d "public/build" ]; then
    ASSET_SIZE=$(du -sh public/build | cut -f1)
    print_status "Built assets size: $ASSET_SIZE"
fi

echo ""
echo "📋 TEST SUMMARY"
echo "==============="
echo "Tests passed: $TESTS_PASSED"
echo "Tests failed: $TESTS_FAILED"
echo "Total tests: $TESTS_TOTAL"
echo ""

if [ $TESTS_FAILED -eq 0 ]; then
    echo -e "${GREEN}🎉 ALL TESTS PASSED!${NC}"
    echo "Your Website Profil Desa Lemah Duhur is ready for production!"
else
    echo -e "${RED}❌ $TESTS_FAILED TESTS FAILED!${NC}"
    echo "Please fix the failing tests before deploying to production."
fi

echo ""
echo "📊 Performance Metrics:"
echo "• Application boot time: ${BOOT_TIME}ms"
echo "• Peak memory usage: ${MEMORY_USAGE}MB"
echo ""

echo "📅 Testing completed at: $(date)"

# Exit with error code if tests failed
exit $TESTS_FAILED