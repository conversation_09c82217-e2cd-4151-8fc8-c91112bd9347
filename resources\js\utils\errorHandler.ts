import { ERROR_MESSAGES, getHttpErrorMessage } from '@/config/errorMessages';
import { router } from '@inertiajs/react';

export interface ApiError {
    message: string;
    status?: number;
    errors?: Record<string, string[]>;
}

export class ErrorHandler {
    /**
     * Handle API errors globally
     */
    static handleApiError(error: unknown): ApiError {
        console.error('API Error:', error);

        // Handle Inertia errors
        if (error && typeof error === 'object' && 'response' in error) {
            const errorResponse = error as { response: { status: number; data: { message?: string; errors?: Record<string, string[]> } } };
            const status = errorResponse.response.status;
            const data = errorResponse.response.data;

            return {
                message: data.message || getHttpErrorMessage(status),
                status,
                errors: data.errors,
            };
        }

        // Handle network errors
        if (error && typeof error === 'object' && 'code' in error && (error.code === 'NETWORK_ERROR' || !navigator.onLine)) {
            return {
                message: ERROR_MESSAGES.NETWORK_ERROR,
                status: 0,
            };
        }

        // Handle timeout errors
        if (error && typeof error === 'object' && 'code' in error && error.code === 'ECONNABORTED') {
            return {
                message: ERROR_MESSAGES.TIMEOUT,
                status: 408,
            };
        }

        // Default error
        const errorObj = error as { message?: string; status?: number };
        return {
            message: errorObj.message || ERROR_MESSAGES.GENERIC_ERROR,
            status: errorObj.status,
        };
    }

    /**
     * Show error notification to user
     */
    static showError(error: ApiError) {
        console.error('Error:', error.message);

        // Create and show error notification
        const event = new CustomEvent('show-error-notification', {
            detail: { message: error.message, type: 'error' },
        });
        window.dispatchEvent(event);
    }

    /**
     * Handle form validation errors
     */
    static handleValidationErrors(errors: Record<string, string[]>): string {
        const firstError = Object.values(errors)[0];
        return firstError ? firstError[0] : ERROR_MESSAGES[422];
    }

    /**
     * Redirect to appropriate error page
     */
    static redirectToErrorPage(status: number) {
        switch (status) {
            case 403:
                router.visit('/errors/403');
                break;
            case 404:
                router.visit('/errors/404');
                break;
            case 500:
                router.visit('/errors/500');
                break;
            default:
                // For other errors, stay on current page and show message
                break;
        }
    }
}

/**
 * Global error handler for unhandled promise rejections
 */
export function setupGlobalErrorHandling() {
    // Handle unhandled promise rejections
    window.addEventListener('unhandledrejection', (event) => {
        console.error('Unhandled promise rejection:', event.reason);

        const error = ErrorHandler.handleApiError(event.reason);
        ErrorHandler.showError(error);

        // Prevent the default browser error handling
        event.preventDefault();
    });

    // Handle global JavaScript errors
    window.addEventListener('error', (event) => {
        console.error('Global error:', event.error);

        // Don't handle React errors here as they should be caught by ErrorBoundary
        if (event.error && event.error.stack && event.error.stack.includes('React')) {
            return;
        }

        const error = ErrorHandler.handleApiError(event.error);
        ErrorHandler.showError(error);
    });
}

/**
 * Wrapper for Inertia requests with error handling
 */
export function handleInertiaError(error: unknown) {
    const apiError = ErrorHandler.handleApiError(error);

    // For certain status codes, redirect to error pages
    if (apiError.status && [403, 404, 500].includes(apiError.status)) {
        ErrorHandler.redirectToErrorPage(apiError.status);
    } else {
        ErrorHandler.showError(apiError);
    }

    return apiError;
}
