/**
 * Utility functions for handling images
 */

export interface ImageVariants {
    original?: {
        path: string;
        url: string;
        size: number;
    };
    medium?: {
        path: string;
        url: string;
        size: number;
    };
    thumbnail?: {
        path: string;
        url: string;
        size: number;
    };
}

/**
 * Get image URL with fallback
 */
export function getImageUrl(imageVariants: ImageVariants | string | null, size: 'original' | 'medium' | 'thumbnail' = 'medium'): string {
    if (!imageVariants) {
        return '/images/placeholder.jpg';
    }

    if (typeof imageVariants === 'string') {
        return imageVariants;
    }

    // Try to get the requested size, fallback to other sizes
    const fallbackOrder: Array<keyof ImageVariants> =
        size === 'original'
            ? ['original', 'medium', 'thumbnail']
            : size === 'medium'
              ? ['medium', 'original', 'thumbnail']
              : ['thumbnail', 'medium', 'original'];

    for (const fallbackSize of fallbackOrder) {
        if (imageVariants[fallbackSize]?.url) {
            return imageVariants[fallbackSize]!.url;
        }
    }

    return '/images/placeholder.jpg';
}

/**
 * Generate srcSet for responsive images
 */
export function generateSrcSet(imageVariants: ImageVariants | string | null): string {
    if (!imageVariants || typeof imageVariants === 'string') {
        return '';
    }

    const srcSet: string[] = [];

    if (imageVariants.thumbnail?.url) {
        srcSet.push(`${imageVariants.thumbnail.url} 300w`);
    }

    if (imageVariants.medium?.url) {
        srcSet.push(`${imageVariants.medium.url} 800w`);
    }

    if (imageVariants.original?.url) {
        srcSet.push(`${imageVariants.original.url} 1200w`);
    }

    return srcSet.join(', ');
}

/**
 * Get WebP URL with fallback
 */
export function getWebPUrl(url: string): string {
    return url.replace(/\.(jpg|jpeg|png)$/i, '.webp');
}

/**
 * Check if browser supports WebP
 */
export function supportsWebP(): Promise<boolean> {
    return new Promise((resolve) => {
        const webP = new Image();
        webP.onload = webP.onerror = () => {
            resolve(webP.height === 2);
        };
        webP.src = 'data:image/webp;base64,UklGRjoAAABXRUJQVlA4IC4AAACyAgCdASoCAAIALmk0mk0iIiIiIgBoSygABc6WWgAA/veff/0PP8bA//LwYAAA';
    });
}

/**
 * Create blur placeholder from image
 */
export function createBlurPlaceholder(imageUrl: string): Promise<string> {
    return new Promise((resolve, reject) => {
        const img = new Image();
        img.crossOrigin = 'anonymous';

        img.onload = () => {
            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');

            if (!ctx) {
                reject(new Error('Canvas context not available'));
                return;
            }

            // Set small canvas size for blur effect
            canvas.width = 20;
            canvas.height = 20;

            // Draw and blur the image
            ctx.filter = 'blur(10px)';
            ctx.drawImage(img, 0, 0, 20, 20);

            // Convert to base64
            const blurDataUrl = canvas.toDataURL('image/jpeg', 0.5);
            resolve(blurDataUrl);
        };

        img.onerror = () => {
            reject(new Error('Failed to load image'));
        };

        img.src = imageUrl;
    });
}

/**
 * Preload images for better performance
 */
export function preloadImages(urls: string[]): Promise<void[]> {
    const promises = urls.map((url) => {
        return new Promise<void>((resolve, reject) => {
            const img = new Image();
            img.onload = () => resolve();
            img.onerror = () => reject(new Error(`Failed to preload ${url}`));
            img.src = url;
        });
    });

    return Promise.all(promises);
}

/**
 * Get optimal image size based on container width
 */
export function getOptimalImageSize(containerWidth: number, imageVariants: ImageVariants): keyof ImageVariants {
    if (containerWidth <= 300 && imageVariants.thumbnail) {
        return 'thumbnail';
    } else if (containerWidth <= 800 && imageVariants.medium) {
        return 'medium';
    } else if (imageVariants.original) {
        return 'original';
    } else if (imageVariants.medium) {
        return 'medium';
    } else {
        return 'thumbnail';
    }
}

/**
 * Format file size for display
 */
export function formatFileSize(bytes: number): string {
    if (bytes === 0) return '0 Bytes';

    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));

    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

/**
 * Validate image file
 */
export function validateImageFile(
    file: File,
    options: {
        maxSize?: number; // in MB
        allowedTypes?: string[];
    } = {},
): { valid: boolean; error?: string } {
    const { maxSize = 10, allowedTypes = ['image/jpeg', 'image/png', 'image/jpg', 'image/gif', 'image/webp'] } = options;

    // Check file type
    if (!allowedTypes.includes(file.type)) {
        return {
            valid: false,
            error: `Format file tidak didukung. Gunakan: ${allowedTypes.join(', ')}`,
        };
    }

    // Check file size
    const fileSizeMB = file.size / (1024 * 1024);
    if (fileSizeMB > maxSize) {
        return {
            valid: false,
            error: `Ukuran file terlalu besar. Maksimal ${maxSize}MB`,
        };
    }

    return { valid: true };
}
