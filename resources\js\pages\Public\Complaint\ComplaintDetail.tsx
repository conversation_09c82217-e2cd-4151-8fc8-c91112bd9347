import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { useSettings } from '@/hooks/useSettings';
import PublicLayout from '@/layouts/PublicLayout';
import { Head, Link } from '@inertiajs/react';
import { AlertCircle, ArrowLeft, Calendar, CheckCircle, Clock, Download, Image, MessageSquare, User, XCircle } from 'lucide-react';

interface User {
    id: number;
    name: string;
}

interface Complaint {
    id: number;
    ticket_number: string;
    name: string;
    email?: string;
    phone?: string;
    category: string;
    subject: string;
    description: string;
    attachments?: Array<{
        path: string;
        original_name: string;
        size: number;
        mime_type: string;
    }>;
    status: string;

    priority: string;

    admin_response?: string;
    responded_at?: string;
    responded_by?: User;
    created_at: string;
}

interface ComplaintDetailProps {
    complaint: Complaint;
    pageTitle: string;
    pageDescription: string;
}

export default function ComplaintDetail({ complaint, pageTitle }: ComplaintDetailProps) {
    const getStatusIcon = (status: string) => {
        switch (status) {
            case 'pending':
                return <Clock className="h-4 w-4" />;
            case 'in_progress':
                return <AlertCircle className="h-4 w-4" />;
            case 'resolved':
                return <CheckCircle className="h-4 w-4" />;
            case 'closed':
                return <XCircle className="h-4 w-4" />;
            default:
                return <Clock className="h-4 w-4" />;
        }
    };

    const getStatusColor = (status: string) => {
        switch (status) {
            case 'pending':
                return 'bg-yellow-100 text-yellow-800 border-yellow-200 dark:bg-yellow-900 dark:text-yellow-300 dark:border-yellow-700';
            case 'in_progress':
                return 'bg-blue-100 text-blue-800 border-blue-200 dark:bg-blue-900 dark:text-blue-300 dark:border-blue-700';
            case 'resolved':
                return 'bg-green-100 text-green-800 border-green-200 dark:bg-green-900 dark:text-green-300 dark:border-green-700';
            case 'closed':
                return 'bg-gray-100 text-gray-800 border-gray-200 dark:bg-neutral-700 dark:text-gray-300 dark:border-neutral-600';
            default:
                return 'bg-gray-100 text-gray-800 border-gray-200 dark:bg-neutral-700 dark:text-gray-300 dark:border-neutral-600';
        }
    };

    const getPriorityColor = (priority: string) => {
        switch (priority) {
            case 'low':
                return 'bg-green-100 text-green-800 border-green-200 dark:bg-green-900 dark:text-green-300 dark:border-green-700';
            case 'medium':
                return 'bg-yellow-100 text-yellow-800 border-yellow-200 dark:bg-yellow-900 dark:text-yellow-300 dark:border-yellow-700';
            case 'high':
                return 'bg-orange-100 text-orange-800 border-orange-200 dark:bg-orange-900 dark:text-orange-300 dark:border-orange-700';
            case 'urgent':
                return 'bg-red-100 text-red-800 border-red-200 dark:bg-red-900 dark:text-red-300 dark:border-red-700';
            default:
                return 'bg-gray-100 text-gray-800 border-gray-200 dark:bg-neutral-700 dark:text-gray-300 dark:border-neutral-600';
        }
    };

    const formatFileSize = (bytes: number): string => {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    };

    const formatDate = (dateString: string) => {
        return new Date(dateString).toLocaleDateString('id-ID', {
            day: 'numeric',
            month: 'long',
            year: 'numeric',
            hour: '2-digit',
            minute: '2-digit',
        });
    };

    return (
        <PublicLayout>
            <Head title={pageTitle} />

            <div className="min-h-screen bg-gray-50 py-8 dark:bg-neutral-900">
                <div className="container mx-auto px-4">
                    {/* Header */}
                    <div className="mb-6">
                        <Button asChild variant="ghost" className="mb-4 text-gray-900 dark:text-gray-100">
                            <Link href={route('complaints.tracking')}>
                                <ArrowLeft className="mr-2 h-4 w-4" />
                                Kembali ke Pencarian
                            </Link>
                        </Button>

                        <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
                            <div>
                                <h1 className="text-2xl font-bold text-gray-900 dark:text-gray-100">Detail Pengaduan</h1>
                                <p className="text-gray-600 dark:text-gray-300">
                                    Nomor Tiket: <span className="font-mono font-semibold dark:text-blue-400">{complaint.ticket_number}</span>
                                </p>
                            </div>

                            <div className="flex items-center gap-2">
                                <Badge className={`${getStatusColor(complaint.status)} border`}>
                                    {getStatusIcon(complaint.status)}
                                    <span className="ml-1">{complaint.status}</span>
                                </Badge>
                                <Badge className={`${getPriorityColor(complaint.priority)} border`}>{complaint.priority}</Badge>
                            </div>
                        </div>
                    </div>

                    <div className="mx-auto grid max-w-4xl grid-cols-1 gap-6 lg:grid-cols-3">
                        {/* Main Content */}
                        <div className="space-y-6 lg:col-span-2">
                            {/* Complaint Details */}
                            <Card className="dark:border-neutral-700 dark:bg-neutral-800">
                                <CardHeader>
                                    <CardTitle className="dark:text-gray-100">{complaint.subject}</CardTitle>
                                    <CardDescription className="dark:text-gray-300">Kategori: {complaint.category}</CardDescription>
                                </CardHeader>
                                <CardContent>
                                    <div className="prose prose-sm max-w-none">
                                        <p className="whitespace-pre-wrap text-gray-700 dark:text-gray-300">{complaint.description}</p>
                                    </div>
                                </CardContent>
                            </Card>

                            {/* Attachments */}
                            {complaint.attachments && complaint.attachments.length > 0 && (
                                <Card className="dark:border-neutral-700 dark:bg-neutral-800">
                                    <CardHeader>
                                        <CardTitle className="flex items-center gap-2 dark:text-gray-100">
                                            <Image className="h-5 w-5" />
                                            Foto Bukti ({complaint.attachments.length})
                                        </CardTitle>
                                        <CardDescription className="dark:text-gray-300">
                                            Klik pada foto untuk melihat dalam ukuran penuh
                                        </CardDescription>
                                    </CardHeader>
                                    <CardContent>
                                        <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
                                            {complaint.attachments.map((file, index) => {
                                                const imageUrl = file.path.startsWith('http')
                                                    ? file.path
                                                    : `/storage/${file.path.replace(/^public\//, '')}`;

                                                return (
                                                    <div
                                                        key={`${complaint.id}-${index}`}
                                                        className="rounded-lg border bg-white p-3 shadow-sm dark:border-neutral-600 dark:bg-neutral-700"
                                                    >
                                                        <div className="space-y-3">
                                                            <div className="relative h-48 w-full overflow-hidden rounded-lg bg-gray-100 dark:bg-neutral-600">
                                                                <a href={imageUrl} target="_blank" rel="noopener noreferrer">
                                                                    <img
                                                                        src={imageUrl}
                                                                        alt={file.original_name}
                                                                        className="h-full w-full object-cover transition-transform duration-200 hover:scale-105"
                                                                        onError={(e) => {
                                                                            console.error('Image failed to load:', imageUrl);
                                                                            e.currentTarget.src = '/images/placeholder-image.jpg';
                                                                        }}
                                                                    />
                                                                </a>
                                                            </div>
                                                            <div className="flex items-start justify-between gap-2">
                                                                <div className="flex min-w-0 flex-1 items-start gap-2">
                                                                    <Image className="mt-0.5 h-4 w-4 flex-shrink-0 text-green-600 dark:text-green-300" />
                                                                    <div className="min-w-0 flex-1">
                                                                        <p
                                                                            className="line-clamp-2 text-sm font-medium break-words text-gray-900 dark:text-gray-100"
                                                                            title={file.original_name}
                                                                        >
                                                                            {file.original_name}
                                                                        </p>
                                                                        <p className="text-xs text-gray-500 dark:text-gray-300">
                                                                            {formatFileSize(file.size)} • {file.mime_type.split('/')[1].toUpperCase()}
                                                                        </p>
                                                                    </div>
                                                                </div>
                                                                <Button
                                                                    asChild
                                                                    variant="ghost"
                                                                    size="sm"
                                                                    className="flex-shrink-0 text-gray-900 dark:text-gray-100"
                                                                >
                                                                    <a href={imageUrl} target="_blank" rel="noopener noreferrer">
                                                                        <Download className="h-4 w-4" />
                                                                    </a>
                                                                </Button>
                                                            </div>
                                                        </div>
                                                    </div>
                                                );
                                            })}
                                        </div>
                                    </CardContent>
                                </Card>
                            )}

                            {/* Admin Response */}
                            {complaint.admin_response && (
                                <Card className="dark:border-neutral-700 dark:bg-neutral-800">
                                    <CardHeader>
                                        <CardTitle className="flex items-center gap-2 dark:text-gray-100">
                                            <MessageSquare className="h-5 w-5" />
                                            Respon Petugas
                                        </CardTitle>
                                        {complaint.responded_at && (
                                            <CardDescription className="dark:text-gray-300">
                                                Direspon pada {formatDate(complaint.responded_at)}
                                                {complaint.responded_by && ` oleh ${complaint.responded_by.name}`}
                                            </CardDescription>
                                        )}
                                    </CardHeader>
                                    <CardContent>
                                        <div className="rounded-lg border border-blue-200 bg-blue-50 p-4 dark:border-blue-700 dark:bg-blue-900">
                                            <p className="whitespace-pre-wrap text-gray-700 dark:text-blue-200">{complaint.admin_response}</p>
                                        </div>
                                    </CardContent>
                                </Card>
                            )}
                        </div>

                        {/* Sidebar */}
                        <div className="space-y-6">
                            {/* Contact Information */}
                            <Card className="dark:border-neutral-700 dark:bg-neutral-800">
                                <CardHeader>
                                    <CardTitle className="flex items-center gap-2 dark:text-gray-100">
                                        <User className="h-5 w-5" />
                                        Informasi Pengadu
                                    </CardTitle>
                                </CardHeader>
                                <CardContent className="space-y-3">
                                    <div>
                                        <p className="text-sm font-medium text-gray-500 dark:text-gray-300">Nama</p>
                                        <p className="text-gray-900 dark:text-gray-100">{complaint.name}</p>
                                    </div>

                                    {complaint.email && (
                                        <div>
                                            <p className="text-sm font-medium text-gray-500 dark:text-gray-300">Email</p>
                                            <p className="text-gray-900 dark:text-gray-100">{complaint.email}</p>
                                        </div>
                                    )}

                                    {complaint.phone && (
                                        <div>
                                            <p className="text-sm font-medium text-gray-500 dark:text-gray-300">Telepon</p>
                                            <p className="text-gray-900 dark:text-gray-100">{complaint.phone}</p>
                                        </div>
                                    )}
                                </CardContent>
                            </Card>

                            {/* Timeline */}
                            <Card className="dark:border-neutral-700 dark:bg-neutral-800">
                                <CardHeader>
                                    <CardTitle className="flex items-center gap-2 dark:text-gray-100">
                                        <Calendar className="h-5 w-5" />
                                        Timeline
                                    </CardTitle>
                                </CardHeader>
                                <CardContent>
                                    <div className="space-y-4">
                                        <div className="flex items-start gap-3">
                                            <div className="mt-2 h-2 w-2 flex-shrink-0 rounded-full bg-blue-600 dark:bg-blue-400"></div>
                                            <div>
                                                <p className="text-sm font-medium text-gray-900 dark:text-gray-100">Pengaduan Dikirim</p>
                                                <p className="text-xs text-gray-500 dark:text-gray-300">{formatDate(complaint.created_at)}</p>
                                            </div>
                                        </div>

                                        {complaint.responded_at && (
                                            <div className="flex items-start gap-3">
                                                <div className="mt-2 h-2 w-2 flex-shrink-0 rounded-full bg-green-600 dark:bg-green-400"></div>
                                                <div>
                                                    <p className="text-sm font-medium text-gray-900 dark:text-gray-100">Respon Diberikan</p>
                                                    <p className="text-xs text-gray-500 dark:text-gray-300">{formatDate(complaint.responded_at)}</p>
                                                </div>
                                            </div>
                                        )}
                                    </div>
                                </CardContent>
                            </Card>

                            {/* Help */}
                            <Card className="dark:border-neutral-700 dark:bg-neutral-800">
                                <CardHeader>
                                    <CardTitle className="dark:text-gray-100">Butuh Bantuan?</CardTitle>
                                </CardHeader>
                                <CardContent className="space-y-3 text-sm">
                                    <p className="text-gray-600 dark:text-gray-300">
                                        Jika Anda memiliki pertanyaan atau memerlukan klarifikasi lebih lanjut, silakan hubungi kami:
                                    </p>

                                    <div className="space-y-2">
                                        <p className="text-gray-900 dark:text-gray-100">
                                            📞 {useSettings().getContactInfo()?.phone || 'Belum tersedia'}
                                        </p>
                                        <p className="text-gray-900 dark:text-gray-100">
                                            ✉️ {useSettings().getContactInfo()?.email || 'Belum tersedia'}
                                        </p>
                                    </div>

                                    <p className="text-xs text-gray-500 dark:text-gray-300">Jam operasional: Senin - Jumat, 08:00 - 16:00 WIB</p>
                                </CardContent>
                            </Card>
                        </div>
                    </div>
                </div>
            </div>
        </PublicLayout>
    );
}
