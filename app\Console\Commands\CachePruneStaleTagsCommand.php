<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Cache;

class CachePruneStaleTagsCommand extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'cache:prune-stale-tags';

    /**
     * The console command description.
     */
    protected $description = 'Prune stale cache entries and tags';

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $this->info('🧹 Pruning stale cache entries...');

        // Clear expired cache entries
        $this->pruneExpiredEntries();

        // Clear specific cache tags that might be stale
        $this->pruneStaleTaggedCache();

        $this->info('✅ Cache pruning completed!');

        return Command::SUCCESS;
    }

    /**
     * Prune expired cache entries
     */
    private function pruneExpiredEntries(): void
    {
        try {
            // For database cache driver, we can clean up expired entries
            if (config('cache.default') === 'database') {
                $deleted = \DB::table(config('cache.stores.database.table', 'cache'))
                    ->where('expiration', '<', time())
                    ->delete();

                $this->line("  • Removed {$deleted} expired cache entries");
            }

        } catch (\Exception $e) {
            $this->warn("Could not prune expired entries: {$e->getMessage()}");
        }
    }

    /**
     * Prune stale tagged cache entries
     */
    private function pruneStaleTaggedCache(): void
    {
        $staleTags = [
            'homepage.old_news',
            'services.inactive',
            'potentials.hidden',
            'profile.outdated',
            'temp_uploads',
        ];

        foreach ($staleTags as $tag) {
            try {
                Cache::tags($tag)->flush();
                $this->line("  • Cleared cache tag: {$tag}");
            } catch (\Exception $e) {
                // Silently continue if tag doesn't exist
            }
        }
    }
}
