<?php

namespace App\Providers;

use App\Models\Complaint;
use App\Models\News;
use App\Models\Potential;
use App\Models\Profile;
use App\Models\Service;
use App\Observers\ComplaintObserver;
use App\Observers\NewsObserver;
use App\Observers\PotentialObserver;
use App\Observers\ProfileObserver;
use App\Observers\ServiceObserver;
use Illuminate\Support\ServiceProvider;

class ObserverServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        Complaint::observe(ComplaintObserver::class);
        News::observe(NewsObserver::class);
        Service::observe(ServiceObserver::class);
        Potential::observe(PotentialObserver::class);
        Profile::observe(ProfileObserver::class);
    }
}
