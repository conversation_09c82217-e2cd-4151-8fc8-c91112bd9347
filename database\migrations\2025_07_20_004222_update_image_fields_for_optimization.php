<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Update news table - change featured_image to JSON for storing image variants
        Schema::table('news', function (Blueprint $table) {
            $table->json('featured_image')->nullable()->change();
        });

        // Update potentials table - ensure images field is JSON
        Schema::table('potentials', function (Blueprint $table) {
            $table->json('images')->nullable()->change();
        });

        // Update profiles table - change image to JSON for storing image variants
        Schema::table('profiles', function (Blueprint $table) {
            $table->json('image')->nullable()->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Revert news table
        Schema::table('news', function (Blueprint $table) {
            $table->string('featured_image')->nullable()->change();
        });

        // Revert potentials table
        Schema::table('potentials', function (Blueprint $table) {
            $table->json('images')->nullable()->change(); // Keep as JSON since it was already JSON
        });

        // Revert profiles table
        Schema::table('profiles', function (Blueprint $table) {
            $table->string('image')->nullable()->change();
        });
    }
};
