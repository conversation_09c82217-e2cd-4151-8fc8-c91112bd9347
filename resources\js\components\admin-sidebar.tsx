import { NavFooter } from '@/components/nav-footer';
import { NavMain } from '@/components/nav-main';
import { NavUser } from '@/components/nav-user';
import { Sidebar, SidebarContent, SidebarFooter, SidebarHeader, SidebarMenu, SidebarMenuButton, SidebarMenuItem } from '@/components/ui/sidebar';
import { useSettings } from '@/hooks/useSettings';
import { type NavItem } from '@/types';
import { Link } from '@inertiajs/react';
import { Building2, Cog, FileText, Globe, Home, LayoutDashboard, MapPin, MessageSquare, Settings, Users } from 'lucide-react';

const mainNavItems: NavItem[] = [
    {
        title: 'Dashboard',
        href: '/dashboard',
        icon: LayoutDashboard,
    },
    {
        title: 'Manajemen Berita',
        href: '/admin/news',
        icon: FileText,
    },
    {
        title: '<PERSON>anan Publik',
        href: '/admin/services',
        icon: Settings,
    },
    {
        title: 'Potensi <PERSON>a',
        href: '/admin/potentials',
        icon: MapPin,
    },
    {
        title: 'Profil Desa',
        href: '/admin/profiles',
        icon: Building2,
    },
    {
        title: 'Manajemen Pengguna',
        href: '/admin/users',
        icon: Users,
    },
    {
        title: 'Pengaduan Masyarakat',
        href: '/admin/complaints',
        icon: MessageSquare,
    },
    {
        title: 'Pengaturan',
        href: '/admin/settings',
        icon: Cog,
    },
];

const footerNavItems: NavItem[] = [
    {
        title: 'Lihat Website',
        href: '/',
        icon: Globe,
    },
    {
        title: 'Beranda Publik',
        href: '/',
        icon: Home,
    },
];

export function AdminSidebar() {
    return (
        <Sidebar collapsible="icon" variant="inset">
            <SidebarHeader>
                <SidebarMenu>
                    <SidebarMenuItem>
                        <SidebarMenuButton size="lg" asChild>
                            <Link href="/dashboard" prefetch>
                                <div className="flex h-8 w-8 flex-shrink-0 items-center justify-center sm:h-9 sm:w-9">
                                    <img src="/images/logo-kabupaten-bogor.png" alt="logo kabupaten bogor" />
                                </div>
                                <div className="grid flex-1 text-left text-sm leading-tight">
                                    <span className="truncate font-semibold">Admin Panel</span>
                                    <span className="truncate text-xs">{useSettings().getVillageProfile()?.name || 'Desa Lemah Duhur'}</span>
                                </div>
                            </Link>
                        </SidebarMenuButton>
                    </SidebarMenuItem>
                </SidebarMenu>
            </SidebarHeader>

            <SidebarContent>
                <NavMain items={mainNavItems} />
            </SidebarContent>

            <SidebarFooter>
                <NavFooter items={footerNavItems} className="mt-auto" />
                <NavUser />
            </SidebarFooter>
        </Sidebar>
    );
}
