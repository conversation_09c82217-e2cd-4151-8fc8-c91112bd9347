import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';

interface OrganizationData {
    name: string;
    position: string;
    period?: string;
    description?: string;
}

interface OrganizationFormProps {
    data: OrganizationData;
    onChange: (data: OrganizationData) => void;
    errors?: {
        name?: string;
        position?: string;
        period?: string;
        description?: string;
    };
}

export function OrganizationForm({ data, onChange, errors }: OrganizationFormProps) {
    const updateField = (field: keyof OrganizationData, value: string) => {
        onChange({
            ...data,
            [field]: value,
        });
    };

    return (
        <div className="space-y-4">
            <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                {/* Name */}
                <div className="space-y-2">
                    <Label htmlFor="name">Nama Lengkap *</Label>
                    <Input
                        id="name"
                        value={data.name}
                        onChange={(e) => updateField('name', e.target.value)}
                        placeholder="Contoh: UJANG NAJMUDIN"
                        className={errors?.name ? 'border-red-500' : ''}
                    />
                    {errors?.name && <p className="text-sm text-red-600">{errors.name}</p>}
                </div>

                {/* Position */}
                <div className="space-y-2">
                    <Label htmlFor="position">Jabatan *</Label>
                    <Input
                        id="position"
                        value={data.position}
                        onChange={(e) => updateField('position', e.target.value)}
                        placeholder="Contoh: Kepala Desa Lemah Duhur"
                        className={errors?.position ? 'border-red-500' : ''}
                    />
                    {errors?.position && <p className="text-sm text-red-600">{errors.position}</p>}
                </div>
            </div>

            {/* Period (Optional) */}
            <div className="space-y-2">
                <Label htmlFor="period">Periode (Opsional)</Label>
                <Input
                    id="period"
                    value={data.period || ''}
                    onChange={(e) => updateField('period', e.target.value)}
                    placeholder="Contoh: 2019-2025"
                    className={errors?.period ? 'border-red-500' : ''}
                />
                {errors?.period && <p className="text-sm text-red-600">{errors.period}</p>}
                <p className="text-xs text-gray-500">Kosongkan jika tidak ada periode tertentu</p>
            </div>

            {/* Description (Optional) */}
            <div className="space-y-2">
                <Label htmlFor="description">Keterangan Tambahan (Opsional)</Label>
                <Textarea
                    id="description"
                    value={data.description || ''}
                    onChange={(e) => updateField('description', e.target.value)}
                    placeholder="Informasi tambahan tentang jabatan atau tugas..."
                    rows={3}
                    className={errors?.description ? 'border-red-500' : ''}
                />
                {errors?.description && <p className="text-sm text-red-600">{errors.description}</p>}
                <p className="text-xs text-gray-500">Informasi tambahan seperti tugas khusus, kontak, dll.</p>
            </div>

            {/* Preview */}
            <div className="space-y-2">
                <Label>Preview</Label>
                <div className="rounded-lg border bg-gray-50 p-4 dark:bg-gray-800">
                    <div className="text-center">
                        <h4 className="text-lg font-semibold">{data.name || 'Nama Lengkap'}</h4>
                        <p className="text-gray-600 dark:text-gray-300">{data.position || 'Jabatan'}</p>
                        {data.period && <p className="text-sm text-gray-500">Periode: {data.period}</p>}
                        {data.description && <p className="mt-2 text-sm text-gray-600 dark:text-gray-400">{data.description}</p>}
                    </div>
                </div>
            </div>
        </div>
    );
}

// Helper function to convert form data to HTML
export function organizationDataToHtml(data: OrganizationData): string {
    let html = '<div class="text-center">';
    html += `<h4>${data.name}</h4>`;
    html += `<p>${data.position}</p>`;

    if (data.period) {
        html += `<p>Periode: ${data.period}</p>`;
    }

    if (data.description) {
        html += `<p>${data.description}</p>`;
    }

    html += '</div>';
    return html;
}

// Helper function to parse HTML back to form data
export function htmlToOrganizationData(html: string): OrganizationData {
    // Create a temporary DOM element to parse HTML
    const tempDiv = document.createElement('div');
    tempDiv.innerHTML = html;

    const h4 = tempDiv.querySelector('h4');
    const paragraphs = tempDiv.querySelectorAll('p');

    const data: OrganizationData = {
        name: h4?.textContent || '',
        position: '',
        period: '',
        description: '',
    };

    // Parse paragraphs to extract position, period, and description
    paragraphs.forEach((p, index) => {
        const text = p.textContent || '';

        if (index === 0) {
            // First paragraph is usually the position
            data.position = text;
        } else if (text.startsWith('Periode:')) {
            // Extract period
            data.period = text.replace('Periode:', '').trim();
        } else if (index > 0 && !text.startsWith('Periode:')) {
            // Additional description
            data.description = data.description ? `${data.description}\n${text}` : text;
        }
    });

    return data;
}
