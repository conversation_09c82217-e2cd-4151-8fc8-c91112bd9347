import { ErrorHandler, handleInertiaError, type ApiError } from '@/utils/errorHandler';
import { useCallback } from 'react';

export function useErrorHandler() {
    const handleError = useCallback((error: unknown): ApiError => {
        return handleInertiaError(error);
    }, []);

    const handleValidationErrors = useCallback((errors: Record<string, string[]>): string => {
        return ErrorHandler.handleValidationErrors(errors);
    }, []);

    const showError = useCallback((error: ApiError) => {
        ErrorHandler.showError(error);
    }, []);

    return {
        handleError,
        handleValidationErrors,
        showError,
    };
}
