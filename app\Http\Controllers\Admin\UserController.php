<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\UserStoreRequest;
use App\Http\Requests\Admin\UserUpdateRequest;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Inertia\Inertia;

class UserController extends Controller
{
    /**
     * Display a listing of users for admin management
     */
    public function index(Request $request)
    {
        // dd("test");
        $query = User::query()->latest('created_at');

        // Apply role filter
        if ($request->filled('role')) {
            $query->where('role', $request->role);
        }

        // Apply search filter
        if ($request->filled('search')) {
            $searchTerm = $request->search;
            $query->where(function ($q) use ($searchTerm) {
                $q->where('name', 'like', "%{$searchTerm}%")
                    ->orWhere('email', 'like', "%{$searchTerm}%");
            });
        }

        // Get paginated results
        $users = $query->select([
            'id', 'name', 'email', 'role', 'email_verified_at', 'created_at', 'updated_at',
        ])->paginate(10);

        // Get available roles based on current user permissions
        $roles = [
            User::USER_ROLE => 'User',
            User::ADMIN_ROLE => 'Admin',
        ];

        // Only superadmins can see and assign superadmin role
        if (auth()->user()->isSuperAdmin()) {
            $roles[User::SUPERADMIN_ROLE] = 'Super Admin';
        }

        return Inertia::render('Admin/Users/<USER>', [
            'users' => $users,
            'roles' => $roles,
            'filters' => [
                'role' => $request->role,
                'search' => $request->search,
            ],
            'auth' => [
                'user' => auth()->user()->only(['id', 'name', 'email', 'role']),
            ],
        ]);
    }

    /**
     * Show the form for creating a new user
     */
    public function create()
    {
        // Get available roles based on current user permissions
        $roles = [
            User::USER_ROLE => 'User',
            User::ADMIN_ROLE => 'Admin',
        ];

        // Only superadmins can create superadmin accounts
        if (auth()->user()->isSuperAdmin()) {
            $roles[User::SUPERADMIN_ROLE] = 'Super Admin';
        }

        return Inertia::render('Admin/Users/<USER>', [
            'roles' => $roles,
            'defaultRole' => User::USER_ROLE,
        ]);
    }

    /**
     * Store a newly created user
     */
    public function store(UserStoreRequest $request)
    {
        $validated = $request->validated();

        // Check if trying to create superadmin without permission
        if ($validated['role'] === User::SUPERADMIN_ROLE && ! auth()->user()->isSuperAdmin()) {
            return redirect()->back()
                ->withErrors(['role' => 'Anda tidak memiliki izin untuk membuat akun Super Admin.'])
                ->withInput();
        }

        // Hash the password
        $validated['password'] = Hash::make($validated['password']);

        // Create the user
        $user = User::create($validated);

        return redirect()->route('admin.users.index')
            ->with('success', 'Pengguna berhasil dibuat.');
    }

    /**
     * Display the specified user
     */
    public function show(User $user)
    {
        return Inertia::render('Admin/Users/<USER>', [
            'user' => $user->only(['id', 'name', 'email', 'role', 'email_verified_at', 'created_at', 'updated_at']),
        ]);
    }

    /**
     * Show the form for editing the specified user
     */
    public function edit(User $user)
    {
        // Check if current user can edit this user
        if ($user->isAdmin() && ! auth()->user()->canEditAdmins()) {
            return redirect()->route('admin.users.index')
                ->with('error', 'Anda tidak memiliki izin untuk mengedit admin lain.');
        }

        // Get available roles based on current user permissions
        $roles = [
            User::USER_ROLE => 'User',
            User::ADMIN_ROLE => 'Admin',
        ];

        // Only superadmins can assign superadmin role
        if (auth()->user()->isSuperAdmin()) {
            $roles[User::SUPERADMIN_ROLE] = 'Super Admin';
        }

        return Inertia::render('Admin/Users/<USER>', [
            'user' => $user->only(['id', 'name', 'email', 'role']),
            'roles' => $roles,
            'canEditRole' => auth()->user()->canEditAdmins() || ! $user->isAdmin(),
        ]);
    }

    /**
     * Update the specified user
     */
    public function update(UserUpdateRequest $request, User $user)
    {
        // Check if current user can edit this user
        if ($user->isAdmin() && ! auth()->user()->canEditAdmins()) {
            return redirect()->route('admin.users.index')
                ->with('error', 'Anda tidak memiliki izin untuk mengedit admin lain.');
        }

        $validated = $request->validated();

        // Check if trying to assign superadmin role without permission
        if ($validated['role'] === User::SUPERADMIN_ROLE && ! auth()->user()->isSuperAdmin()) {
            return redirect()->back()
                ->withErrors(['role' => 'Anda tidak memiliki izin untuk menetapkan peran Super Admin.'])
                ->withInput();
        }

        // Prevent regular admins from changing admin roles
        if ($user->isAdmin() && ! auth()->user()->canEditAdmins() && $validated['role'] !== $user->role) {
            return redirect()->back()
                ->withErrors(['role' => 'Anda tidak memiliki izin untuk mengubah peran admin.'])
                ->withInput();
        }

        // Only hash and update password if provided
        if (! empty($validated['password'])) {
            $validated['password'] = Hash::make($validated['password']);
        } else {
            unset($validated['password']);
        }

        // Update the user
        $user->update($validated);

        return redirect()->route('admin.users.index')
            ->with('success', 'Pengguna berhasil diperbarui.');
    }

    /**
     * Remove the specified user from storage
     */
    public function destroy(User $user)
    {
        // Prevent deleting own account
        if ($user->id === auth()->id()) {
            return redirect()->route('admin.users.index')
                ->with('error', 'Anda tidak dapat menghapus akun Anda sendiri.');
        }

        // Check if current user can delete this user
        if ($user->isAdmin() && ! auth()->user()->canEditAdmins()) {
            return redirect()->route('admin.users.index')
                ->with('error', 'Anda tidak memiliki izin untuk menghapus admin lain.');
        }

        // Delete the user
        $user->delete();

        return redirect()->route('admin.users.index')
            ->with('success', 'Pengguna berhasil dihapus.');
    }
}
