import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import PublicLayout from '@/layouts/PublicLayout';
import { Head, useForm } from '@inertiajs/react';
import { FormEventHandler } from 'react';

export default function ConfirmPassword() {
    const { data, setData, post, processing, errors, reset } = useForm({
        password: '',
    });

    const submit: FormEventHandler = (e) => {
        e.preventDefault();

        post(route('password.confirm'), {
            onFinish: () => reset('password'),
        });
    };

    return (
        <PublicLayout>
            <Head title="Konfirmasi Password" />

            <div className="mb-4 text-sm text-gray-600">
                Ini adalah area aman dari aplikasi. Silakan konfirmasi password Anda sebelum melanjutkan.
            </div>

            <form onSubmit={submit}>
                <div>
                    <Label htmlFor="password">Password</Label>

                    <Input
                        id="password"
                        type="password"
                        name="password"
                        value={data.password}
                        className="mt-1 block w-full"
                        onChange={(e) => setData('password', e.target.value)}
                    />

                    {errors.password && <div className="mt-2 text-sm text-red-600">{errors.password}</div>}
                </div>

                <div className="mt-4 flex justify-end">
                    <Button className="ml-4" disabled={processing}>
                        Konfirmasi
                    </Button>
                </div>
            </form>
        </PublicLayout>
    );
}
