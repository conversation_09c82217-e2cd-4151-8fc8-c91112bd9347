<?php

namespace Tests\Feature;

use App\Models\Complaint;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class PublicComplaintsTest extends TestCase
{
    use RefreshDatabase;

    public function test_can_access_public_complaints_page()
    {
        $response = $this->get(route('complaints.public'));

        $response->assertStatus(200);
        $response->assertInertia(fn ($page) => $page->component('Public/Complaint/ComplaintsPublic')
            ->has('complaints')
            ->has('stats')
            ->has('categories')
            ->has('statuses')
            ->has('priorities')
        );
    }

    public function test_only_public_complaints_are_shown()
    {
        // Create public and private complaints
        $publicComplaint = Complaint::factory()->create([
            'visibility' => 'public',
            'subject' => 'Public Complaint',
        ]);

        $privateComplaint = Complaint::factory()->create([
            'visibility' => 'private',
            'subject' => 'Private Complaint',
        ]);

        $response = $this->get(route('complaints.public'));

        $response->assertStatus(200);
        $response->assertInertia(fn ($page) => $page->has('complaints.data', 1)
            ->where('complaints.data.0.subject', 'Public Complaint')
        );
    }

    public function test_public_complaints_can_be_filtered_by_status()
    {
        Complaint::factory()->create([
            'visibility' => 'public',
            'status' => 'pending',
        ]);

        Complaint::factory()->create([
            'visibility' => 'public',
            'status' => 'resolved',
        ]);

        $response = $this->get(route('complaints.public', ['status' => 'pending']));

        $response->assertStatus(200);
        $response->assertInertia(fn ($page) => $page->has('complaints.data', 1)
            ->where('complaints.data.0.status', 'pending')
        );
    }

    public function test_public_complaints_can_be_searched()
    {
        Complaint::factory()->create([
            'visibility' => 'public',
            'subject' => 'Jalan Rusak',
        ]);

        Complaint::factory()->create([
            'visibility' => 'public',
            'subject' => 'Lampu Mati',
        ]);

        $response = $this->get(route('complaints.public', ['search' => 'Jalan']));

        $response->assertStatus(200);
        $response->assertInertia(fn ($page) => $page->has('complaints.data', 1)
            ->where('complaints.data.0.subject', 'Jalan Rusak')
        );
    }

    public function test_public_complaints_stats_only_count_public_complaints()
    {
        // Create 3 public and 2 private complaints
        Complaint::factory()->count(3)->create(['visibility' => 'public']);
        Complaint::factory()->count(2)->create(['visibility' => 'private']);

        $response = $this->get(route('complaints.public'));

        $response->assertStatus(200);
        $response->assertInertia(fn ($page) => $page->where('stats.total', 3)
        );
    }
}
