<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('potentials', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->enum('type', ['tourism', 'umkm']);
            $table->text('description');
            $table->json('images')->nullable(); // Array of image paths
            $table->json('contact_info')->nullable();
            $table->string('location')->nullable();
            $table->boolean('is_featured')->default(false);
            $table->timestamps();

            $table->index(['type', 'is_featured']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('potentials');
    }
};
