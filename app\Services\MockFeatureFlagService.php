<?php

namespace App\Services;

/**
 * Mock Feature Flag Service for Testing
 *
 * This service provides empty implementations to prevent database access during tests
 */
class MockFeatureFlagService extends FeatureFlagService
{
    public function __construct()
    {
        // Don't call parent constructor to avoid database dependencies
    }

    public function getAllFlags(): array
    {
        return [];
    }

    public function getFlagsByCategory(): array
    {
        return [];
    }

    public function updateFlag(string $key, bool $enabled): bool
    {
        return true;
    }

    public function updateMultipleFlags(array $flags): array
    {
        return [];
    }

    public function getFlag(string $key): ?array
    {
        return null;
    }

    public function initializeDefaultFlags(): void
    {
        // Do nothing
    }

    public function getDefaultFlags(): array
    {
        return [];
    }

    public function getCategories(): array
    {
        return [];
    }

    public function isEnabled(string $key): bool
    {
        return false;
    }

    public function getSummary(): array
    {
        return [
            'total' => 0,
            'enabled' => 0,
            'disabled' => 0,
            'byCategory' => [],
        ];
    }

    public function resetFlag(string $key): bool
    {
        return true;
    }

    public function resetAllFlags(): array
    {
        return [];
    }
}
