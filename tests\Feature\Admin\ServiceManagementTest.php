<?php

use App\Models\Service;
use App\Models\User;

describe('Admin Service Management', function () {
    beforeEach(function () {
        $this->user = User::factory()->create(['role' => 'admin']);
        $this->actingAs($this->user);
    });

    describe('Service Creation', function () {
        it('can create a new service', function () {
            $serviceData = [
                'name' => 'Surat Keterangan Test',
                'description' => 'Layanan pembuatan surat keterangan test',
                'requirements' => ['KTP', 'KK', 'Surat RT'],
                'procedure' => ['Datang ke kantor', 'Isi formulir', 'Tunggu proses'],
                'cost' => 10000,
                'processing_time' => '2 hari',
                'contact_info' => [
                    'phone' => '08123456789',
                    'email' => '<EMAIL>',
                    'person' => 'Pak Admin',
                ],
                'is_active' => true,
            ];

            $response = $this->post('/admin/services', $serviceData);

            $response->assertRedirect();
            $this->assertDatabaseHas('services', [
                'name' => 'Surat Keterangan Test',
                'cost' => 10000,
                'is_active' => true,
            ]);
        });

        it('validates required fields', function () {
            $response = $this->post('/admin/services', []);

            $response->assertSessionHasErrors(['name', 'description']);
        });

        it('validates service name uniqueness', function () {
            Service::factory()->create(['name' => 'Layanan Sama']);

            $response = $this->post('/admin/services', [
                'name' => 'Layanan Sama',
                'description' => 'Deskripsi berbeda',
            ]);

            $response->assertSessionHasErrors(['name']);
        });

        it('handles array fields correctly', function () {
            $serviceData = [
                'name' => 'Test Service',
                'description' => 'Test description',
                'requirements' => ['Requirement 1', 'Requirement 2'],
                'procedure' => ['Step 1', 'Step 2', 'Step 3'],
                'processing_time' => '2 hari',
                'contact_info' => [
                    'phone' => '08123456789',
                    'email' => '<EMAIL>',
                ],
            ];

            $response = $this->post('/admin/services', $serviceData);

            $response->assertRedirect();

            $service = Service::where('name', 'Test Service')->first();
            expect($service->requirements)->toBe(['Requirement 1', 'Requirement 2']);
            expect($service->procedure)->toBe(['Step 1', 'Step 2', 'Step 3']);
            expect($service->contact_info['phone'])->toBe('08123456789');
        });
    });

    describe('Service Update', function () {
        it('can update existing service', function () {
            $service = Service::factory()->create([
                'name' => 'Layanan Lama',
                'cost' => 5000,
            ]);

            $updateData = [
                'name' => 'Layanan Baru',
                'description' => 'Deskripsi baru',
                'requirements' => ['KTP', 'KK'],
                'procedure' => ['Datang ke kantor', 'Isi formulir'],
                'processing_time' => '3 hari',
                'cost' => 15000,
                'is_active' => false,
            ];

            $response = $this->put("/admin/services/{$service->id}", $updateData);

            $response->assertRedirect();
            $this->assertDatabaseHas('services', [
                'id' => $service->id,
                'name' => 'Layanan Baru',
                'cost' => 15000,
                'is_active' => false,
            ]);
        });

        it('can update requirements and procedures', function () {
            $service = Service::factory()->create([
                'requirements' => ['Old Req 1', 'Old Req 2'],
                'procedure' => ['Old Step 1'],
            ]);

            $updateData = [
                'name' => $service->name,
                'description' => $service->description,
                'requirements' => ['New Req 1', 'New Req 2', 'New Req 3'],
                'procedure' => ['New Step 1', 'New Step 2'],
                'processing_time' => $service->processing_time,
            ];

            $response = $this->put("/admin/services/{$service->id}", $updateData);

            $response->assertRedirect();

            $updatedService = $service->fresh();
            expect($updatedService->requirements)->toBe(['New Req 1', 'New Req 2', 'New Req 3']);
            expect($updatedService->procedure)->toBe(['New Step 1', 'New Step 2']);
        });

        it('validates update data', function () {
            $service = Service::factory()->create();

            $response = $this->put("/admin/services/{$service->id}", [
                'name' => '', // Empty name
                'description' => '', // Empty description
            ]);

            $response->assertSessionHasErrors(['name', 'description']);
        });
    });

    describe('Service Deletion', function () {
        it('can delete service', function () {
            $service = Service::factory()->create();

            $response = $this->delete("/admin/services/{$service->id}");

            $response->assertRedirect();
            $this->assertDatabaseMissing('services', ['id' => $service->id]);
        });

        it('prevents deletion of service with dependencies', function () {
            $service = Service::factory()->create();

            // Simulate service being referenced elsewhere
            // This would depend on your actual business logic

            $response = $this->delete("/admin/services/{$service->id}");

            // Should either prevent deletion or handle gracefully
            $response->assertRedirect();
        });
    });

    describe('Service Status Toggle', function () {
        it('can activate service', function () {
            $service = Service::factory()->inactive()->create();

            $response = $this->post("/admin/services/{$service->id}/activate");

            $response->assertRedirect();
            expect($service->fresh()->is_active)->toBe(true);
        });

        it('can deactivate service', function () {
            $service = Service::factory()->active()->create();

            $response = $this->post("/admin/services/{$service->id}/deactivate");

            $response->assertRedirect();
            expect($service->fresh()->is_active)->toBe(false);
        });
    });

    describe('Bulk Operations', function () {
        it('can activate multiple services', function () {
            $service1 = Service::factory()->inactive()->create();
            $service2 = Service::factory()->inactive()->create();

            $response = $this->post('/admin/services/bulk-activate', [
                'service_ids' => [$service1->id, $service2->id],
            ]);

            $response->assertRedirect();

            expect($service1->fresh()->is_active)->toBe(true);
            expect($service2->fresh()->is_active)->toBe(true);
        });

        it('can deactivate multiple services', function () {
            $service1 = Service::factory()->active()->create();
            $service2 = Service::factory()->active()->create();

            $response = $this->post('/admin/services/bulk-deactivate', [
                'service_ids' => [$service1->id, $service2->id],
            ]);

            $response->assertRedirect();

            expect($service1->fresh()->is_active)->toBe(false);
            expect($service2->fresh()->is_active)->toBe(false);
        });

        it('can delete multiple services', function () {
            $service1 = Service::factory()->create();
            $service2 = Service::factory()->create();

            $response = $this->post('/admin/services/bulk-delete', [
                'service_ids' => [$service1->id, $service2->id],
            ]);

            $response->assertRedirect();

            $this->assertDatabaseMissing('services', ['id' => $service1->id]);
            $this->assertDatabaseMissing('services', ['id' => $service2->id]);
        });
    });

    describe('Form Validation', function () {
        it('validates cost as numeric', function () {
            $response = $this->post('/admin/services', [
                'name' => 'Test Service',
                'description' => 'Test description',
                'requirements' => ['KTP'],
                'procedure' => ['Datang ke kantor'],
                'processing_time' => '2 hari',
                'cost' => 'not-a-number',
            ]);

            $response->assertSessionHasErrors(['cost']);
        });

        it('validates processing time format', function () {
            $response = $this->post('/admin/services', [
                'name' => 'Test Service',
                'description' => 'Test description',
                'processing_time' => str_repeat('a', 256), // Too long
            ]);

            $response->assertSessionHasErrors(['processing_time']);
        });

        it('validates contact info structure', function () {
            $response = $this->post('/admin/services', [
                'name' => 'Test Service',
                'description' => 'Test description',
                'contact_info' => 'invalid-format', // Should be array
            ]);

            $response->assertSessionHasErrors(['contact_info']);
        });

        it('validates requirements array', function () {
            $response = $this->post('/admin/services', [
                'name' => 'Test Service',
                'description' => 'Test description',
                'requirements' => 'not-an-array',
            ]);

            $response->assertSessionHasErrors(['requirements']);
        });

        it('validates procedure array', function () {
            $response = $this->post('/admin/services', [
                'name' => 'Test Service',
                'description' => 'Test description',
                'procedure' => 'not-an-array',
            ]);

            $response->assertSessionHasErrors(['procedure']);
        });
    });

    describe('Search and Filter', function () {
        it('can search services by name', function () {
            Service::factory()->create(['name' => 'Surat Domisili']);
            Service::factory()->create(['name' => 'Surat Nikah']);

            $response = $this->get('/admin/services?search=domisili');

            $response->assertInertia(fn ($page) => $page->where('services.data', function ($services) {
                return count($services) === 1 &&
                       str_contains(strtolower($services[0]['name']), 'domisili');
            })
            );
        });

        it('can filter by active status', function () {
            Service::factory()->active()->create();
            Service::factory()->inactive()->create();

            $response = $this->get('/admin/services?status=active');

            $response->assertInertia(fn ($page) => $page->where('services.data', function ($services) {
                foreach ($services as $service) {
                    if (! $service['is_active']) {
                        return false;
                    }
                }

                return count($services) === 1;
            })
            );
        });
    });

    describe('Authorization', function () {
        it('requires authentication', function () {
            auth()->logout();

            $response = $this->post('/admin/services', [
                'name' => 'Test Service',
                'description' => 'Test description',
            ]);

            $response->assertRedirect('/login');
        });

        it('prevents unauthorized access', function () {
            $regularUser = User::factory()->create(['role' => 'user']);
            $this->actingAs($regularUser);

            $response = $this->get('/admin/services');

            $response->assertStatus(403);
        });
    });

    describe('Data Integrity', function () {
        it('maintains data consistency during updates', function () {
            $service = Service::factory()->create([
                'requirements' => ['Req 1', 'Req 2'],
                'procedure' => ['Step 1', 'Step 2'],
            ]);

            // Partial update should not lose existing data
            $response = $this->put("/admin/services/{$service->id}", [
                'name' => 'Updated Name',
                'description' => $service->description,
                'requirements' => $service->requirements,
                'procedure' => $service->procedure,
            ]);

            $response->assertRedirect();

            $updatedService = $service->fresh();
            expect($updatedService->requirements)->toBe(['Req 1', 'Req 2']);
            expect($updatedService->procedure)->toBe(['Step 1', 'Step 2']);
        });
    });
});
