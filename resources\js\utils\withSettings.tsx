import { useSettings } from '@/hooks/useSettings';
import React from 'react';

/**
 * Props interface for components that receive settings
 */
export interface WithSettingsProps {
    settings: ReturnType<typeof useSettings>;
}

/**
 * Higher-order component that provides settings to wrapped components
 * Useful for components that need settings but don't have direct access to Inertia props
 *
 * @param WrappedComponent - The component to wrap with settings
 * @returns Enhanced component with settings props
 */
export function withSettings<P extends object>(WrappedComponent: React.ComponentType<P & WithSettingsProps>) {
    const WithSettingsComponent = (props: P) => {
        const settings = useSettings();

        return <WrappedComponent {...props} settings={settings} />;
    };

    // Set display name for debugging
    WithSettingsComponent.displayName = `withSettings(${WrappedComponent.displayName || WrappedComponent.name})`;

    return WithSettingsComponent;
}

/**
 * React context for settings (alternative approach for deeply nested components)
 */
export const SettingsContext = React.createContext<ReturnType<typeof useSettings> | null>(null);

/**
 * Settings provider component
 * Use this to provide settings context to deeply nested components
 *
 * @param children - Child components
 * @returns Provider component
 */
export function SettingsProvider({ children }: { children: React.ReactNode }) {
    const settings = useSettings();

    return <SettingsContext.Provider value={settings}>{children}</SettingsContext.Provider>;
}

/**
 * Hook to use settings from context
 * Alternative to useSettings for deeply nested components
 *
 * @returns Settings object from context
 * @throws Error if used outside SettingsProvider
 */
export function useSettingsContext() {
    const context = React.useContext(SettingsContext);

    if (context === null) {
        throw new Error('useSettingsContext must be used within a SettingsProvider');
    }

    return context;
}
