import { cn } from '@/lib/utils';
import { useEffect, useRef, useState } from 'react';

interface OptimizedImageProps {
    src: string;
    alt: string;
    className?: string;
    sizes?: string;
    srcSet?: string;
    placeholder?: string;
    priority?: boolean;
    onLoad?: () => void;
    onError?: () => void;
}

export default function OptimizedImage({
    src,
    alt,
    className,
    sizes = '(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw',
    srcSet,
    placeholder,
    priority = false,
    onLoad,
    onError,
}: OptimizedImageProps) {
    const [isLoaded, setIsLoaded] = useState(false);
    const [isInView, setIsInView] = useState(priority);
    const [hasError, setHasError] = useState(false);
    const imgRef = useRef<HTMLImageElement>(null);
    const observerRef = useRef<IntersectionObserver | null>(null);

    // Lazy loading dengan Intersection Observer
    useEffect(() => {
        if (priority || isInView) return;

        observerRef.current = new IntersectionObserver(
            (entries) => {
                entries.forEach((entry) => {
                    if (entry.isIntersecting) {
                        setIsInView(true);
                        observerRef.current?.disconnect();
                    }
                });
            },
            {
                rootMargin: '50px',
            },
        );

        if (imgRef.current) {
            observerRef.current.observe(imgRef.current);
        }

        return () => {
            observerRef.current?.disconnect();
        };
    }, [priority, isInView]);

    const handleLoad = () => {
        setIsLoaded(true);
        onLoad?.();
    };

    const handleError = () => {
        setHasError(true);
        onError?.();
    };

    // Generate WebP srcSet dengan fallback
    const generateWebPSrcSet = (originalSrc: string, originalSrcSet?: string) => {
        if (originalSrcSet) {
            return originalSrcSet.replace(/\.(jpg|jpeg|png)/g, '.webp');
        }

        const webpSrc = originalSrc.replace(/\.(jpg|jpeg|png)$/i, '.webp');
        return webpSrc;
    };

    if (hasError) {
        return (
            <div className={cn('flex items-center justify-center bg-gray-100 text-gray-400', className)}>
                <svg className="h-8 w-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"
                    />
                </svg>
            </div>
        );
    }

    return (
        <div className={cn('relative overflow-hidden', className)}>
            {/* Placeholder blur saat loading */}
            {placeholder && !isLoaded && (
                <img
                    src={placeholder}
                    alt=""
                    className="absolute inset-0 h-full w-full scale-110 object-cover blur-sm transition-opacity duration-300"
                    aria-hidden="true"
                />
            )}

            {/* Loading skeleton */}
            {!isLoaded && !placeholder && <div className="absolute inset-0 animate-pulse bg-gray-200" />}

            {/* Main image */}
            {isInView && (
                <picture>
                    {/* WebP source untuk browser yang mendukung */}
                    <source srcSet={generateWebPSrcSet(src, srcSet)} sizes={sizes} type="image/webp" />

                    {/* Fallback untuk browser lama */}
                    <img
                        ref={imgRef}
                        src={src}
                        srcSet={srcSet}
                        sizes={sizes}
                        alt={alt}
                        className={cn('h-full w-full object-cover transition-opacity duration-300', isLoaded ? 'opacity-100' : 'opacity-0')}
                        onLoad={handleLoad}
                        onError={handleError}
                        loading={priority ? 'eager' : 'lazy'}
                    />
                </picture>
            )}
        </div>
    );
}
