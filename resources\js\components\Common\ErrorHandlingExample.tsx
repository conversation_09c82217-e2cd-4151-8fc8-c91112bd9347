import { Button } from '@/components/ui/button';
import { useError<PERSON>andler } from '@/hooks/useErrorHandler';
import { router } from '@inertiajs/react';
import { useState } from 'react';

/**
 * Example component showing how to use error handling
 * This is for demonstration purposes and can be removed in production
 */
export function ErrorHandlingExample() {
    const { handleError, showError } = useErrorHandler();
    const [loading, setLoading] = useState(false);

    const simulateApiError = async (errorType: string) => {
        setLoading(true);

        try {
            // Simulate different types of errors
            switch (errorType) {
                case '404':
                    throw { response: { status: 404, data: { message: 'Data tidak ditemukan' } } };
                case '500':
                    throw { response: { status: 500, data: { message: 'Kesalahan server' } } };
                case 'network':
                    throw { code: 'NETWORK_ERROR' };
                case 'validation':
                    throw {
                        response: {
                            status: 422,
                            data: {
                                message: 'Validasi gagal',
                                errors: {
                                    name: ['<PERSON><PERSON> wajib diisi'],
                                    email: ['Format email tidak valid'],
                                },
                            },
                        },
                    };
                default:
                    throw new Error('Kesalahan tidak terduga');
            }
        } catch (error) {
            const apiError = handleError(error);
            showError(apiError);
        } finally {
            setLoading(false);
        }
    };

    const navigateToErrorPage = (errorCode: string) => {
        router.visit(`/errors/${errorCode}`);
    };

    return (
        <div className="space-y-4 rounded-lg border p-6">
            <h3 className="text-lg font-semibold">Test Error Handling</h3>
            <p className="text-sm text-gray-600">Klik tombol di bawah untuk menguji berbagai jenis error handling:</p>

            <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                    <h4 className="font-medium">API Errors:</h4>
                    <Button onClick={() => simulateApiError('404')} variant="outline" size="sm" disabled={loading}>
                        Test 404 Error
                    </Button>
                    <Button onClick={() => simulateApiError('500')} variant="outline" size="sm" disabled={loading}>
                        Test 500 Error
                    </Button>
                    <Button onClick={() => simulateApiError('network')} variant="outline" size="sm" disabled={loading}>
                        Test Network Error
                    </Button>
                    <Button onClick={() => simulateApiError('validation')} variant="outline" size="sm" disabled={loading}>
                        Test Validation Error
                    </Button>
                </div>

                <div className="space-y-2">
                    <h4 className="font-medium">Error Pages:</h4>
                    <Button onClick={() => navigateToErrorPage('403')} variant="outline" size="sm">
                        View 403 Page
                    </Button>
                    <Button onClick={() => navigateToErrorPage('404')} variant="outline" size="sm">
                        View 404 Page
                    </Button>
                    <Button onClick={() => navigateToErrorPage('500')} variant="outline" size="sm">
                        View 500 Page
                    </Button>
                </div>
            </div>
        </div>
    );
}
