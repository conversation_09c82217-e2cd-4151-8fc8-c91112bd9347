<?php

use App\Models\News;
use App\Models\User;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;

describe('Admin News Management', function () {
    beforeEach(function () {
        $this->user = User::factory()->create(['role' => 'admin']);
        $this->actingAs($this->user);
        Storage::fake('public');
    });

    describe('News Creation', function () {
        it('can create a new news article', function () {
            $newsData = [
                'title' => 'Berita Baru Test',
                'content' => 'Konten berita baru untuk testing',
                'excerpt' => 'Excerpt berita test',
                'category' => 'pengumuman',
                'is_published' => true,
                'published_at' => now()->format('Y-m-d H:i:s'),
            ];

            $response = $this->post('/admin/news', $newsData);

            $response->assertRedirect();
            $this->assertDatabaseHas('news', [
                'title' => 'Berita Baru Test',
                'slug' => 'berita-baru-test',
                'category' => 'pengumuman',
            ]);
        });

        it('validates required fields', function () {
            $response = $this->post('/admin/news', []);

            $response->assertSessionHasErrors(['title', 'content', 'category']);
        });

        it('validates title uniqueness', function () {
            News::factory()->create(['title' => 'Judul Sama']);

            $response = $this->post('/admin/news', [
                'title' => 'Judul Sama',
                'content' => 'Konten berbeda',
                'category' => 'pengumuman',
            ]);

            $response->assertSessionHasErrors(['title']);
        });

        it('can upload featured image during creation', function () {
            $file = UploadedFile::fake()->image('featured.jpg', 800, 600);

            $newsData = [
                'title' => 'Berita Dengan Gambar',
                'content' => 'Konten berita dengan gambar',
                'category' => 'kegiatan',
                'featured_image' => $file,
            ];

            $response = $this->post('/admin/news', $newsData);

            $response->assertRedirect();

            $news = News::where('title', 'Berita Dengan Gambar')->first();
            expect($news->featured_image)->not()->toBeNull();
            expect($news->featured_image)->toBeArray();
        });

        it('validates image file type', function () {
            $file = UploadedFile::fake()->create('document.pdf', 1000);

            $response = $this->post('/admin/news', [
                'title' => 'Test News',
                'content' => 'Test content',
                'category' => 'pengumuman',
                'featured_image' => $file,
            ]);

            $response->assertSessionHasErrors(['featured_image']);
        });

        it('validates image file size', function () {
            $file = UploadedFile::fake()->image('large.jpg')->size(10000); // 10MB

            $response = $this->post('/admin/news', [
                'title' => 'Test News',
                'content' => 'Test content',
                'category' => 'pengumuman',
                'featured_image' => $file,
            ]);

            $response->assertSessionHasErrors(['featured_image']);
        });
    });

    describe('News Update', function () {
        it('can update existing news article', function () {
            $news = News::factory()->create([
                'title' => 'Judul Lama',
                'content' => 'Konten lama',
            ]);

            $updateData = [
                'title' => 'Judul Baru',
                'content' => 'Konten baru',
                'category' => 'pembangunan',
            ];

            $response = $this->put("/admin/news/{$news->id}", $updateData);

            $response->assertRedirect();
            $this->assertDatabaseHas('news', [
                'id' => $news->id,
                'title' => 'Judul Baru',
                'content' => 'Konten baru',
                'category' => 'pembangunan',
            ]);
        });

        it('can update featured image', function () {
            $news = News::factory()->withFeaturedImage()->create();
            $newFile = UploadedFile::fake()->image('new-featured.jpg');

            $response = $this->put("/admin/news/{$news->id}", [
                'title' => $news->title,
                'content' => $news->content,
                'category' => $news->category,
                'featured_image' => $newFile,
            ]);

            $response->assertRedirect();

            $updatedNews = $news->fresh();
            expect($updatedNews->featured_image)->not()->toBe($news->featured_image);
        });

        it('can remove featured image', function () {
            $news = News::factory()->withFeaturedImage()->create();

            $response = $this->put("/admin/news/{$news->id}", [
                'title' => $news->title,
                'content' => $news->content,
                'category' => $news->category,
                'remove_featured_image' => true,
            ]);

            $response->assertRedirect();

            $updatedNews = $news->fresh();
            expect($updatedNews->featured_image)->toBeNull();
        });

        it('validates update data', function () {
            $news = News::factory()->create();

            $response = $this->put("/admin/news/{$news->id}", [
                'title' => '', // Empty title
                'content' => '', // Empty content
            ]);

            $response->assertSessionHasErrors(['title', 'content']);
        });
    });

    describe('News Deletion', function () {
        it('can delete news article', function () {
            $news = News::factory()->create();

            $response = $this->delete("/admin/news/{$news->id}");

            $response->assertRedirect();
            $this->assertDatabaseMissing('news', ['id' => $news->id]);
        });

        it('deletes associated images when deleting news', function () {
            $news = News::factory()->withFeaturedImage()->create();

            $response = $this->delete("/admin/news/{$news->id}");

            $response->assertRedirect();
            // Image files should be cleaned up
        });
    });

    describe('Bulk Operations', function () {
        it('can publish multiple news articles', function () {
            $news1 = News::factory()->unpublished()->create();
            $news2 = News::factory()->unpublished()->create();

            $response = $this->post('/admin/news/bulk-publish', [
                'news_ids' => [$news1->id, $news2->id],
            ]);

            $response->assertRedirect();

            expect($news1->fresh()->is_published)->toBe(true);
            expect($news2->fresh()->is_published)->toBe(true);
        });

        it('can unpublish multiple news articles', function () {
            $news1 = News::factory()->published()->create();
            $news2 = News::factory()->published()->create();

            $response = $this->post('/admin/news/bulk-unpublish', [
                'news_ids' => [$news1->id, $news2->id],
            ]);

            $response->assertRedirect();

            expect($news1->fresh()->is_published)->toBe(false);
            expect($news2->fresh()->is_published)->toBe(false);
        });

        it('can delete multiple news articles', function () {
            $news1 = News::factory()->create();
            $news2 = News::factory()->create();

            $response = $this->post('/admin/news/bulk-delete', [
                'news_ids' => [$news1->id, $news2->id],
            ]);

            $response->assertRedirect();

            $this->assertDatabaseMissing('news', ['id' => $news1->id]);
            $this->assertDatabaseMissing('news', ['id' => $news2->id]);
        });
    });

    describe('Form Validation', function () {
        it('validates published_at format', function () {
            $response = $this->post('/admin/news', [
                'title' => 'Test News',
                'content' => 'Test content',
                'published_at' => 'invalid-date',
            ]);

            $response->assertSessionHasErrors(['published_at']);
        });

        it('validates meta fields length', function () {
            $response = $this->post('/admin/news', [
                'title' => 'Test News',
                'content' => 'Test content',
                'category' => 'pengumuman',
                'meta_title' => str_repeat('a', 256), // Too long
                'meta_description' => str_repeat('b', 256), // Too long
            ]);

            $response->assertSessionHasErrors(['meta_title', 'meta_description']);
        });
    });

    describe('Authorization', function () {
        it('requires authentication', function () {
            auth()->logout();

            $response = $this->post('/admin/news', [
                'title' => 'Test News',
                'content' => 'Test content',
            ]);

            $response->assertRedirect('/login');
        });

        it('prevents unauthorized access to admin routes', function () {
            // Test with regular user without admin privileges
            $regularUser = User::factory()->create(['role' => 'user']);
            $this->actingAs($regularUser);

            $response = $this->get('/admin/news');

            $response->assertStatus(403);
        });
    });

    describe('Rich Text Editor', function () {
        it('handles rich text content with HTML', function () {
            $htmlContent = '<p>Paragraph dengan <strong>bold text</strong> dan <em>italic text</em>.</p><ul><li>List item 1</li><li>List item 2</li></ul>';

            $response = $this->post('/admin/news', [
                'title' => 'Rich Text News',
                'content' => $htmlContent,
                'category' => 'pengumuman',
            ]);

            $response->assertRedirect();

            $news = News::where('title', 'Rich Text News')->first();
            expect($news->content)->toBe($htmlContent);
        });

        it('sanitizes dangerous HTML content', function () {
            $dangerousContent = '<script>alert("xss")</script><p>Safe content</p>';

            $response = $this->post('/admin/news', [
                'title' => 'Sanitized News',
                'content' => $dangerousContent,
                'category' => 'pengumuman',
            ]);

            $response->assertRedirect();

            $news = News::where('title', 'Sanitized News')->first();
            expect($news->content)->not()->toContain('<script>');
        });
    });
});
