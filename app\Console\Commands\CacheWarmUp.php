<?php

namespace App\Console\Commands;

use App\Services\PerformanceService;
use Illuminate\Console\Command;

class CacheWarmUp extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'cache:warm-up';

    /**
     * The console command description.
     */
    protected $description = 'Warm up application cache with common queries';

    /**
     * Execute the console command.
     */
    public function handle(PerformanceService $performanceService)
    {
        $this->info('Warming up application cache...');

        $performanceService->warmUpCache();

        $this->info('Cache warm-up completed successfully!');

        // Show cache statistics
        $stats = $performanceService->getCacheStats();
        $this->table(
            ['Metric', 'Value'],
            [
                ['Cache Driver', $stats['cache_driver']],
                ['Cached Items', $stats['cached_items'] ?? 0],
                ['Total Keys', $stats['total_keys'] ?? 0],
                ['Hit Ratio', ($stats['cache_hit_ratio'] ?? 0).'%'],
            ]
        );
    }
}
