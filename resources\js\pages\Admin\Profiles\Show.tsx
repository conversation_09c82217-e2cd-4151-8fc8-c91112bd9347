import { Badge } from '@/components/ui/badge';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import AdminLayout from '@/layouts/AdminLayout';
import { type BreadcrumbItem } from '@/types';
import { Head, Link, useForm } from '@inertiajs/react';
import { ArrowLeft, Edit, FileText, Trash2 } from 'lucide-react';

interface Profile {
    id: number;
    section: string;
    title: string;
    content: string;
    image: string | null;
    order: number;
    created_at: string;
    updated_at: string;
}

interface Props {
    profile: Profile;
}

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'Dashboard',
        href: '/dashboard',
    },
    {
        title: 'Manajemen Profil Desa',
        href: '/admin/profiles',
    },
    {
        title: 'Detail Konten',
        href: '#',
    },
];

export default function ProfilesShow({ profile }: Props) {
    const { delete: destroy } = useForm();

    const handleDelete = () => {
        if (confirm(`<PERSON><PERSON><PERSON><PERSON><PERSON> yakin ingin menghapus konten "${profile.title}"?`)) {
            destroy(route('admin.profiles.destroy', profile.id));
        }
    };

    const getSectionTitle = (section: string) => {
        const sections: Record<string, string> = {
            history: 'Sejarah Desa',
            vision_mission: 'Visi & Misi',
            organization: 'Struktur Organisasi',
            demographics: 'Data Demografis',
            geography: 'Data Geografis',
        };
        return sections[section] || section;
    };

    const getSectionBadgeColor = (section: string) => {
        const colors: Record<string, string> = {
            history: 'bg-blue-100 text-blue-800',
            vision_mission: 'bg-green-100 text-green-800',
            organization: 'bg-purple-100 text-purple-800',
            demographics: 'bg-orange-100 text-orange-800',
            geography: 'bg-teal-100 text-teal-800',
        };
        return colors[section] || 'bg-gray-100 text-gray-800';
    };

    const formatContent = (content: string) => {
        return content.split('\n').map((paragraph, index) => (
            <p key={index} className="mb-4 last:mb-0">
                {paragraph}
            </p>
        ));
    };

    return (
        <AdminLayout breadcrumbs={breadcrumbs}>
            <Head title={`${profile.title} - Admin Desa Lemah Duhur`} />

            <div className="flex h-full flex-1 flex-col gap-6 p-6">
                {/* Header */}
                <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
                    <div>
                        <h1 className="text-3xl font-bold text-gray-900 dark:text-gray-100">{profile.title}</h1>
                        <p className="text-gray-600 dark:text-gray-300">Detail konten profil desa</p>
                    </div>
                    <div className="flex items-center space-x-3">
                        <Link href={route('admin.profiles.index')}>
                            <Button variant="outline" className="flex items-center space-x-2">
                                <ArrowLeft className="h-4 w-4" />
                                <span>Kembali</span>
                            </Button>
                        </Link>
                        <Link href={route('admin.profiles.edit', profile.id)}>
                            <Button className="flex items-center space-x-2">
                                <Edit className="h-4 w-4" />
                                <span>Edit</span>
                            </Button>
                        </Link>
                        <Button variant="destructive" onClick={handleDelete} className="flex items-center space-x-2">
                            <Trash2 className="h-4 w-4" />
                            <span>Hapus</span>
                        </Button>
                    </div>
                </div>

                {/* Profile Details */}
                <div className="grid grid-cols-1 gap-6 lg:grid-cols-3">
                    {/* Main Content */}
                    <div className="lg:col-span-2">
                        <Card>
                            <CardHeader>
                                <CardTitle className="flex items-center space-x-2">
                                    <FileText className="h-5 w-5" />
                                    <span>Konten</span>
                                </CardTitle>
                            </CardHeader>
                            <CardContent>
                                <div className="prose max-w-none">{formatContent(profile.content)}</div>
                            </CardContent>
                        </Card>

                        {/* Image */}
                        {profile.image && (
                            <Card className="mt-6">
                                <CardHeader>
                                    <CardTitle>Gambar</CardTitle>
                                </CardHeader>
                                <CardContent>
                                    <img src={`/storage/${profile.image}`} alt={profile.title} className="w-full rounded-lg object-cover" />
                                </CardContent>
                            </Card>
                        )}
                    </div>

                    {/* Sidebar */}
                    <div className="space-y-6">
                        {/* Meta Information */}
                        <Card>
                            <CardHeader>
                                <CardTitle>Informasi</CardTitle>
                            </CardHeader>
                            <CardContent className="space-y-4">
                                <div>
                                    <label className="text-sm font-medium text-gray-500 dark:text-gray-400">Bagian</label>
                                    <div className="mt-1">
                                        <Badge className={getSectionBadgeColor(profile.section)}>{getSectionTitle(profile.section)}</Badge>
                                    </div>
                                </div>

                                <div>
                                    <label className="text-sm font-medium text-gray-500 dark:text-gray-400">Urutan</label>
                                    <div className="mt-1">
                                        <Badge variant="outline">{profile.order}</Badge>
                                    </div>
                                </div>

                                <div>
                                    <label className="text-sm font-medium text-gray-500 dark:text-gray-400">Tanggal Dibuat</label>
                                    <div className="mt-1 text-sm">
                                        {new Date(profile.created_at).toLocaleDateString('id-ID', {
                                            weekday: 'long',
                                            year: 'numeric',
                                            month: 'long',
                                            day: 'numeric',
                                        })}
                                    </div>
                                </div>

                                <div>
                                    <label className="text-sm font-medium text-gray-500 dark:text-gray-400">Terakhir Diperbarui</label>
                                    <div className="mt-1 text-sm">
                                        {new Date(profile.updated_at).toLocaleDateString('id-ID', {
                                            weekday: 'long',
                                            year: 'numeric',
                                            month: 'long',
                                            day: 'numeric',
                                        })}
                                    </div>
                                </div>

                                <div>
                                    <label className="text-sm font-medium text-gray-500 dark:text-gray-400">Status Gambar</label>
                                    <div className="mt-1 text-sm">
                                        {profile.image ? (
                                            <span className="text-green-600">Ada gambar</span>
                                        ) : (
                                            <span className="text-gray-500 dark:text-gray-400">Tidak ada gambar</span>
                                        )}
                                    </div>
                                </div>
                            </CardContent>
                        </Card>

                        {/* Quick Actions */}
                        <Card>
                            <CardHeader>
                                <CardTitle>Aksi Cepat</CardTitle>
                            </CardHeader>
                            <CardContent className="space-y-3">
                                <Link href={route('admin.profiles.edit', profile.id)} className="block">
                                    <Button variant="outline" className="w-full justify-start">
                                        <Edit className="mr-2 h-4 w-4" />
                                        Edit Konten
                                    </Button>
                                </Link>

                                <Link href={route('admin.profiles.section', profile.section)} className="block">
                                    <Button variant="outline" className="w-full justify-start">
                                        <FileText className="mr-2 h-4 w-4" />
                                        Kelola {getSectionTitle(profile.section)}
                                    </Button>
                                </Link>

                                <Button variant="destructive" onClick={handleDelete} className="w-full justify-start">
                                    <Trash2 className="mr-2 h-4 w-4" />
                                    Hapus Konten
                                </Button>
                            </CardContent>
                        </Card>
                    </div>
                </div>
            </div>
        </AdminLayout>
    );
}
