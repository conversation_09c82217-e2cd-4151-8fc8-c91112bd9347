import { cn } from '@/lib/utils';
import { Link } from '@inertiajs/react';

interface NavigationItem {
    name: string;
    href: string;
    current?: boolean;
}

interface NavigationProps {
    items: NavigationItem[];
    className?: string;
    orientation?: 'horizontal' | 'vertical';
    onItemClick?: () => void;
}

export function Navigation({ items, className, orientation = 'horizontal', onItemClick }: NavigationProps) {
    return (
        <nav className={cn(orientation === 'horizontal' ? 'flex items-center space-x-4 lg:space-x-8' : 'flex flex-col space-y-1', className)}>
            {items.map((item) => (
                <Link
                    key={item.name}
                    href={item.href}
                    className={cn(
                        'touch-manipulation font-medium transition-colors',
                        orientation === 'horizontal'
                            ? 'rounded-md px-2 py-2 text-sm text-muted-foreground hover:bg-accent/50 hover:text-foreground lg:text-base'
                            : 'block flex min-h-[44px] items-center rounded-md px-4 py-4 text-base text-muted-foreground hover:bg-accent hover:text-foreground',
                        item.current && 'bg-accent/30 text-foreground',
                    )}
                    onClick={onItemClick}
                >
                    {item.name}
                </Link>
            ))}
        </nav>
    );
}
