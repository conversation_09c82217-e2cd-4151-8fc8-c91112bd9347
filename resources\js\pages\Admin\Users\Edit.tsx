import { Button } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import AdminLayout from '@/layouts/AdminLayout';
import { type BreadcrumbItem } from '@/types';
import { Head, Link, useForm } from '@inertiajs/react';
import { AlertCircle, ArrowLeft, Edit, Eye, EyeOff, Save } from 'lucide-react';
import { FormEventHandler, useState } from 'react';

interface User {
    id: number;
    name: string;
    email: string;
    role: string;
}

interface Props {
    user: User;
    roles: Record<string, string>;
    canEditRole?: boolean;
}

export default function UserEdit({ user, roles, canEditRole = true }: Props) {
    const [showPassword, setShowPassword] = useState(false);
    const [showPasswordConfirmation, setShowPasswordConfirmation] = useState(false);

    const breadcrumbs: BreadcrumbItem[] = [
        {
            title: 'Dashboard',
            href: '/dashboard',
        },
        {
            title: 'Manajemen Pengguna',
            href: '/admin/users',
        },
        {
            title: 'Edit Pengguna',
            href: `/admin/users/${user.id}/edit`,
        },
    ];

    const { data, setData, put, processing, errors } = useForm({
        name: user.name || '',
        email: user.email || '',
        password: '',
        password_confirmation: '',
        role: user.role || '',
    });

    const submit: FormEventHandler = (e) => {
        e.preventDefault();
        put(route('admin.users.update', user.id));
    };

    const togglePasswordVisibility = () => {
        setShowPassword(!showPassword);
    };

    const togglePasswordConfirmationVisibility = () => {
        setShowPasswordConfirmation(!showPasswordConfirmation);
    };

    return (
        <AdminLayout breadcrumbs={breadcrumbs}>
            <Head title={`Edit Pengguna: ${user.name} - Admin Desa Lemah Duhur`} />

            <div className="flex h-full flex-1 flex-col gap-6 p-6">
                {/* Header */}
                <div className="flex items-center justify-between">
                    <div>
                        <h1 className="text-3xl font-bold text-gray-900 dark:text-gray-100">Edit Pengguna</h1>
                        <p className="text-gray-600 dark:text-gray-300">Perbarui informasi akun pengguna {user.name}</p>
                    </div>
                    <Link href={route('admin.users.index')}>
                        <Button variant="outline" className="flex items-center space-x-2">
                            <ArrowLeft className="h-4 w-4" />
                            <span>Kembali</span>
                        </Button>
                    </Link>
                </div>

                <form onSubmit={submit} className="space-y-6">
                    <div className="grid grid-cols-1 gap-6 lg:grid-cols-3">
                        {/* Main Content */}
                        <div className="space-y-6 lg:col-span-2">
                            {/* Basic Information */}
                            <Card>
                                <CardHeader>
                                    <CardTitle className="flex items-center space-x-2">
                                        <Edit className="h-5 w-5" />
                                        <span>Informasi Akun</span>
                                    </CardTitle>
                                </CardHeader>
                                <CardContent className="space-y-4">
                                    <div className="space-y-2">
                                        <Label htmlFor="name">Nama Lengkap *</Label>
                                        <Input
                                            id="name"
                                            value={data.name}
                                            onChange={(e) => setData('name', e.target.value)}
                                            placeholder="Masukkan nama lengkap..."
                                            className={errors.name ? 'border-red-500' : ''}
                                            required
                                        />
                                        {errors.name && <p className="text-sm text-red-600">{errors.name}</p>}
                                    </div>

                                    <div className="space-y-2">
                                        <Label htmlFor="email">Alamat Email *</Label>
                                        <Input
                                            id="email"
                                            type="email"
                                            value={data.email}
                                            onChange={(e) => setData('email', e.target.value)}
                                            placeholder="<EMAIL>"
                                            className={errors.email ? 'border-red-500' : ''}
                                            required
                                        />
                                        {errors.email && <p className="text-sm text-red-600">{errors.email}</p>}
                                        <p className="text-xs text-gray-500 dark:text-gray-400">Email akan digunakan untuk login ke sistem</p>
                                    </div>
                                </CardContent>
                            </Card>

                            {/* Password Change Section */}
                            <Card>
                                <CardHeader>
                                    <CardTitle className="flex items-center space-x-2">
                                        <AlertCircle className="h-5 w-5" />
                                        <span>Ubah Kata Sandi</span>
                                    </CardTitle>
                                </CardHeader>
                                <CardContent className="space-y-4">
                                    <div className="rounded-md bg-blue-50 p-4 dark:bg-blue-900/20">
                                        <p className="text-sm text-blue-800 dark:text-blue-200">
                                            <strong>Opsional:</strong> Kosongkan field kata sandi jika tidak ingin mengubah kata sandi pengguna.
                                        </p>
                                    </div>

                                    <div className="space-y-2">
                                        <Label htmlFor="password">Kata Sandi Baru</Label>
                                        <div className="relative">
                                            <Input
                                                id="password"
                                                type={showPassword ? 'text' : 'password'}
                                                value={data.password}
                                                onChange={(e) => setData('password', e.target.value)}
                                                placeholder="Masukkan kata sandi baru..."
                                                className={errors.password ? 'border-red-500 pr-10' : 'pr-10'}
                                            />
                                            <Button
                                                type="button"
                                                variant="ghost"
                                                size="sm"
                                                className="absolute top-0 right-0 h-full px-3 py-2 hover:bg-transparent"
                                                onClick={togglePasswordVisibility}
                                            >
                                                {showPassword ? (
                                                    <EyeOff className="h-4 w-4 text-gray-400" />
                                                ) : (
                                                    <Eye className="h-4 w-4 text-gray-400" />
                                                )}
                                            </Button>
                                        </div>
                                        {errors.password && <p className="text-sm text-red-600">{errors.password}</p>}
                                        <p className="text-xs text-gray-500 dark:text-gray-400">
                                            Minimal 8 karakter, disarankan menggunakan kombinasi huruf, angka, dan simbol
                                        </p>
                                    </div>

                                    <div className="space-y-2">
                                        <Label htmlFor="password_confirmation">Konfirmasi Kata Sandi Baru</Label>
                                        <div className="relative">
                                            <Input
                                                id="password_confirmation"
                                                type={showPasswordConfirmation ? 'text' : 'password'}
                                                value={data.password_confirmation}
                                                onChange={(e) => setData('password_confirmation', e.target.value)}
                                                placeholder="Ulangi kata sandi baru..."
                                                className={errors.password_confirmation ? 'border-red-500 pr-10' : 'pr-10'}
                                            />
                                            <Button
                                                type="button"
                                                variant="ghost"
                                                size="sm"
                                                className="absolute top-0 right-0 h-full px-3 py-2 hover:bg-transparent"
                                                onClick={togglePasswordConfirmationVisibility}
                                            >
                                                {showPasswordConfirmation ? (
                                                    <EyeOff className="h-4 w-4 text-gray-400" />
                                                ) : (
                                                    <Eye className="h-4 w-4 text-gray-400" />
                                                )}
                                            </Button>
                                        </div>
                                        {errors.password_confirmation && <p className="text-sm text-red-600">{errors.password_confirmation}</p>}
                                        <p className="text-xs text-gray-500 dark:text-gray-400">
                                            Pastikan konfirmasi kata sandi sama dengan kata sandi baru di atas
                                        </p>
                                    </div>
                                </CardContent>
                            </Card>
                        </div>

                        {/* Sidebar */}
                        <div className="space-y-6">
                            {/* Role Selection */}
                            <Card>
                                <CardHeader>
                                    <CardTitle>Peran Pengguna</CardTitle>
                                </CardHeader>
                                <CardContent className="space-y-4">
                                    <div className="space-y-2">
                                        <Label>Pilih Peran *</Label>
                                        {canEditRole ? (
                                            <Select value={data.role} onValueChange={(value) => setData('role', value)}>
                                                <SelectTrigger className={errors.role ? 'border-red-500' : ''}>
                                                    <SelectValue placeholder="Pilih peran pengguna..." />
                                                </SelectTrigger>
                                                <SelectContent>
                                                    {Object.entries(roles).map(([key, label]) => (
                                                        <SelectItem key={key} value={key}>
                                                            {label}
                                                        </SelectItem>
                                                    ))}
                                                </SelectContent>
                                            </Select>
                                        ) : (
                                            <div className="rounded-md border border-gray-200 bg-gray-50 px-3 py-2 text-sm text-gray-700 dark:border-gray-700 dark:bg-gray-800 dark:text-gray-300">
                                                {roles[data.role] || data.role}
                                            </div>
                                        )}
                                        {errors.role && <p className="text-sm text-red-600">{errors.role}</p>}
                                        {!canEditRole && (
                                            <p className="text-xs text-amber-600 dark:text-amber-400">
                                                Anda tidak memiliki izin untuk mengubah peran pengguna ini.
                                            </p>
                                        )}
                                        <div className="space-y-2 text-xs text-gray-500 dark:text-gray-400">
                                            <p>
                                                <strong>User:</strong> Akses terbatas untuk pengguna umum
                                            </p>
                                            <p>
                                                <strong>Admin:</strong> Akses ke sistem administrasi
                                            </p>
                                            {Object.keys(roles).includes('superadmin') && (
                                                <p>
                                                    <strong>Super Admin:</strong> Akses penuh termasuk mengelola admin lain
                                                </p>
                                            )}
                                        </div>
                                    </div>
                                </CardContent>
                            </Card>

                            {/* Account Information */}
                            <Card>
                                <CardHeader>
                                    <CardTitle>Informasi Akun</CardTitle>
                                </CardHeader>
                                <CardContent className="space-y-3 text-sm text-gray-600 dark:text-gray-300">
                                    <div>
                                        <p className="font-medium">ID Pengguna:</p>
                                        <p>{user.id}</p>
                                    </div>
                                    <div>
                                        <p className="font-medium">Status Akun:</p>
                                        <p>Akun aktif</p>
                                    </div>
                                    <div>
                                        <p className="font-medium">Keamanan:</p>
                                        <p>Kata sandi dienkripsi secara otomatis</p>
                                    </div>
                                    <div>
                                        <p className="font-medium">Perubahan Password:</p>
                                        <p>Kosongkan jika tidak ingin mengubah</p>
                                    </div>
                                </CardContent>
                            </Card>

                            {/* Submit Button */}
                            <Button type="submit" disabled={processing} className="w-full">
                                <Save className="mr-2 h-4 w-4" />
                                {processing ? 'Menyimpan Perubahan...' : 'Simpan Perubahan'}
                            </Button>
                        </div>
                    </div>
                </form>
            </div>
        </AdminLayout>
    );
}
