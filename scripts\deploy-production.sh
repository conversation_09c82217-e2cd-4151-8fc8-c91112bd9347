#!/bin/bash

# Production Deployment Script for Website Profil Desa Lemah Duhur
# Optimized for Hostinger shared hosting

set -e  # Exit on any error

echo "🚀 Starting production deployment for Website Profil Desa Lemah Duhur..."
echo "📅 Deployment started at: $(date)"
echo ""

# Color codes for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if we're in the right directory
if [ ! -f "artisan" ]; then
    print_error "artisan file not found. Please run this script from the Laravel project root."
    exit 1
fi

# Step 1: Environment Setup
print_status "Setting up production environment..."

# Check if .env exists, if not copy from .env.production
if [ ! -f ".env" ]; then
    if [ -f ".env.production" ]; then
        cp .env.production .env
        print_success "Copied .env.production to .env"
        print_warning "Please update .env with your production values before continuing!"
        echo "Press Enter to continue after updating .env..."
        read
    else
        print_error ".env file not found and .env.production template not available"
        exit 1
    fi
fi

# Generate app key if not set
if ! grep -q "APP_KEY=base64:" .env; then
    print_status "Generating application key..."
    php artisan key:generate --force
    print_success "Application key generated"
fi

# Step 2: Dependencies Installation
print_status "Installing production dependencies..."

# Install PHP dependencies (production only)
if command -v composer &> /dev/null; then
    composer2 install --no-dev --optimize-autoloader --no-interaction
    print_success "PHP dependencies installed"
else
    print_error "Composer not found. Please install composer first."
    exit 1
fi

# Install Node.js dependencies
if command -v npm &> /dev/null; then
    npm ci --include=dev
    print_success "Node.js dependencies installed"
else
    print_error "npm not found. Please install Node.js first."
    exit 1
fi

# Step 3: Database Setup
print_status "Setting up production database..."

# Create database file if it doesn't exist (SQLite)
if [ ! -f "database/database.sqlite" ]; then
    touch database/database.sqlite
    print_success "SQLite database file created"
fi

# Run migrations
php artisan migrate --force
print_success "Database migrations completed"

# Run seeders for production data
php artisan db:seed --force --class=ProductionSeeder
print_success "Production data seeded"

# Step 4: Clear all caches
print_status "Clearing existing caches..."
php artisan cache:clear
php artisan config:clear
php artisan route:clear
php artisan view:clear
print_success "All caches cleared"

# Step 5: Build and optimize assets
print_status "Building and optimizing frontend assets..."

# Clean previous builds
rm -rf public/build

# Build production assets with production config
npm run build
print_success "Frontend assets built"

# Verify build assets exist
if [ -d "public/build" ]; then
    print_success "Build assets created successfully"
    ls -la public/build/
else
    print_error "Build assets not found in public/build/"
    exit 1
fi

# Step 6: Laravel optimizations
print_status "Optimizing Laravel for production..."

# Cache configuration
php artisan config:cache
print_success "Configuration cached"

# Cache routes
php artisan route:cache
print_success "Routes cached"

# Cache views
php artisan view:cache
print_success "Views cached"

# Optimize autoloader
composer dump-autoload --optimize --no-dev
print_success "Autoloader optimized"

# Step 7: Database optimizations
print_status "Optimizing database..."
php artisan db:analyze
print_success "Database analyzed and optimized"

# Step 8: Performance optimizations
print_status "Running performance optimizations..."

# Warm up application cache
php artisan cache:warm-up
print_success "Application cache warmed up"

# Step 9: Security and permissions
print_status "Setting up security and file permissions..."

# Set proper permissions for shared hosting
find storage -type f -exec chmod 644 {} \;
find storage -type d -exec chmod 755 {} \;
find bootstrap/cache -type f -exec chmod 644 {} \;
find bootstrap/cache -type d -exec chmod 755 {} \;

# Secure sensitive files
chmod 600 .env
if [ -f "database/database.sqlite" ]; then
    chmod 644 database/database.sqlite
fi

print_success "File permissions set"

# Step 10: Final optimizations and cleanup
print_status "Running final optimizations..."

# Remove development files
rm -rf node_modules/.cache
rm -rf storage/logs/*.log

# Create necessary directories
mkdir -p storage/app/public/images
mkdir -p storage/app/public/uploads
mkdir -p storage/framework/cache/data
mkdir -p storage/framework/sessions
mkdir -p storage/framework/views

print_success "Final cleanup completed"

# Step 11: Health checks
print_status "Running deployment health checks..."

# Check if Laravel can boot
if php artisan about > /dev/null 2>&1; then
    print_success "Laravel application boots successfully"
else
    print_error "Laravel application failed to boot"
    exit 1
fi

# Check database connection
if php artisan migrate:status > /dev/null 2>&1; then
    print_success "Database connection working"
else
    print_error "Database connection failed"
    exit 1
fi

# Check if assets exist
if [ -d "public/build" ]; then
    print_success "Built assets found"
else
    print_warning "Built assets not found - frontend may not work properly"
fi

# Step 12: Generate sitemap and final SEO setup
print_status "Generating SEO assets..."
php artisan sitemap:generate
print_success "XML sitemap generated"

# Step 13: Performance summary
print_status "Generating performance summary..."

echo ""
echo "🎉 Production deployment completed successfully!"
echo "📊 Deployment Summary:"
echo "=================================="

# Show Laravel info
php artisan about --only=environment,cache,database

echo ""
echo "📈 Performance Optimizations Applied:"
echo "• Configuration cached for faster bootstrap"
echo "• Routes cached for faster routing"
echo "• Views compiled and cached"
echo "• Autoloader optimized"
echo "• Database analyzed and optimized"
echo "• Application cache warmed up"
echo "• Assets minified and compressed"
echo "• Images optimized with WebP support"
echo "• Gzip compression enabled"
echo ""

echo "🔒 Security Measures Applied:"
echo "• Debug mode disabled"
echo "• Secure session cookies enabled"
echo "• CSRF protection active"
echo "• File permissions secured"
echo "• Sensitive files protected"
echo ""

echo "📋 Post-Deployment Checklist:"
echo "1. ✅ Update .env with production values"
echo "2. ✅ Database migrations completed"
echo "3. ✅ Production data seeded"
echo "4. ✅ Assets built and optimized"
echo "5. ✅ Caches warmed up"
echo "6. ✅ Security configured"
echo "7. ✅ Health checks passed"
echo ""

echo "🌐 Your Website Profil Desa Lemah Duhur is now ready for production!"
echo "🔗 Access your website at: $(grep APP_URL .env | cut -d '=' -f2)"
echo ""

print_warning "Important: Make sure to:"
print_warning "1. Set up SSL certificate for HTTPS"
print_warning "2. Configure your web server (Apache/Nginx)"
print_warning "3. Set up regular backups"
print_warning "4. Monitor application logs"
print_warning "5. Test all functionality thoroughly"

echo ""
echo "📅 Deployment completed at: $(date)"
echo "🎯 Happy launching! 🚀"