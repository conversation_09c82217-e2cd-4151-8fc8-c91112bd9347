import { Navigation } from '@/components/layout/Navigation';
import { Button } from '@/components/ui/button';
import { useFeatureFlags } from '@/hooks/useFeatureFlags';
import { useSettings } from '@/hooks/useSettings';
import { Link } from '@inertiajs/react';
import { Menu, X } from 'lucide-react';
import { useMemo, useState } from 'react';

const baseNavigationItems = [
    { name: '<PERSON><PERSON><PERSON>', href: '/', flagKey: null },
    { name: 'Profil Desa', href: '/profil', flagKey: null },
    { name: 'Layanan', href: '/layanan', flagKey: 'pages.layanan' },
    { name: 'Berita', href: '/berita', flagKey: 'pages.berita' },
    { name: 'Potensi <PERSON>', href: '/potensi', flagKey: 'pages.potensi' },
    { name: 'Pengaduan', href: '/pengaduan', flagKey: 'pages.pengaduan' },
];

export function Header() {
    const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
    const { isEnabled } = useFeatureFlags();

    // Filter navigation items based on feature flags
    const navigationItems = useMemo(() => {
        return baseNavigationItems
            .filter((item) => {
                // If no flag key, always show the item
                if (!item.flagKey) return true;

                // Check if the feature flag is enabled
                return isEnabled(item.flagKey);
            })
            .map((item) => ({
                name: item.name,
                href: item.href,
            }));
    }, [isEnabled]);

    const toggleMobileMenu = () => {
        setIsMobileMenuOpen(!isMobileMenuOpen);
    };

    return (
        <header className="sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
            <div className="container mx-auto px-3 sm:px-4 lg:px-8">
                <div className="flex h-14 items-center justify-between sm:h-16">
                    {/* Logo */}
                    <div className="flex min-w-0 flex-1 items-center sm:flex-none">
                        <Link href="/" className="flex min-w-0 items-center space-x-2">
                            <div className="flex h-8 w-8 flex-shrink-0 items-center justify-center sm:h-9 sm:w-9">
                                <img src="/images/logo-kabupaten-bogor.png" alt="logo kabupaten bogor" />
                            </div>
                            <div className="hidden min-w-0 sm:block">
                                <h1 className="truncate text-base font-semibold text-foreground lg:text-lg">
                                    {useSettings().getVillageProfile()?.name || 'Desa Lemah Duhur'}
                                </h1>
                                <p className="text-xs text-muted-foreground">Caringin - Bogor</p>
                            </div>
                            <div className="min-w-0 sm:hidden">
                                <h1 className="truncate text-sm font-semibold text-foreground">
                                    {useSettings().getVillageProfile()?.name?.replace('Desa ', '') || 'Lemah Duhur'}
                                </h1>
                            </div>
                        </Link>
                    </div>

                    {/* Desktop Navigation */}
                    <div className="hidden lg:block">
                        <Navigation items={navigationItems} orientation="horizontal" />
                    </div>

                    {/* Mobile menu button */}
                    <div className="flex-shrink-0 lg:hidden">
                        <Button variant="ghost" size="icon" onClick={toggleMobileMenu} aria-label="Toggle menu" className="h-10 w-10 sm:h-11 sm:w-11">
                            {isMobileMenuOpen ? <X className="h-5 w-5" /> : <Menu className="h-5 w-5" />}
                        </Button>
                    </div>
                </div>

                {/* Mobile Navigation */}
                {isMobileMenuOpen && (
                    <div className="border-t lg:hidden">
                        <div className="py-3 sm:py-4">
                            <Navigation items={navigationItems} orientation="vertical" onItemClick={() => setIsMobileMenuOpen(false)} />
                        </div>
                    </div>
                )}
            </div>
        </header>
    );
}
