# Settings Utilities Documentation

This document explains how to use the settings utilities for accessing village settings in React components.

## Overview

The settings system provides multiple ways to access village configuration data throughout the frontend application:

1. **useSettings Hook** - Primary method for accessing settings in React components
2. **Utility Functions** - Direct access functions for specific use cases
3. **HOC Pattern** - Higher-order component for injecting settings
4. **Context Pattern** - For deeply nested components

## Quick Start

### Basic Usage with Hook

```tsx
import { useSettings } from '@/hooks/useSettings';

function MyComponent() {
    const { getContactInfo, getVillageProfile, isAvailable } = useSettings();

    if (!isAvailable) {
        return <div>Settings tidak tersedia</div>;
    }

    const contactInfo = getContactInfo();
    const villageProfile = getVillageProfile();

    return (
        <div>
            <h1>{villageProfile.name}</h1>
            <p>Telepon: {contactInfo.phone}</p>
        </div>
    );
}
```

## Available Settings

The system provides access to the following settings:

- `village.contact_info` - Phone, email, address, WhatsApp, postal code, maps link
- `village.profile` - Village name, district, regency, province, established year, area, population
- `village.operating_hours` - Weekdays, Saturday, Sunday, break time, holidays
- `village.emergency_contacts` - Village head, secretary, security, health center contacts
- `village.history` - Etymology, legend, administrative history, cultural significance
- `village.service_settings` - Online services, mobile optimization, emergency contacts, etc.

## API Reference

### useSettings Hook

The primary hook for accessing settings in React components.

```tsx
const {
    // Raw settings data
    settings,

    // Generic access methods
    getSetting,
    getSettingProperty,
    hasSetting,

    // Convenience methods
    getContactInfo,
    getVillageProfile,
    getOperatingHours,
    getEmergencyContacts,
    getVillageHistory,
    getServiceSettings,

    // Utility properties
    isAvailable,
    isEmpty,
} = useSettings();
```

#### Methods

**getSetting(key, defaultValue?)**

```tsx
const contactInfo = getSetting('village.contact_info', {
    phone: '',
    email: '',
    address: '',
    whatsapp: '',
    postal_code: '',
    maps_link: '',
});
```

**getSettingProperty(key, path, defaultValue?)**

```tsx
const villageName = getSettingProperty('village.profile', 'name', 'Desa Lemah Duhur');
const villagePhone = getSettingProperty('village.contact_info', 'phone', 'Tidak tersedia');
```

**hasSetting(key)**

```tsx
const hasContactInfo = hasSetting('village.contact_info');
```

#### Convenience Methods

**getContactInfo()**
Returns contact information with fallback values:

```tsx
const contactInfo = getContactInfo();
// Returns: { phone, whatsapp, email, address, postal_code, maps_link }
```

**getVillageProfile()**
Returns village profile with fallback values:

```tsx
const profile = getVillageProfile();
// Returns: { name, district, regency, province, established_year, area, population }
```

**getOperatingHours()**
Returns operating hours with fallback values:

```tsx
const hours = getOperatingHours();
// Returns: { weekdays, saturday, sunday, break, holidays }
```

**getEmergencyContacts()**
Returns emergency contacts with fallback values:

```tsx
const contacts = getEmergencyContacts();
// Returns: { village_head, village_secretary, security, health_center }
```

### Utility Functions

For direct access without hooks (use sparingly):

```tsx
import { getSettingFromProps, getSettingProperty, hasSettingValue } from '@/utils/settings';

// Must be used within components that have Inertia page props
const contactInfo = getSettingFromProps('village.contact_info');
const phone = getSettingProperty('village.contact_info', 'phone', 'Tidak tersedia');
const hasContact = hasSettingValue('village.contact_info');
```

### HOC Pattern

For components that need settings injected as props:

```tsx
import { withSettings, type WithSettingsProps } from '@/utils/withSettings';

interface MyComponentProps extends WithSettingsProps {
    title: string;
}

function MyComponent({ title, settings }: MyComponentProps) {
    const contactInfo = settings.getContactInfo();

    return (
        <div>
            <h1>{title}</h1>
            <p>{contactInfo.phone}</p>
        </div>
    );
}

export default withSettings(MyComponent);
```

### Context Pattern

For deeply nested components:

```tsx
import { SettingsProvider, useSettingsContext } from '@/utils/withSettings';

// Wrap your app or section
function App() {
    return (
        <SettingsProvider>
            <DeepComponent />
        </SettingsProvider>
    );
}

// Use in deeply nested component
function DeepComponent() {
    const { getContactInfo } = useSettingsContext();
    const contactInfo = getContactInfo();

    return <div>{contactInfo.phone}</div>;
}
```

## TypeScript Support

All utilities provide full TypeScript support with proper type inference:

```tsx
import type { SettingKey, SettingValue, VillageSettings } from '@/types/settings';

// Type-safe setting access
const getSafeSettings = <K extends SettingKey>(key: K): SettingValue<K> | undefined => {
    return getSetting(key);
};

// Specific type imports
import type { ContactInfo, VillageProfile } from '@/types/settings';
```

## Best Practices

### 1. Use the Hook Pattern (Recommended)

```tsx
// ✅ Good - Use the hook for most cases
function ContactDisplay() {
    const { getContactInfo, isAvailable } = useSettings();

    if (!isAvailable) return null;

    const contact = getContactInfo();
    return <div>{contact.phone}</div>;
}
```

### 2. Handle Missing Settings Gracefully

```tsx
// ✅ Good - Always provide fallbacks
const { getContactInfo, hasSetting } = useSettings();

if (!hasSetting('village.contact_info')) {
    return <div>Informasi kontak belum tersedia</div>;
}

const contact = getContactInfo();
const phone = contact.phone || 'Nomor telepon belum diatur';
```

### 3. Use Convenience Methods

```tsx
// ✅ Good - Use convenience methods
const contactInfo = getContactInfo();

// ❌ Avoid - Manual object construction
const contactInfo = getSetting('village.contact_info', {
    phone: '',
    email: '',
    address: '',
    whatsapp: '',
    postal_code: '',
    maps_link: '',
});
```

### 4. Check Availability

```tsx
// ✅ Good - Check if settings are available
const { isAvailable, isEmpty } = useSettings();

if (!isAvailable || isEmpty) {
    return <div>Settings tidak tersedia</div>;
}
```

## Error Handling

All utilities include built-in error handling:

- Functions return default values when settings are missing
- Console warnings are logged for debugging
- No exceptions are thrown in normal usage
- TypeScript ensures type safety

## Performance Considerations

- Settings are memoized to prevent unnecessary re-renders
- Use the hook pattern for optimal performance
- Avoid calling utility functions in render loops
- Settings are cached at the page level by Inertia

## Examples

See `resources/js/components/examples/SettingsUsageExample.tsx` for comprehensive usage examples.

## Migration Guide

If you're updating existing code to use these utilities:

### Before

```tsx
// Old way - accessing props directly
function MyComponent({ settings }) {
    const phone = settings?.['village.contact_info']?.phone || '';
    return <div>{phone}</div>;
}
```

### After

```tsx
// New way - using the hook
function MyComponent() {
    const { getSettingProperty } = useSettings();
    const phone = getSettingProperty('village.contact_info', 'phone', '');
    return <div>{phone}</div>;
}
```
