<?php

namespace App\Providers;

use App\Services\FeatureFlagResolver;
use App\Services\SettingsFeatureStore;
use Illuminate\Support\ServiceProvider;
use Laravel\Pennant\Feature;
use Laravel\Pennant\FeatureManager;

class FeatureFlagServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        $this->app->singleton(FeatureFlagResolver::class);

        $this->app->singleton(\App\Services\FeatureFlagService::class, function ($app) {
            return new \App\Services\FeatureFlagService(
                $app->make(FeatureFlagResolver::class)
            );
        });
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        // Register custom settings store driver
        $this->app->make(FeatureManager::class)->extend('settings', function ($app, $config) {
            return new SettingsFeatureStore;
        });

        // Register custom feature resolver with Pennant
        $resolver = $this->app->make(FeatureFlagResolver::class);

        // Only define and register features if not in testing environment
        // or if database is available
        if (! $this->app->environment('testing') || $this->isDatabaseAvailable()) {
            // Define default feature flags and register them with Pennant
            $this->defineDefaultFeatures($resolver);
        }

        // Set up automatic flag registration with Pennant
        $this->registerFeaturesWithPennant($resolver);
    }

    /**
     * Define default feature flags for the application
     */
    private function defineDefaultFeatures(FeatureFlagResolver $resolver): void
    {
        // Public Page Features
        $resolver->define('pages.layanan', true, 'Mengaktifkan halaman layanan publik');
        $resolver->define('pages.berita', true, 'Mengaktifkan halaman berita publik');
        $resolver->define('pages.potensi', true, 'Mengaktifkan halaman potensi desa');
        $resolver->define('pages.pengaduan', true, 'Mengaktifkan halaman pengaduan publik');
    }

    /**
     * Register features with Pennant for automatic resolution
     */
    private function registerFeaturesWithPennant(FeatureFlagResolver $resolver): void
    {
        // Get all defined feature flags
        $defaultFeatures = [
            'pages.layanan' => true,
            'pages.berita' => true,
            'pages.potensi' => true,
            'pages.pengaduan' => true,
        ];

        // Register each feature with Pennant using our custom resolver
        foreach ($defaultFeatures as $feature => $defaultValue) {
            Feature::define($feature, function () use ($resolver, $feature, $defaultValue) {
                // In testing environment, return default value if database is not available
                if ($this->app->environment('testing') && ! $this->isDatabaseAvailable()) {
                    return $defaultValue;
                }

                return $resolver->resolve($feature);
            });
        }
    }

    /**
     * Check if database is available for operations
     */
    private function isDatabaseAvailable(): bool
    {
        try {
            \Illuminate\Support\Facades\DB::connection()->getPdo();

            // Also check if settings table exists
            return \Illuminate\Support\Facades\Schema::hasTable('settings');
        } catch (\Exception $e) {
            return false;
        }
    }
}
