<?php

namespace App\Observers;

use App\Models\Profile;
use App\Services\CacheService;

class ProfileObserver
{
    public function __construct(
        private CacheService $cacheService
    ) {}

    /**
     * Handle the Profile "created" event.
     */
    public function created(Profile $profile): void
    {
        $this->clearCache();
    }

    /**
     * Handle the Profile "updated" event.
     */
    public function updated(Profile $profile): void
    {
        $this->clearCache();
    }

    /**
     * Handle the Profile "deleted" event.
     */
    public function deleted(Profile $profile): void
    {
        $this->clearCache();
    }

    /**
     * Clear profile-related cache
     */
    private function clearCache(): void
    {
        $this->cacheService->clearProfileCache();
    }
}
