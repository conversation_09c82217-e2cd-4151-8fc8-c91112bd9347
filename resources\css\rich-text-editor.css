/* Rich Text Editor Styles */
.rich-text-editor .ql-toolbar {
    border: 1px solid hsl(var(--border));
    border-bottom: none;
    border-radius: 0.375rem 0.375rem 0 0;
    background: hsl(var(--background));
}

.rich-text-editor .ql-container {
    border: 1px solid hsl(var(--border));
    border-top: none;
    border-radius: 0 0 0.375rem 0.375rem;
    font-family: inherit;
    font-size: 0.875rem;
}

.rich-text-editor .ql-editor {
    min-height: 200px;
    padding: 0.75rem;
    color: hsl(var(--foreground));
    line-height: 1.5;
}

.rich-text-editor .ql-editor.ql-blank::before {
    color: hsl(var(--muted-foreground));
    font-style: normal;
}

.rich-text-editor--error .ql-toolbar,
.rich-text-editor--error .ql-container {
    border-color: hsl(var(--destructive));
}

.rich-text-editor--disabled .ql-toolbar {
    opacity: 0.5;
    pointer-events: none;
}

.rich-text-editor--disabled .ql-editor {
    background-color: hsl(var(--muted));
    cursor: not-allowed;
}

/* Dark mode support */
.dark .rich-text-editor .ql-toolbar {
    background: hsl(var(--background));
    border-color: hsl(var(--border));
}

.dark .rich-text-editor .ql-stroke {
    stroke: hsl(var(--foreground));
}

.dark .rich-text-editor .ql-fill {
    fill: hsl(var(--foreground));
}

.dark .rich-text-editor .ql-picker-label {
    color: hsl(var(--foreground));
}

/* Custom styling for better integration */
.rich-text-editor .ql-toolbar .ql-formats {
    margin-right: 1rem;
}

.rich-text-editor .ql-toolbar .ql-formats:last-child {
    margin-right: 0;
}

/* Focus styles */
.rich-text-editor .ql-container.ql-focused {
    border-color: hsl(var(--ring));
    box-shadow: 0 0 0 2px hsl(var(--ring) / 0.2);
}
