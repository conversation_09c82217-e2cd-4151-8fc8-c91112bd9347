import { Badge } from '@/components/ui/badge';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { cn } from '@/lib/utils';
import type { FeatureFlagToggleProps } from '@/types/feature-flags';
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, Beaker, Loader2 } from 'lucide-react';
import { useState } from 'react';

export default function FeatureFlagToggle({ flag, enabled, onToggle, disabled = false }: FeatureFlagToggleProps) {
    const [isToggling, setIsToggling] = useState(false);

    const handleToggle = async (checked: boolean) => {
        if (disabled || isToggling) return;

        setIsToggling(true);
        try {
            await onToggle(checked);
        } finally {
            setIsToggling(false);
        }
    };

    return (
        <div
            className={cn(
                'flex items-start justify-between gap-4 rounded-lg border p-4 transition-colors',
                'hover:bg-gray-50 dark:hover:bg-gray-800/50',
                disabled && 'cursor-not-allowed opacity-50',
            )}
        >
            <div className="flex-1 space-y-2">
                <div className="flex items-center gap-2">
                    <Label
                        htmlFor={`flag-${flag.key}`}
                        className="text-sm leading-none font-medium peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                    >
                        {flag.title}
                    </Label>

                    {/* Warning indicator for critical flags */}
                    {flag.warning && (
                        <div className="group relative">
                            <AlertTriangle className="h-4 w-4 text-amber-500" />
                            <div className="absolute bottom-full left-1/2 mb-2 hidden -translate-x-1/2 transform group-hover:block">
                                <div className="rounded-md bg-amber-50 px-2 py-1 text-xs text-amber-800 shadow-lg dark:bg-amber-900/20 dark:text-amber-400">
                                    {flag.warning}
                                </div>
                            </div>
                        </div>
                    )}

                    {/* Experimental badge */}
                    {flag.experimental && (
                        <Badge variant="secondary" className="text-xs">
                            <Beaker className="mr-1 h-3 w-3" />
                            Eksperimental
                        </Badge>
                    )}
                </div>

                <p className="text-sm text-gray-600 dark:text-gray-400">{flag.description}</p>

                {/* Dependencies info */}
                {flag.dependencies && flag.dependencies.length > 0 && (
                    <div className="text-xs text-gray-500 dark:text-gray-500">Memerlukan: {flag.dependencies.join(', ')}</div>
                )}
            </div>

            <div className="flex items-center gap-2">
                {/* Loading indicator */}
                {isToggling && <Loader2 className="h-4 w-4 animate-spin text-gray-400" />}

                {/* Switch toggle */}
                <Switch
                    id={`flag-${flag.key}`}
                    checked={enabled}
                    onCheckedChange={handleToggle}
                    disabled={disabled || isToggling}
                    className="data-[state=checked]:bg-green-600"
                />
            </div>
        </div>
    );
}
