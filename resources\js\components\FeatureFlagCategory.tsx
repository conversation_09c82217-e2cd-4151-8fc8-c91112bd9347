import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { cn } from '@/lib/utils';
import type { FeatureFlagCategoryProps } from '@/types/feature-flags';
import { ChevronDown, ChevronRight, ToggleLeft, ToggleRight } from 'lucide-react';
import { useState } from 'react';
import FeatureFlagToggle from './FeatureFlagToggle';

export default function FeatureFlagCategory({ category, onToggle, processing }: FeatureFlagCategoryProps) {
    const [isExpanded, setIsExpanded] = useState(true);
    const [isBulkToggling, setIsBulkToggling] = useState(false);

    const handleBulkToggle = async (enableAll: boolean) => {
        if (processing || isBulkToggling) return;

        setIsBulkToggling(true);
        try {
            // Toggle all flags in the category
            const promises = category.flags.map((flag) => {
                if (flag.enabled !== enableAll) {
                    return onToggle(flag.key, enableAll);
                }
                return Promise.resolve();
            });
            await Promise.all(promises);
        } finally {
            setIsBulkToggling(false);
        }
    };

    const getCategoryIcon = (categoryName: string) => {
        switch (categoryName) {
            case 'ui':
                return '🎨';
            case 'services':
                return '⚙️';
            case 'experimental':
                return '🧪';
            case 'performance':
                return '⚡';
            default:
                return '📋';
        }
    };

    const getCategoryColor = (categoryName: string) => {
        switch (categoryName) {
            case 'ui':
                return 'text-blue-600 dark:text-blue-400';
            case 'services':
                return 'text-green-600 dark:text-green-400';
            case 'experimental':
                return 'text-purple-600 dark:text-purple-400';
            case 'performance':
                return 'text-orange-600 dark:text-orange-400';
            default:
                return 'text-gray-600 dark:text-gray-400';
        }
    };

    return (
        <Card className="dark:bg-gray-800">
            <CardHeader className="pb-3">
                <div className="flex items-center justify-between">
                    <button
                        onClick={() => setIsExpanded(!isExpanded)}
                        className="flex items-center gap-3 text-left transition-opacity hover:opacity-80"
                    >
                        {isExpanded ? <ChevronDown className="h-5 w-5 text-gray-400" /> : <ChevronRight className="h-5 w-5 text-gray-400" />}

                        <div className="flex items-center gap-3">
                            <span className="text-2xl">{getCategoryIcon(category.name)}</span>
                            <div>
                                <h3 className={cn('text-lg font-semibold', getCategoryColor(category.name))}>{category.title}</h3>
                                <p className="text-sm text-gray-600 dark:text-gray-400">{category.description}</p>
                            </div>
                        </div>
                    </button>

                    <div className="flex items-center gap-3">
                        {/* Category summary */}
                        <div className="flex items-center gap-2">
                            <Badge variant={category.enabledCount > 0 ? 'default' : 'secondary'} className="text-xs">
                                {category.enabledCount}/{category.totalCount} Aktif
                            </Badge>
                        </div>

                        {/* Bulk operations */}
                        {isExpanded && (
                            <div className="flex items-center gap-1">
                                <Button
                                    variant="ghost"
                                    size="sm"
                                    onClick={() => handleBulkToggle(true)}
                                    disabled={processing || isBulkToggling || category.enabledCount === category.totalCount}
                                    className="h-8 px-2 text-xs"
                                >
                                    <ToggleRight className="mr-1 h-3 w-3" />
                                    Aktifkan Semua
                                </Button>
                                <Button
                                    variant="ghost"
                                    size="sm"
                                    onClick={() => handleBulkToggle(false)}
                                    disabled={processing || isBulkToggling || category.enabledCount === 0}
                                    className="h-8 px-2 text-xs"
                                >
                                    <ToggleLeft className="mr-1 h-3 w-3" />
                                    Nonaktifkan Semua
                                </Button>
                            </div>
                        )}
                    </div>
                </div>
            </CardHeader>

            {isExpanded && (
                <CardContent className="pt-0">
                    <div className="space-y-3">
                        {category.flags.map((flag) => (
                            <FeatureFlagToggle
                                key={flag.key}
                                flag={flag}
                                enabled={flag.enabled}
                                onToggle={(enabled) => onToggle(flag.key, enabled)}
                                disabled={processing || isBulkToggling}
                            />
                        ))}
                    </div>
                </CardContent>
            )}
        </Card>
    );
}
