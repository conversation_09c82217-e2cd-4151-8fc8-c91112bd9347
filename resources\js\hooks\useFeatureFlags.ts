import { usePage } from '@inertiajs/react';

interface FeatureFlags {
    [key: string]: boolean;
}

interface PageProps extends Record<string, unknown> {
    featureFlags: FeatureFlags;
}

/**
 * Hook untuk mengakses feature flags di komponen React
 *
 * @returns Object dengan method untuk mengecek status feature flag
 */
export function useFeatureFlags() {
    const { featureFlags = {} } = usePage<PageProps>().props;

    /**
     * Mengecek apakah feature flag aktif
     *
     * @param flagKey - Key dari feature flag (contoh: 'pages.layanan')
     * @returns boolean - true jika flag aktif, false jika tidak
     */
    const isEnabled = (flagKey: string): boolean => {
        return featureFlags[flagKey] ?? false;
    };

    /**
     * Mengecek apakah halaman publik dapat diakses
     *
     * @param page - Nama halaman ('layanan', 'berita', 'potensi', 'pengaduan')
     * @returns boolean - true jika halaman dapat diakses
     */
    const isPageEnabled = (page: 'layanan' | 'berita' | 'potensi' | 'pengaduan'): boolean => {
        return isEnabled(`pages.${page}`);
    };

    /**
     * Mengecek multiple feature flags dengan logika AND
     *
     * @param flagKeys - Array dari feature flag keys
     * @returns boolean - true jika semua flag aktif
     */
    const areAllEnabled = (flagKeys: string[]): boolean => {
        return flagKeys.every((key) => isEnabled(key));
    };

    /**
     * Mengecek multiple feature flags dengan logika OR
     *
     * @param flagKeys - Array dari feature flag keys
     * @returns boolean - true jika minimal satu flag aktif
     */
    const isAnyEnabled = (flagKeys: string[]): boolean => {
        return flagKeys.some((key) => isEnabled(key));
    };

    /**
     * Mendapatkan semua feature flags
     *
     * @returns FeatureFlags object
     */
    const getAllFlags = (): FeatureFlags => {
        return featureFlags;
    };

    return {
        isEnabled,
        isPageEnabled,
        areAllEnabled,
        isAnyEnabled,
        getAllFlags,
    };
}
