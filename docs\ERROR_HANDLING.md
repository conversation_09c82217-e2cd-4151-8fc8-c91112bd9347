# Error Handling System - Website Profil Desa

Sistem error handling yang komprehensif untuk Website Profil Desa Lemah Duhur dengan pesan error dalam Bahasa Indonesia.

## Komponen Utama

### 1. Error Pages
Halaman error khusus untuk berbagai status HTTP:

- **403.tsx** - <PERSON><PERSON><PERSON>
- **404.tsx** - Halaman Tidak Ditemukan  
- **500.tsx** - Kesalahan Server

Semua halaman error menggunakan desain yang konsisten dengan tombol navigasi untuk kembali atau ke beranda.

### 2. ErrorBoundary Component
React Error Boundary untuk menangkap JavaScript errors:

```tsx
import { ErrorBoundary } from '@/components/Common/ErrorBoundary';

<ErrorBoundary>
  <YourComponent />
</ErrorBoundary>
```

### 3. Global Error Handler
Utility untuk menangani berbagai jenis error:

```typescript
import { ErrorHandler } from '@/utils/errorHandler';

try {
  // API call
} catch (error) {
  const apiError = ErrorHandler.handleApiError(error);
  ErrorHandler.showError(apiError);
}
```

### 4. Error Messages Configuration
Pesan error terpusat dalam Bahasa Indonesia:

```typescript
import { ERROR_MESSAGES, getErrorMessage } from '@/config/errorMessages';

const message = getErrorMessage('NETWORK_ERROR');
// Output: "Tidak dapat terhubung ke server. Periksa koneksi internet Anda."
```

### 5. Error Notification System
Sistem notifikasi error yang dapat ditampilkan di seluruh aplikasi:

```tsx
import { useErrorHandler } from '@/hooks/useErrorHandler';

const { handleError, showError } = useErrorHandler();
```

## Penggunaan

### Dalam Components
```tsx
import { useErrorHandler } from '@/hooks/useErrorHandler';

function MyComponent() {
  const { handleError } = useErrorHandler();
  
  const handleSubmit = async () => {
    try {
      // API call
      await submitData();
    } catch (error) {
      handleError(error);
    }
  };
}
```

### Error Routes
Error pages dapat diakses melalui:
- `/errors/403` - Akses ditolak
- `/errors/404` - Halaman tidak ditemukan
- `/errors/500` - Kesalahan server

### Laravel Integration
Error handling terintegrasi dengan Laravel melalui custom Exception Handler di `app/Exceptions/Handler.php`:

```php
class Handler extends ExceptionHandler
{
    public function render($request, Throwable $e)
    {
        // Handle validation exceptions
        if ($e instanceof ValidationException) {
            return $this->handleValidationException($e, $request);
        }

        // Handle authentication exceptions
        if ($e instanceof AuthenticationException) {
            return $this->handleAuthenticationException($e, $request);
        }

        // Handle HTTP exceptions dengan pesan Bahasa Indonesia
        if ($e instanceof HttpException) {
            return $this->handleHttpException($e, $request);
        }

        return $this->handleGeneralException($e, $request);
    }
}
```

#### Fitur Laravel Error Handler:
- **Logging Komprehensif**: Semua error dicatat dengan detail lengkap
- **Multi-format Response**: Support untuk Inertia, JSON, dan HTML
- **Pesan Bahasa Indonesia**: Semua pesan error dalam Bahasa Indonesia
- **Environment Aware**: Pesan error berbeda untuk development dan production

## Jenis Error yang Ditangani

### HTTP Status Codes
- **400** - Bad Request
- **401** - Unauthorized  
- **403** - Forbidden
- **404** - Not Found
- **422** - Validation Error
- **429** - Too Many Requests
- **500** - Internal Server Error
- **503** - Service Unavailable

### Network Errors
- **NETWORK_ERROR** - Tidak dapat terhubung ke server
- **TIMEOUT** - Request timeout
- **OFFLINE** - User sedang offline

### Validation Errors
- Field validation dengan pesan dalam Bahasa Indonesia
- Support untuk multiple validation rules
- Format error yang user-friendly

## Pesan Error dalam Bahasa Indonesia

Semua pesan error menggunakan Bahasa Indonesia yang formal dan mudah dipahami:

- ✅ "Halaman yang Anda cari tidak dapat ditemukan"
- ✅ "Terjadi kesalahan pada server. Silakan coba lagi"
- ✅ "Tidak dapat terhubung ke server. Periksa koneksi internet Anda"

## Konteks Desa

Pesan error disesuaikan dengan konteks desa:

- Menyertakan informasi kontak kantor desa
- Jam operasional pelayanan
- Email administrator desa
- Bahasa yang sesuai dengan masyarakat desa

## Testing

### Manual Testing
Untuk testing error handling, gunakan `ErrorHandlingExample` component yang menyediakan berbagai simulasi error.

### Automated Testing
Error handling diuji secara otomatis melalui `tests/Feature/ErrorHandlingTest.php`:

```bash
php artisan test --filter ErrorHandlingTest
```

Test coverage meliputi:
- 404 errors untuk route yang tidak ada
- 403 errors untuk akses yang tidak diizinkan
- 500 errors untuk kesalahan server
- Validation errors dengan pesan Indonesia
- Authentication errors
- JSON API error responses

## Best Practices

1. **Selalu tangkap error** - Jangan biarkan error tidak tertangani
2. **Pesan yang jelas** - Gunakan pesan yang mudah dipahami pengguna
3. **Logging** - Log error untuk debugging
4. **Fallback UI** - Sediakan UI alternatif saat error
5. **User guidance** - Berikan petunjuk apa yang harus dilakukan user

## Maintenance

### Frontend (React/Inertia)
- Pesan error dapat diupdate di `resources/js/config/errorMessages.ts`
- Error pages dapat dikustomisasi di `resources/js/Pages/Errors/`
- Global error handling dapat dimodifikasi di `resources/js/utils/errorHandler.ts`

### Backend (Laravel)
- Custom Exception Handler di `app/Exceptions/Handler.php`
- Blade error views di `resources/views/errors/`
- Error logging dan monitoring di `app/Http/Middleware/HandleErrors.php`

### Configuration
- Error handling middleware di `bootstrap/app.php`
- Error routes di `routes/web.php`
- Environment-specific error handling di `.env`

## Monitoring & Logging

Error handling system mencatat semua error dengan informasi:
- Pesan error dan stack trace
- URL dan HTTP method
- User ID dan IP address
- User agent dan referer
- Timestamp dan environment

Log dapat dilihat di:
- `storage/logs/laravel.log` untuk development
- External monitoring service untuk production