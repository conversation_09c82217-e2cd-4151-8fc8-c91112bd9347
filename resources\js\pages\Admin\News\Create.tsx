import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { RichTextEditor } from '@/components/ui/rich-text-editor';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { Textarea } from '@/components/ui/textarea';
import AdminLayout from '@/layouts/AdminLayout';
import { type BreadcrumbItem } from '@/types';
import { Head, Link, useForm } from '@inertiajs/react';
import { ArrowLeft, FileText, Save } from 'lucide-react';
import { FormEventHandler, useRef, useState } from 'react';

interface Props {
    categories: string[];
    defaultCategory?: string;
}

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'Dashboard',
        href: '/dashboard',
    },
    {
        title: 'Manajemen Berita',
        href: '/admin/news',
    },
    {
        title: 'Tambah Berita',
        href: '/admin/news/create',
    },
];

export default function NewsCreate({ categories, defaultCategory = 'Pengumuman' }: Props) {
    const fileInputRef = useRef<HTMLInputElement>(null);
    const [previewImage, setPreviewImage] = useState<string | null>(null);
    const [newCategory, setNewCategory] = useState('');
    const [showNewCategory, setShowNewCategory] = useState(false);

    const { data, setData, post, processing, errors } = useForm({
        title: '',
        content: '',
        excerpt: '',
        category: defaultCategory,
        featured_image: null as File | null,
        is_published: false as boolean,
        published_at: '' as string,
        meta_title: '',
        meta_description: '',
    });

    const handleImageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const file = e.target.files?.[0];
        if (file) {
            setData('featured_image', file);

            // Create preview
            const reader = new FileReader();
            reader.onload = (e) => {
                setPreviewImage(e.target?.result as string);
            };
            reader.readAsDataURL(file);
        }
    };

    const removeImage = () => {
        setData('featured_image', null);
        setPreviewImage(null);
        if (fileInputRef.current) {
            fileInputRef.current.value = '';
        }
    };

    const handleCategoryChange = (value: string) => {
        if (value === 'new') {
            setShowNewCategory(true);
            setData('category', '');
        } else {
            setShowNewCategory(false);
            setData('category', value);
        }
    };

    const handleNewCategorySubmit = () => {
        if (newCategory.trim()) {
            setData('category', newCategory.trim());
            setShowNewCategory(false);
            setNewCategory('');
        }
    };

    const submit: FormEventHandler = (e) => {
        e.preventDefault();
        post(route('admin.news.store'));
    };

    return (
        <AdminLayout breadcrumbs={breadcrumbs}>
            <Head title="Tambah Berita - Admin Desa Lemah Duhur" />

            <div className="flex h-full flex-1 flex-col gap-6 p-6">
                {/* Header */}
                <div className="flex items-center justify-between">
                    <div>
                        <h1 className="text-3xl font-bold text-gray-900 dark:text-gray-100">Tambah Berita</h1>
                        <p className="text-gray-600 dark:text-gray-300">Buat berita atau pengumuman baru untuk desa</p>
                    </div>
                    <Link href={route('admin.news.index')}>
                        <Button variant="outline" className="flex items-center space-x-2">
                            <ArrowLeft className="h-4 w-4" />
                            <span>Kembali</span>
                        </Button>
                    </Link>
                </div>

                <form onSubmit={submit} className="space-y-6">
                    <div className="grid grid-cols-1 gap-6 lg:grid-cols-3">
                        {/* Main Content */}
                        <div className="space-y-6 lg:col-span-2">
                            {/* Basic Information */}
                            <Card>
                                <CardHeader>
                                    <CardTitle className="flex items-center space-x-2">
                                        <FileText className="h-5 w-5" />
                                        <span>Informasi Dasar</span>
                                    </CardTitle>
                                </CardHeader>
                                <CardContent className="space-y-4">
                                    <div className="space-y-2">
                                        <Label htmlFor="title">Judul Berita *</Label>
                                        <Input
                                            id="title"
                                            value={data.title}
                                            onChange={(e) => setData('title', e.target.value)}
                                            placeholder="Masukkan judul berita..."
                                            className={errors.title ? 'border-red-500' : ''}
                                        />
                                        {errors.title && <p className="text-sm text-red-600">{errors.title}</p>}
                                    </div>

                                    <div className="space-y-2">
                                        <Label htmlFor="excerpt">Ringkasan</Label>
                                        <Textarea
                                            id="excerpt"
                                            value={data.excerpt}
                                            onChange={(e) => setData('excerpt', e.target.value)}
                                            placeholder="Ringkasan singkat berita (opsional)..."
                                            rows={3}
                                            className={errors.excerpt ? 'border-red-500' : ''}
                                        />
                                        {errors.excerpt && <p className="text-sm text-red-600">{errors.excerpt}</p>}
                                        <p className="text-xs text-gray-500 dark:text-gray-400">
                                            Jika kosong, akan diambil dari 150 karakter pertama konten
                                        </p>
                                    </div>

                                    <div className="space-y-2">
                                        <Label htmlFor="content">Konten Berita *</Label>
                                        <RichTextEditor
                                            value={data.content}
                                            onChange={(value) => setData('content', value)}
                                            placeholder="Tulis konten berita lengkap di sini..."
                                            error={!!errors.content}
                                        />
                                        {errors.content && <p className="text-sm text-red-600">{errors.content}</p>}
                                        <p className="text-xs text-gray-500 dark:text-gray-400">
                                            Gunakan toolbar di atas untuk memformat teks, membuat daftar, dan mengatur tata letak konten
                                        </p>
                                    </div>
                                </CardContent>
                            </Card>

                            {/* SEO Settings */}
                            <Card>
                                <CardHeader>
                                    <CardTitle>Pengaturan SEO</CardTitle>
                                </CardHeader>
                                <CardContent className="space-y-4">
                                    <div className="space-y-2">
                                        <Label htmlFor="meta_title">Meta Title</Label>
                                        <Input
                                            id="meta_title"
                                            value={data.meta_title}
                                            onChange={(e) => setData('meta_title', e.target.value)}
                                            placeholder="Judul untuk mesin pencari (opsional)..."
                                            className={errors.meta_title ? 'border-red-500' : ''}
                                        />
                                        {errors.meta_title && <p className="text-sm text-red-600">{errors.meta_title}</p>}
                                        <p className="text-xs text-gray-500 dark:text-gray-400">Jika kosong, akan menggunakan judul berita</p>
                                    </div>

                                    <div className="space-y-2">
                                        <Label htmlFor="meta_description">Meta Description</Label>
                                        <Textarea
                                            id="meta_description"
                                            value={data.meta_description}
                                            onChange={(e) => setData('meta_description', e.target.value)}
                                            placeholder="Deskripsi untuk mesin pencari (opsional)..."
                                            rows={3}
                                            className={errors.meta_description ? 'border-red-500' : ''}
                                        />
                                        {errors.meta_description && <p className="text-sm text-red-600">{errors.meta_description}</p>}
                                        <p className="text-xs text-gray-500 dark:text-gray-400">Jika kosong, akan menggunakan ringkasan berita</p>
                                    </div>
                                </CardContent>
                            </Card>
                        </div>

                        {/* Sidebar */}
                        <div className="space-y-6">
                            {/* Publish Settings */}
                            <Card>
                                <CardHeader>
                                    <CardTitle>Pengaturan Publikasi</CardTitle>
                                </CardHeader>
                                <CardContent className="space-y-4">
                                    <div className="flex items-center justify-between">
                                        <Label htmlFor="is_published">Publikasikan Sekarang</Label>
                                        <Switch
                                            id="is_published"
                                            checked={data.is_published}
                                            onCheckedChange={(checked: boolean) => setData('is_published', checked)}
                                        />
                                    </div>

                                    {data.is_published && (
                                        <div className="space-y-2">
                                            <Label htmlFor="published_at">Tanggal Publikasi</Label>
                                            <Input
                                                id="published_at"
                                                type="date"
                                                value={data.published_at}
                                                onChange={(e) => setData('published_at', e.target.value)}
                                                className={errors.published_at ? 'border-red-500' : ''}
                                            />
                                            {errors.published_at && <p className="text-sm text-red-600">{errors.published_at}</p>}
                                            <p className="text-xs text-gray-500 dark:text-gray-400">Kosongkan untuk menggunakan waktu sekarang</p>
                                        </div>
                                    )}
                                </CardContent>
                            </Card>

                            {/* Category */}
                            <Card>
                                <CardHeader>
                                    <CardTitle>Kategori</CardTitle>
                                </CardHeader>
                                <CardContent className="space-y-4">
                                    {!showNewCategory ? (
                                        <div className="space-y-2">
                                            <Label>Pilih Kategori *</Label>
                                            <Select value={data.category} onValueChange={handleCategoryChange}>
                                                <SelectTrigger className={errors.category ? 'border-red-500' : ''}>
                                                    <SelectValue placeholder="Pilih kategori..." />
                                                </SelectTrigger>
                                                <SelectContent>
                                                    <SelectItem value="Pengumuman">Pengumuman</SelectItem>
                                                    <SelectItem value="Kegiatan">Kegiatan</SelectItem>
                                                    <SelectItem value="Pembangunan">Pembangunan</SelectItem>
                                                    <SelectItem value="Sosial">Sosial</SelectItem>
                                                    {categories &&
                                                        categories.length > 0 &&
                                                        categories.map(
                                                            (category) =>
                                                                !['Pengumuman', 'Kegiatan', 'Pembangunan', 'Sosial'].includes(category) && (
                                                                    <SelectItem key={category} value={category}>
                                                                        {category}
                                                                    </SelectItem>
                                                                ),
                                                        )}
                                                    <SelectItem value="new">+ Kategori Baru</SelectItem>
                                                </SelectContent>
                                            </Select>
                                            {errors.category && <p className="text-sm text-red-600">{errors.category}</p>}
                                        </div>
                                    ) : (
                                        <div className="space-y-2">
                                            <Label>Kategori Baru</Label>
                                            <div className="flex space-x-2">
                                                <Input
                                                    value={newCategory}
                                                    onChange={(e) => setNewCategory(e.target.value)}
                                                    placeholder="Nama kategori baru..."
                                                    onKeyDown={(e) => {
                                                        if (e.key === 'Enter') {
                                                            e.preventDefault();
                                                            handleNewCategorySubmit();
                                                        }
                                                    }}
                                                />
                                                <Button type="button" variant="outline" onClick={handleNewCategorySubmit}>
                                                    OK
                                                </Button>
                                            </div>
                                            <Button
                                                type="button"
                                                variant="ghost"
                                                size="sm"
                                                onClick={() => {
                                                    setShowNewCategory(false);
                                                    setNewCategory('');
                                                }}
                                            >
                                                Batal
                                            </Button>
                                        </div>
                                    )}
                                </CardContent>
                            </Card>

                            {/* Featured Image */}
                            <Card>
                                <CardHeader>
                                    <CardTitle>Gambar Unggulan</CardTitle>
                                </CardHeader>
                                <CardContent className="space-y-4">
                                    {previewImage ? (
                                        <div className="space-y-2">
                                            <img
                                                src={previewImage}
                                                alt="Preview"
                                                className="w-full rounded-lg object-cover"
                                                style={{ aspectRatio: '16/9' }}
                                            />
                                            <Button type="button" variant="outline" size="sm" onClick={removeImage} className="w-full">
                                                Hapus Gambar
                                            </Button>
                                        </div>
                                    ) : (
                                        <div className="space-y-2">
                                            <Input
                                                ref={fileInputRef}
                                                type="file"
                                                accept="image/jpeg,image/png,image/jpg,image/webp"
                                                onChange={handleImageChange}
                                                className={errors.featured_image ? 'border-red-500' : ''}
                                            />
                                            {errors.featured_image && <p className="text-sm text-red-600">{errors.featured_image}</p>}
                                            <p className="text-xs text-gray-500 dark:text-gray-400">Format: JPEG, PNG, JPG, WebP. Maksimal 2MB.</p>
                                        </div>
                                    )}
                                </CardContent>
                            </Card>

                            {/* Submit Button */}
                            <Button type="submit" disabled={processing} className="w-full">
                                <Save className="mr-2 h-4 w-4" />
                                {processing ? 'Menyimpan...' : 'Simpan Berita'}
                            </Button>
                        </div>
                    </div>
                </form>
            </div>
        </AdminLayout>
    );
}
