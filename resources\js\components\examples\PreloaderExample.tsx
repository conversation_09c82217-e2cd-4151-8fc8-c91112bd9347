import { Button } from '@/components/ui/button';
import { usePreloaderControl } from '@/hooks/use-preloader';

export default function PreloaderExample() {
    const { isLoading, showPreloader, hidePreloader } = usePreloaderControl();

    return (
        <div className="space-y-4 p-4">
            <h2 className="text-xl font-semibold">Preloader Control Example</h2>
            <p className="text-gray-600 dark:text-gray-400">Status: {isLoading ? 'Loading...' : 'Ready'}</p>
            <div className="space-x-2">
                <Button onClick={showPreloader} disabled={isLoading}>
                    Show Preloader
                </Button>
                <Button onClick={hidePreloader} disabled={!isLoading} variant="outline">
                    Hide Preloader
                </Button>
            </div>
        </div>
    );
}
