<?php

namespace Database\Factories;

use App\Models\Complaint;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Complaint>
 */
class ComplaintFactory extends Factory
{
    protected $model = Complaint::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $categories = array_keys(Complaint::getCategories());
        $statuses = array_keys(Complaint::getStatuses());
        $priorities = array_keys(Complaint::getPriorities());

        return [
            'ticket_number' => $this->generateTicketNumber(),
            'name' => fake()->name(),
            'email' => fake()->optional(0.8)->safeEmail(),
            'phone' => fake()->optional(0.7)->phoneNumber(),
            'category' => fake()->randomElement($categories),
            'subject' => fake()->sentence(6),
            'description' => fake()->paragraph(3),
            'attachments' => fake()->optional(0.3)->randomElements([
                [
                    'path' => 'complaints/sample1.jpg',
                    'original_name' => 'foto_masalah.jpg',
                    'size' => fake()->numberBetween(100000, 2000000),
                    'mime_type' => 'image/jpeg',
                ],
                [
                    'path' => 'complaints/sample2.pdf',
                    'original_name' => 'dokumen_pendukung.pdf',
                    'size' => fake()->numberBetween(50000, 1000000),
                    'mime_type' => 'application/pdf',
                ],
            ], fake()->numberBetween(1, 2)),
            'status' => fake()->randomElement($statuses),
            'priority' => fake()->randomElement($priorities),
            'visibility' => fake()->randomElement(['private', 'public']),
            'admin_response' => fake()->optional(0.4)->paragraph(2),
            'responded_at' => fake()->optional(0.4)->dateTimeBetween('-1 month', 'now'),
            'responded_by' => null,
            'created_at' => fake()->dateTimeBetween('-3 months', 'now'),
        ];
    }

    /**
     * Generate a unique ticket number
     */
    private function generateTicketNumber(): string
    {
        $date = fake()->dateTimeBetween('-3 months', 'now')->format('Ymd');
        $number = fake()->unique()->numberBetween(1, 999);

        return 'LDH-'.$date.'-'.str_pad($number, 3, '0', STR_PAD_LEFT);
    }

    /**
     * Indicate that the complaint is pending.
     */
    public function pending(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'pending',
            'admin_response' => null,
            'responded_at' => null,
            'responded_by' => null,
        ]);
    }

    /**
     * Indicate that the complaint is in progress.
     */
    public function inProgress(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'in_progress',
            'admin_response' => fake()->paragraph(),
            'responded_at' => fake()->dateTimeBetween('-1 week', 'now'),
            'responded_by' => null,
        ]);
    }

    /**
     * Indicate that the complaint is resolved.
     */
    public function resolved(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'resolved',
            'admin_response' => fake()->paragraph(),
            'responded_at' => fake()->dateTimeBetween('-1 month', 'now'),
            'responded_by' => null,
        ]);
    }

    /**
     * Indicate that the complaint is closed.
     */
    public function closed(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'closed',
            'admin_response' => fake()->paragraph(),
            'responded_at' => fake()->dateTimeBetween('-1 month', 'now'),
            'responded_by' => null,
        ]);
    }

    /**
     * Indicate that the complaint has high priority.
     */
    public function highPriority(): static
    {
        return $this->state(fn (array $attributes) => [
            'priority' => 'high',
        ]);
    }

    /**
     * Indicate that the complaint is urgent.
     */
    public function urgent(): static
    {
        return $this->state(fn (array $attributes) => [
            'priority' => 'urgent',
        ]);
    }

    /**
     * Indicate that the complaint has attachments.
     */
    public function withAttachments(): static
    {
        return $this->state(fn (array $attributes) => [
            'attachments' => [
                [
                    'path' => 'complaints/test_image.jpg',
                    'original_name' => 'foto_masalah.jpg',
                    'size' => 1024000,
                    'mime_type' => 'image/jpeg',
                ],
                [
                    'path' => 'complaints/test_document.pdf',
                    'original_name' => 'dokumen_pendukung.pdf',
                    'size' => 512000,
                    'mime_type' => 'application/pdf',
                ],
            ],
        ]);
    }

    /**
     * Indicate that the complaint has no email (phone only).
     */
    public function phoneOnly(): static
    {
        return $this->state(fn (array $attributes) => [
            'email' => null,
            'phone' => fake()->phoneNumber(),
        ]);
    }

    /**
     * Indicate that the complaint has no phone (email only).
     */
    public function emailOnly(): static
    {
        return $this->state(fn (array $attributes) => [
            'phone' => null,
            'email' => fake()->safeEmail(),
        ]);
    }
}
