import { useSettings } from '@/hooks/useSettings';
import { withSettings, type WithSettingsProps } from '@/utils/withSettings';

/**
 * Example component demonstrating different ways to access settings
 * This component shows various patterns for using the settings utilities
 */
export function SettingsUsageExample() {
    // Using the useSettings hook (recommended)
    const { getSettingProperty, hasSetting, getContactInfo, getVillageProfile, getOperatingHours, isAvailable } = useSettings();

    // Get convenience method results with fallbacks
    const contactInfo = getContactInfo() || {
        phone: '',
        whatsapp: '',
        email: '',
        address: '',
        postal_code: '',
        maps_link: '',
    };
    const villageProfile = getVillageProfile() || {
        name: 'Desa Lemah Duhur',
        district: 'Kecamatan Caringin',
        regency: 'Kabupaten Bogor',
        province: 'Jawa Barat',
        established_year: '1910-1920',
        area: '',
        population: '',
    };
    const operatingHours = getOperatingHours() || {
        weekdays: 'Senin - Jumat: 08:00 - 15:00 WIB',
        saturday: 'Sabtu: 08:00 - 12:00 WIB',
        sunday: 'Minggu: Tutup',
        break: 'Istirahat: 12:00 - 13:00 WIB',
        holidays: 'Hari libur nasional: Tutup',
    };

    // Get specific nested properties
    const villageName = String(getSettingProperty('village.profile', 'name', 'Desa Lemah Duhur'));
    const villagePhone = String(getSettingProperty('village.contact_info', 'phone', 'Tidak tersedia'));
    const weekdayHours = String(getSettingProperty('village.operating_hours', 'weekdays', 'Tidak tersedia'));

    if (!isAvailable) {
        return (
            <div className="rounded-lg border border-yellow-200 bg-yellow-50 p-4">
                <p className="text-yellow-800">Settings tidak tersedia pada halaman ini.</p>
            </div>
        );
    }

    return (
        <div className="space-y-6 rounded-lg bg-white p-6 shadow">
            <h2 className="text-2xl font-bold text-gray-900">Contoh Penggunaan Settings</h2>

            {/* Village Profile Section */}
            <div className="space-y-2">
                <h3 className="text-lg font-semibold text-gray-800">Profil Desa</h3>
                <div className="grid grid-cols-1 gap-4 text-sm md:grid-cols-2">
                    <div>
                        <span className="font-medium">Nama Desa:</span> {villageProfile.name}
                    </div>
                    <div>
                        <span className="font-medium">Kecamatan:</span> {villageProfile.district}
                    </div>
                    <div>
                        <span className="font-medium">Kabupaten:</span> {villageProfile.regency}
                    </div>
                    <div>
                        <span className="font-medium">Provinsi:</span> {villageProfile.province}
                    </div>
                    <div>
                        <span className="font-medium">Tahun Berdiri:</span> {villageProfile.established_year}
                    </div>
                    <div>
                        <span className="font-medium">Luas Wilayah:</span> {villageProfile.area || 'Belum diatur'}
                    </div>
                </div>
            </div>

            {/* Contact Information Section */}
            <div className="space-y-2">
                <h3 className="text-lg font-semibold text-gray-800">Informasi Kontak</h3>
                <div className="grid grid-cols-1 gap-4 text-sm md:grid-cols-2">
                    <div>
                        <span className="font-medium">Telepon:</span> {contactInfo.phone || 'Belum diatur'}
                    </div>
                    <div>
                        <span className="font-medium">WhatsApp:</span> {contactInfo.whatsapp || 'Belum diatur'}
                    </div>
                    <div>
                        <span className="font-medium">Email:</span> {contactInfo.email || 'Belum diatur'}
                    </div>
                    <div className="md:col-span-2">
                        <span className="font-medium">Alamat:</span> {contactInfo.address || 'Belum diatur'}
                    </div>
                </div>
            </div>

            {/* Operating Hours Section */}
            <div className="space-y-2">
                <h3 className="text-lg font-semibold text-gray-800">Jam Operasional</h3>
                <div className="space-y-1 text-sm">
                    <div>{operatingHours.weekdays}</div>
                    <div>{operatingHours.saturday}</div>
                    <div>{operatingHours.sunday}</div>
                    <div className="text-gray-600">{operatingHours.break}</div>
                    <div className="text-gray-600">{operatingHours.holidays}</div>
                </div>
            </div>

            {/* Direct Access Examples */}
            <div className="space-y-2">
                <h3 className="text-lg font-semibold text-gray-800">Contoh Akses Langsung</h3>
                <div className="space-y-1 text-sm text-gray-600">
                    <div>Nama desa (nested property): {villageName}</div>
                    <div>Telepon (nested property): {villagePhone}</div>
                    <div>Jam kerja (nested property): {weekdayHours}</div>
                </div>
            </div>

            {/* Settings Availability Check */}
            <div className="space-y-2">
                <h3 className="text-lg font-semibold text-gray-800">Status Settings</h3>
                <div className="space-y-1 text-sm">
                    <div>
                        <span className="font-medium">Kontak Info:</span>{' '}
                        <span className={hasSetting('village.contact_info') ? 'text-green-600' : 'text-red-600'}>
                            {hasSetting('village.contact_info') ? 'Tersedia' : 'Tidak tersedia'}
                        </span>
                    </div>
                    <div>
                        <span className="font-medium">Profil Desa:</span>{' '}
                        <span className={hasSetting('village.profile') ? 'text-green-600' : 'text-red-600'}>
                            {hasSetting('village.profile') ? 'Tersedia' : 'Tidak tersedia'}
                        </span>
                    </div>
                    <div>
                        <span className="font-medium">Jam Operasional:</span>{' '}
                        <span className={hasSetting('village.operating_hours') ? 'text-green-600' : 'text-red-600'}>
                            {hasSetting('village.operating_hours') ? 'Tersedia' : 'Tidak tersedia'}
                        </span>
                    </div>
                </div>
            </div>
        </div>
    );
}

/**
 * Example component using HOC pattern
 * This shows how to use withSettings for components that need settings
 */
function SettingsHOCExampleComponent({ settings }: WithSettingsProps) {
    const contactInfo = settings.getContactInfo() || { phone: '', whatsapp: '', email: '', address: '', postal_code: '', maps_link: '' };

    return (
        <div className="rounded-lg border border-blue-200 bg-blue-50 p-4">
            <h3 className="mb-2 text-lg font-semibold text-blue-800">Contoh HOC Pattern</h3>
            <p className="text-blue-700">Desa: {String(settings.getSettingProperty('village.profile', 'name', 'Tidak diketahui'))}</p>
            <p className="text-blue-700">Telepon: {contactInfo.phone || 'Belum diatur'}</p>
        </div>
    );
}

// Export the HOC-wrapped component
export const SettingsHOCExample = withSettings(SettingsHOCExampleComponent);
