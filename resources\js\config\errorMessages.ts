/**
 * Centralized error messages in Indonesian
 */
export const ERROR_MESSAGES = {
    // HTTP Status Codes
    400: 'Permintaan tidak valid. Silakan periksa data yang Anda masukkan.',
    401: '<PERSON>si Anda telah berakhir. <PERSON>lakan login kembali.',
    403: 'Anda tidak memiliki izin untuk melakukan tindakan ini.',
    404: 'Data yang diminta tidak ditemukan.',
    422: 'Data yang Anda masukkan tidak valid.',
    429: 'Terlalu banyak permintaan. Silakan coba lagi dalam beberapa saat.',
    500: 'Ter<PERSON>di kesalahan pada server. Silakan coba lagi atau hubungi administrator.',
    503: 'Layanan sedang dalam pemeliharaan. Silakan coba lagi nanti.',

    // Network Errors
    NETWORK_ERROR: 'Tidak dapat terhubung ke server. Periksa koneksi internet Anda.',
    TIMEOUT: 'Permintaan timeout. Silakan coba lagi.',
    OFFLINE: 'Anda sedang offline. Periksa koneksi internet Anda.',

    // Form Validation
    REQUIRED: 'Field ini wajib diisi.',
    EMAIL: 'Format email tidak valid.',
    MIN_LENGTH: 'Minimal {min} karakter.',
    MAX_LENGTH: 'Maksimal {max} karakter.',
    NUMERIC: 'Hanya boleh berisi angka.',
    ALPHA: 'Hanya boleh berisi huruf.',
    ALPHA_NUM: 'Hanya boleh berisi huruf dan angka.',

    // File Upload
    FILE_TOO_LARGE: 'Ukuran file terlalu besar. Maksimal {max}MB.',
    INVALID_FILE_TYPE: 'Tipe file tidak didukung. Gunakan {types}.',
    UPLOAD_FAILED: 'Gagal mengupload file. Silakan coba lagi.',

    // Authentication
    LOGIN_FAILED: 'Email atau password salah.',
    ACCOUNT_LOCKED: 'Akun Anda telah dikunci. Hubungi administrator.',
    PASSWORD_EXPIRED: 'Password Anda telah kedaluwarsa. Silakan ganti password.',

    // CRUD Operations
    CREATE_SUCCESS: 'Data berhasil ditambahkan.',
    CREATE_FAILED: 'Gagal menambahkan data. Silakan coba lagi.',
    UPDATE_SUCCESS: 'Data berhasil diperbarui.',
    UPDATE_FAILED: 'Gagal memperbarui data. Silakan coba lagi.',
    DELETE_SUCCESS: 'Data berhasil dihapus.',
    DELETE_FAILED: 'Gagal menghapus data. Silakan coba lagi.',
    DELETE_CONFIRM: 'Apakah Anda yakin ingin menghapus data ini?',

    // Generic Messages
    GENERIC_ERROR: 'Terjadi kesalahan yang tidak terduga.',
    TRY_AGAIN: 'Silakan coba lagi.',
    CONTACT_ADMIN: 'Jika masalah berlanjut, hubungi administrator.',
    LOADING: 'Memuat...',
    NO_DATA: 'Tidak ada data.',
    SEARCH_NO_RESULTS: 'Tidak ada hasil yang ditemukan.',

    // Village Specific
    VILLAGE_CONTACT: 'Hubungi kantor desa untuk bantuan lebih lanjut.',
    SERVICE_UNAVAILABLE: 'Layanan ini sedang tidak tersedia. Silakan hubungi kantor desa.',
    OFFICE_HOURS: 'Kantor desa buka Senin-Jumat, 08:00-16:00 WIB.',
} as const;

/**
 * Get error message by key with optional parameters
 */
export function getErrorMessage(key: keyof typeof ERROR_MESSAGES, params?: Record<string, string | number>): string {
    let message: string = ERROR_MESSAGES[key] || ERROR_MESSAGES.GENERIC_ERROR;

    if (params) {
        Object.entries(params).forEach(([param, value]) => {
            message = message.replace(`{${param}}`, String(value));
        });
    }

    return message;
}

/**
 * Get HTTP status error message
 */
export function getHttpErrorMessage(status: number): string {
    const key = status as keyof typeof ERROR_MESSAGES;
    return ERROR_MESSAGES[key] || ERROR_MESSAGES.GENERIC_ERROR;
}

/**
 * Format validation errors for display
 */
export function formatValidationError(field: string, rule: string, params?: Record<string, string | number>): string {
    const fieldName = field.replace('_', ' ').toLowerCase();

    switch (rule) {
        case 'required':
            return `${fieldName} ${ERROR_MESSAGES.REQUIRED}`;
        case 'email':
            return `${fieldName} ${ERROR_MESSAGES.EMAIL}`;
        case 'min':
            return getErrorMessage('MIN_LENGTH', { min: params?.min || 0 });
        case 'max':
            return getErrorMessage('MAX_LENGTH', { max: params?.max || 0 });
        case 'numeric':
            return `${fieldName} ${ERROR_MESSAGES.NUMERIC}`;
        case 'alpha':
            return `${fieldName} ${ERROR_MESSAGES.ALPHA}`;
        case 'alpha_num':
            return `${fieldName} ${ERROR_MESSAGES.ALPHA_NUM}`;
        default:
            return `${fieldName} tidak valid.`;
    }
}
