<?php

use App\Models\Profile;

describe('Profile Model', function () {
    it('can create a profile', function () {
        $profile = Profile::factory()->create([
            'section' => 'history',
            'title' => 'Sejarah Desa',
            'content' => 'Konten sejarah desa',
        ]);

        expect($profile->section)->toBe('history');
        expect($profile->title)->toBe('Sejarah Desa');
        expect($profile->content)->toBe('Konten sejarah desa');
    });

    it('casts order to integer', function () {
        $profile = Profile::factory()->create([
            'order' => '5',
        ]);

        expect($profile->order)->toBe(5);
        expect($profile->order)->toBeInt();
    });

    it('casts image to array', function () {
        $imageData = [
            'original' => ['url' => 'original.jpg'],
            'medium' => ['url' => 'medium.jpg'],
        ];

        $profile = Profile::factory()->create([
            'image' => $imageData,
        ]);

        expect($profile->image)->toBe($imageData);
        expect($profile->image)->toBeArray();
    });

    describe('Scopes', function () {
        beforeEach(function () {
            Profile::factory()->history()->create(['order' => 2]);
            Profile::factory()->history()->create(['order' => 1]);
            Profile::factory()->visionMission()->create(['order' => 1]);
        });

        it('filters by section', function () {
            $historyProfiles = Profile::bySection('history')->get();

            expect($historyProfiles)->toHaveCount(2);
            $historyProfiles->each(function ($profile) {
                expect($profile->section)->toBe('history');
            });
        });

        it('orders profiles correctly', function () {
            $orderedProfiles = Profile::bySection('history')->ordered()->get();

            expect($orderedProfiles->first()->order)->toBe(1);
            expect($orderedProfiles->last()->order)->toBe(2);
        });
    });

    describe('Static Methods', function () {
        beforeEach(function () {
            Profile::factory()->history()->create(['order' => 2]);
            Profile::factory()->history()->create(['order' => 1]);
            Profile::factory()->visionMission()->create(['order' => 1]);
            Profile::factory()->demographics()->create(['order' => 1]);
            Profile::factory()->organization()->create(['order' => 1]);
            Profile::factory()->geography()->create(['order' => 1]);
        });

        it('gets history profiles', function () {
            $history = Profile::getHistory();

            expect($history)->toHaveCount(2);
            $history->each(function ($profile) {
                expect($profile->section)->toBe('history');
            });
            expect($history->first()->order)->toBe(1);
        });

        it('gets vision mission profiles', function () {
            $visionMission = Profile::getVisionMission();

            expect($visionMission)->toHaveCount(1);
            expect($visionMission->first()->section)->toBe('vision_mission');
        });

        it('gets demographics profiles', function () {
            $demographics = Profile::getDemographics();

            expect($demographics)->toHaveCount(1);
            expect($demographics->first()->section)->toBe('demographics');
        });

        it('gets organization profiles', function () {
            $organization = Profile::getOrganization();

            expect($organization)->toHaveCount(1);
            expect($organization->first()->section)->toBe('organization');
        });

        it('gets geography profiles', function () {
            $geography = Profile::getGeography();

            expect($geography)->toHaveCount(1);
            expect($geography->first()->section)->toBe('geography');
        });
    });

    describe('Image Helpers', function () {
        it('returns null for image URL when no image', function () {
            $profile = Profile::factory()->create([
                'image' => null,
            ]);

            expect($profile->getImageUrl())->toBeNull();
        });

        it('returns correct image URL for specified size', function () {
            $imageData = [
                'original' => ['url' => 'original.jpg'],
                'medium' => ['url' => 'medium.jpg'],
                'thumbnail' => ['url' => 'thumbnail.jpg'],
            ];

            $profile = Profile::factory()->create([
                'image' => $imageData,
            ]);

            expect($profile->getImageUrl('medium'))->toBe('medium.jpg');
            expect($profile->getImageUrl('thumbnail'))->toBe('thumbnail.jpg');
        });

        it('falls back to medium size when requested size not available', function () {
            $imageData = [
                'medium' => ['url' => 'medium.jpg'],
            ];

            $profile = Profile::factory()->create([
                'image' => $imageData,
            ]);

            expect($profile->getImageUrl('large'))->toBe('medium.jpg');
        });

        it('generates correct srcset for responsive images', function () {
            $imageData = [
                'original' => ['url' => 'original.jpg'],
                'medium' => ['url' => 'medium.jpg'],
                'thumbnail' => ['url' => 'thumbnail.jpg'],
            ];

            $profile = Profile::factory()->create([
                'image' => $imageData,
            ]);

            $srcSet = $profile->getImageSrcSet();

            expect($srcSet)->toContain('thumbnail.jpg 300w');
            expect($srcSet)->toContain('medium.jpg 800w');
            expect($srcSet)->toContain('original.jpg 1200w');
        });

        it('returns empty string for srcset when no image', function () {
            $profile = Profile::factory()->create([
                'image' => null,
            ]);

            expect($profile->getImageSrcSet())->toBe('');
        });
    });

    describe('Factory States', function () {
        it('creates history profile', function () {
            $profile = Profile::factory()->history()->create();

            expect($profile->section)->toBe('history');
        });

        it('creates vision mission profile', function () {
            $profile = Profile::factory()->visionMission()->create();

            expect($profile->section)->toBe('vision_mission');
        });

        it('creates organization profile', function () {
            $profile = Profile::factory()->organization()->create();

            expect($profile->section)->toBe('organization');
        });

        it('creates demographics profile', function () {
            $profile = Profile::factory()->demographics()->create();

            expect($profile->section)->toBe('demographics');
        });

        it('creates geography profile', function () {
            $profile = Profile::factory()->geography()->create();

            expect($profile->section)->toBe('geography');
        });

        it('creates profile with image', function () {
            $profile = Profile::factory()->withImage()->create();

            expect($profile->image)->not()->toBeNull();
        });

        it('creates profile for specific section', function () {
            $profile = Profile::factory()->forSection('custom_section')->create();

            expect($profile->section)->toBe('custom_section');
        });
    });

    describe('Validation', function () {
        it('requires section', function () {
            expect(fn () => Profile::factory()->create(['section' => null]))
                ->toThrow(\Illuminate\Database\QueryException::class);
        });

        it('allows null values for optional fields', function () {
            $profile = Profile::factory()->create([
                'image' => null,
            ]);

            expect($profile)->toBeInstanceOf(Profile::class);
        });

        it('defaults order to reasonable value', function () {
            $profile = Profile::factory()->create();

            expect($profile->order)->toBeInt();
        });
    });
});
