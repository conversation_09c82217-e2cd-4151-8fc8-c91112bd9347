import { Button } from '@/components/ui/button';
import { AlertTriangle, X } from 'lucide-react';
import { useEffect, useState } from 'react';

interface ErrorNotificationProps {
    message: string;
    type?: 'error' | 'warning' | 'info';
    autoHide?: boolean;
    duration?: number;
    onClose?: () => void;
}

export function ErrorNotification({ message, type = 'error', autoHide = true, duration = 5000, onClose }: ErrorNotificationProps) {
    const [isVisible, setIsVisible] = useState(true);

    useEffect(() => {
        if (autoHide && duration > 0) {
            const timer = setTimeout(() => {
                setIsVisible(false);
                onClose?.();
            }, duration);

            return () => clearTimeout(timer);
        }
    }, [autoHide, duration, onClose]);

    if (!isVisible) return null;

    const handleClose = () => {
        setIsVisible(false);
        onClose?.();
    };

    const getStyles = () => {
        switch (type) {
            case 'error':
                return {
                    container: 'bg-red-50 border-red-200 text-red-800',
                    icon: 'text-red-500',
                    button: 'text-red-600 hover:text-red-800',
                };
            case 'warning':
                return {
                    container: 'bg-yellow-50 border-yellow-200 text-yellow-800',
                    icon: 'text-yellow-500',
                    button: 'text-yellow-600 hover:text-yellow-800',
                };
            case 'info':
                return {
                    container: 'bg-blue-50 border-blue-200 text-blue-800',
                    icon: 'text-blue-500',
                    button: 'text-blue-600 hover:text-blue-800',
                };
            default:
                return {
                    container: 'bg-red-50 border-red-200 text-red-800',
                    icon: 'text-red-500',
                    button: 'text-red-600 hover:text-red-800',
                };
        }
    };

    const styles = getStyles();

    return (
        <div className={`fixed top-4 right-4 z-50 max-w-md rounded-lg border p-4 shadow-lg ${styles.container}`}>
            <div className="flex items-start">
                <AlertTriangle className={`mt-0.5 mr-3 h-5 w-5 flex-shrink-0 ${styles.icon}`} />
                <div className="flex-1">
                    <p className="text-sm font-medium">{message}</p>
                </div>
                <Button variant="ghost" size="sm" onClick={handleClose} className={`ml-2 h-6 w-6 p-0 ${styles.button}`}>
                    <X className="h-4 w-4" />
                </Button>
            </div>
        </div>
    );
}

// Hook for managing error notifications
export function useErrorNotification() {
    const [notification, setNotification] = useState<{
        message: string;
        type: 'error' | 'warning' | 'info';
    } | null>(null);

    const showError = (message: string) => {
        setNotification({ message, type: 'error' });
    };

    const showWarning = (message: string) => {
        setNotification({ message, type: 'warning' });
    };

    const showInfo = (message: string) => {
        setNotification({ message, type: 'info' });
    };

    const hideNotification = () => {
        setNotification(null);
    };

    return {
        notification,
        showError,
        showWarning,
        showInfo,
        hideNotification,
    };
}
