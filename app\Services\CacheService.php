<?php

namespace App\Services;

use Illuminate\Support\Facades\Cache;

class CacheService
{
    /**
     * Clear all page cache
     */
    public function clearPageCache(): void
    {
        $keys = Cache::get('page_cache_keys', []);

        foreach ($keys as $key) {
            Cache::forget($key);
        }

        Cache::forget('page_cache_keys');
    }

    /**
     * Clear cache for specific route pattern
     */
    public function clearRouteCache(string $pattern): void
    {
        $keys = Cache::get('page_cache_keys', []);

        foreach ($keys as $key) {
            if (str_contains($key, $pattern)) {
                Cache::forget($key);
            }
        }
    }

    /**
     * Clear news-related cache
     */
    public function clearNewsCache(): void
    {
        $this->clearRouteCache('berita');
        $this->clearRouteCache('/'); // Homepage has news
        Cache::forget('news_latest');
        Cache::forget('news_categories');
    }

    /**
     * Clear service-related cache
     */
    public function clearServiceCache(): void
    {
        $this->clearRouteCache('layanan');
        $this->clearRouteCache('/'); // Homepage has services
        Cache::forget('services_active');
        Cache::forget('services_featured');
    }

    /**
     * Clear potential-related cache
     */
    public function clearPotentialCache(): void
    {
        $this->clearRouteCache('potensi');
        $this->clearRouteCache('/'); // Homepage has potentials
        Cache::forget('potentials_featured');
        Cache::forget('potentials_tourism');
        Cache::forget('potentials_umkm');
    }

    /**
     * Clear profile-related cache
     */
    public function clearProfileCache(): void
    {
        $this->clearRouteCache('profil');
        Cache::forget('profile_sections');
    }

    /**
     * Get cached data with fallback - CACHING DISABLED
     */
    public function remember(string $key, int $ttl, callable $callback)
    {
        // Always execute callback directly, no caching
        return $callback();
    }

    /**
     * Cache frequently accessed data - CACHING DISABLED
     */
    public function cacheCommonData(): void
    {
        // Caching disabled - this method now does nothing
        // Data will be fetched fresh on each request
    }
}
