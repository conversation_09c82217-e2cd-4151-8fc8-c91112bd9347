<?php

namespace App\Http\Controllers;

use App\Http\Requests\ImageUploadRequest;
use App\Services\ImageOptimizationService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;

class ImageUploadController extends Controller
{
    protected ImageOptimizationService $imageService;

    public function __construct(ImageOptimizationService $imageService)
    {
        $this->imageService = $imageService;
    }

    /**
     * Upload single image
     */
    public function upload(ImageUploadRequest $request): JsonResponse
    {
        try {
            $file = $request->file('image');
            $directory = $request->getValidatedDirectory();
            $sizes = $request->getValidatedSizes();

            $result = $this->imageService->uploadAndOptimize($file, $directory, $sizes);

            return response()->json([
                'success' => true,
                'message' => 'Gambar berhasil diupload.',
                'data' => $result,
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Gagal mengupload gambar: '.$e->getMessage(),
            ], 422);
        }
    }

    /**
     * Upload multiple images
     */
    public function uploadMultiple(Request $request): JsonResponse
    {
        $request->validate([
            'images' => 'required|array|max:10',
            'images.*' => 'required|image|mimes:jpeg,png,jpg,gif,webp|max:10240',
            'directory' => 'sometimes|string|max:255',
        ], [
            'images.required' => 'Minimal satu gambar wajib diupload.',
            'images.array' => 'Format upload tidak valid.',
            'images.max' => 'Maksimal 10 gambar dapat diupload sekaligus.',
            'images.*.required' => 'Semua file gambar wajib diisi.',
            'images.*.image' => 'Semua file harus berupa gambar.',
            'images.*.mimes' => 'Format gambar harus JPEG, PNG, JPG, GIF, atau WebP.',
            'images.*.max' => 'Ukuran setiap gambar maksimal 10MB.',
        ]);

        try {
            $files = $request->file('images');
            $directory = $request->input('directory', 'images');
            $sizes = ['original' => null, 'medium' => 800, 'thumbnail' => 300];

            $results = [];
            $errors = [];

            foreach ($files as $index => $file) {
                try {
                    $result = $this->imageService->uploadAndOptimize($file, $directory, $sizes);
                    $results[] = $result;
                } catch (\Exception $e) {
                    $errors[] = 'File '.($index + 1).': '.$e->getMessage();
                }
            }

            if ($errors !== []) {
                return response()->json([
                    'success' => false,
                    'message' => 'Beberapa gambar gagal diupload.',
                    'errors' => $errors,
                    'data' => $results,
                ], 422);
            }

            return response()->json([
                'success' => true,
                'message' => count($results).' gambar berhasil diupload.',
                'data' => $results,
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Gagal mengupload gambar: '.$e->getMessage(),
            ], 422);
        }
    }

    /**
     * Delete image
     */
    public function delete(Request $request): JsonResponse
    {
        $request->validate([
            'path' => 'required|string',
        ], [
            'path.required' => 'Path gambar wajib diisi.',
            'path.string' => 'Path gambar tidak valid.',
        ]);

        try {
            $path = $request->input('path');

            // Verify file exists and is in allowed directory
            if (! Storage::disk('public')->exists($path)) {
                return response()->json([
                    'success' => false,
                    'message' => 'Gambar tidak ditemukan.',
                ], 404);
            }

            // Check if path is in allowed directories
            $allowedDirectories = ['images', 'news', 'profiles', 'services', 'potentials'];
            $isAllowed = false;

            foreach ($allowedDirectories as $dir) {
                if (str_starts_with($path, $dir.'/')) {
                    $isAllowed = true;
                    break;
                }
            }

            if (! $isAllowed) {
                return response()->json([
                    'success' => false,
                    'message' => 'Tidak diizinkan menghapus file ini.',
                ], 403);
            }

            $this->imageService->deleteImage($path);

            return response()->json([
                'success' => true,
                'message' => 'Gambar berhasil dihapus.',
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Gagal menghapus gambar: '.$e->getMessage(),
            ], 422);
        }
    }

    /**
     * Generate blur placeholder
     */
    public function generatePlaceholder(Request $request): JsonResponse
    {
        $request->validate([
            'path' => 'required|string',
        ]);

        try {
            $path = $request->input('path');

            if (! Storage::disk('public')->exists($path)) {
                return response()->json([
                    'success' => false,
                    'message' => 'Gambar tidak ditemukan.',
                ], 404);
            }

            $placeholder = $this->imageService->generateBlurPlaceholder($path);

            return response()->json([
                'success' => true,
                'data' => [
                    'placeholder' => $placeholder,
                ],
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Gagal membuat placeholder: '.$e->getMessage(),
            ], 422);
        }
    }
}
