# DatePicker Component

Komponen DatePicker yang modern dan UI-friendly untuk aplikasi <PERSON>, dibangun menggunakan React DayPicker, Radix UI, dan date-fns dengan lokalisasi Indonesia.

## Features

- ✅ **Modern UI**: <PERSON>ain yang clean dan konsisten dengan design system
- ✅ **Lokalisasi Indonesia**: Format tanggal dan bahasa Indonesia
- ✅ **Responsive**: Bekerja dengan baik di desktop dan mobile
- ✅ **Accessible**: Mengikuti standar accessibility dengan keyboard navigation
- ✅ **Error States**: Dukungan untuk menampilkan error state
- ✅ **Date & DateTime**: <PERSON>a varian untuk tanggal saja atau tanggal + waktu
- ✅ **TypeScript**: Full TypeScript support dengan type safety

## Installation

Dependencies sudah terinstall:
- `react-day-picker` - Calendar component
- `date-fns` - Date utility library
- `@radix-ui/react-popover` - Popover component

## Components

### DatePicker

Komponen untuk memilih tanggal saja.

```tsx
import { DatePicker } from '@/components/ui/date-picker';

const [date, setDate] = useState<Date | undefined>();

<DatePicker
  date={date}
  onDateChange={setDate}
  placeholder="Pilih tanggal"
  error={false}
  disabled={false}
  className="w-full"
/>
```

#### Props

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `date` | `Date \| undefined` | - | Tanggal yang dipilih |
| `onDateChange` | `(date: Date \| undefined) => void` | - | Callback ketika tanggal berubah |
| `placeholder` | `string` | "Pilih tanggal" | Placeholder text |
| `disabled` | `boolean` | `false` | Disable date picker |
| `className` | `string` | - | CSS class tambahan |
| `error` | `boolean` | `false` | Tampilkan error state |

### DateTimePicker

Komponen untuk memilih tanggal dan waktu.

```tsx
import { DateTimePicker } from '@/components/ui/date-picker';

const [dateTime, setDateTime] = useState<Date | undefined>();

<DateTimePicker
  date={dateTime}
  onDateChange={setDateTime}
  placeholder="Pilih tanggal dan waktu"
  showTime={true}
  error={false}
  disabled={false}
  className="w-full"
/>
```

#### Props

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `date` | `Date \| undefined` | - | Tanggal dan waktu yang dipilih |
| `onDateChange` | `(date: Date \| undefined) => void` | - | Callback ketika tanggal/waktu berubah |
| `placeholder` | `string` | "Pilih tanggal dan waktu" | Placeholder text |
| `showTime` | `boolean` | `true` | Tampilkan input waktu |
| `disabled` | `boolean` | `false` | Disable date picker |
| `className` | `string` | - | CSS class tambahan |
| `error` | `boolean` | `false` | Tampilkan error state |

## Usage Examples

### Basic Date Selection

```tsx
const [publishDate, setPublishDate] = useState<Date | undefined>();

<div className="space-y-2">
  <Label>Tanggal Publikasi</Label>
  <DatePicker
    date={publishDate}
    onDateChange={setPublishDate}
    placeholder="Pilih tanggal publikasi"
  />
</div>
```

### With Form Validation

```tsx
const [date, setDate] = useState<Date | undefined>();
const [errors, setErrors] = useState<{date?: string}>({});

<div className="space-y-2">
  <Label>Tanggal Wajib</Label>
  <DatePicker
    date={date}
    onDateChange={setDate}
    placeholder="Pilih tanggal"
    error={!!errors.date}
  />
  {errors.date && (
    <p className="text-sm text-red-600">{errors.date}</p>
  )}
</div>
```

### Date Range Filter

```tsx
const [dateFrom, setDateFrom] = useState<Date | undefined>();
const [dateTo, setDateTo] = useState<Date | undefined>();

<div className="grid grid-cols-2 gap-4">
  <div className="space-y-2">
    <Label>Dari Tanggal</Label>
    <DatePicker
      date={dateFrom}
      onDateChange={setDateFrom}
      placeholder="Dari tanggal"
    />
  </div>
  <div className="space-y-2">
    <Label>Sampai Tanggal</Label>
    <DatePicker
      date={dateTo}
      onDateChange={setDateTo}
      placeholder="Sampai tanggal"
    />
  </div>
</div>
```

### Event Scheduling

```tsx
const [eventDateTime, setEventDateTime] = useState<Date | undefined>();

<div className="space-y-2">
  <Label>Jadwal Event</Label>
  <DateTimePicker
    date={eventDateTime}
    onDateChange={setEventDateTime}
    placeholder="Pilih tanggal dan waktu event"
    showTime={true}
  />
</div>
```

## Integration with Forms

### Inertia.js Forms

```tsx
import { useForm } from '@inertiajs/react';

const { data, setData, post, errors } = useForm({
  title: '',
  published_at: null as Date | null,
});

const submit = (e: FormEvent) => {
  e.preventDefault();
  
  // Convert Date to string for backend
  const publishedAtString = data.published_at 
    ? data.published_at.toISOString().slice(0, 10) 
    : '';
  
  post(route('news.store'), {
    data: {
      ...data,
      published_at: publishedAtString,
    }
  });
};

<DatePicker
  date={data.published_at || undefined}
  onDateChange={(date) => setData('published_at', date || null)}
  placeholder="Pilih tanggal publikasi"
  error={!!errors.published_at}
/>
```

## Styling

Komponen menggunakan CSS variables yang dapat dikustomisasi:

```css
.rdp {
  --rdp-cell-size: 40px;
  --rdp-accent-color: var(--color-primary);
  --rdp-background-color: var(--color-background);
}
```

## Accessibility

- ✅ Keyboard navigation (Arrow keys, Enter, Escape)
- ✅ Screen reader support
- ✅ Focus management
- ✅ ARIA labels dan roles

## Browser Support

- ✅ Chrome/Edge 88+
- ✅ Firefox 78+
- ✅ Safari 14+
- ✅ Mobile browsers

## Migration from Native Date Input

Untuk mengganti `<input type="date">` dengan DatePicker:

**Before:**
```tsx
<Input
  type="date"
  value={data.published_at}
  onChange={(e) => setData('published_at', e.target.value)}
/>
```

**After:**
```tsx
<DatePicker
  date={data.published_at ? new Date(data.published_at) : undefined}
  onDateChange={(date) => setData('published_at', date || null)}
  placeholder="Pilih tanggal publikasi"
/>
```
