<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Default Pennant Store
    |--------------------------------------------------------------------------
    |
    | Here you will specify the default store that <PERSON><PERSON> should use when
    | storing and resolving feature flag values. We use 'settings' to integrate
    | with our existing settings system for consistency.
    |
    | Supported: "array", "database", "settings"
    |
    */

    'default' => env('PENNANT_STORE', 'settings'),

    /*
    |--------------------------------------------------------------------------
    | Pennant Stores
    |--------------------------------------------------------------------------
    |
    | Here you may configure each of the stores that should be available to
    | Pennant. These stores shall be used to store resolved feature flag
    | values - you may configure as many as your application requires.
    |
    */

    'stores' => [

        'array' => [
            'driver' => 'array',
        ],

        'database' => [
            'driver' => 'database',
            'connection' => null,
            'table' => 'features',
        ],

        'settings' => [
            'driver' => 'settings',
        ],

    ],
];
