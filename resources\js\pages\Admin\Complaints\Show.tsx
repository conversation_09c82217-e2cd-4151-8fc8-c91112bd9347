import { Badge } from '@/components/ui/badge';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Separator } from '@/components/ui/separator';
import { Textarea } from '@/components/ui/textarea';
import AdminLayout from '@/layouts/AdminLayout';
import { Head, Link, useForm } from '@inertiajs/react';
import {
    AlertTriangle,
    ArrowLeft,
    Calendar,
    CheckCircle,
    Clock,
    Download,
    FileText,
    Image,
    Mail,
    MessageSquare,
    Phone,
    Send,
    User,
    XCircle,
} from 'lucide-react';
import React, { useState } from 'react';

interface Complaint {
    id: number;
    ticket_number: string;
    name: string;
    email: string;
    phone: string;
    category: string;
    subject: string;
    description: string;
    status: string;

    priority: string;
    visibility: string;
    admin_response: string | null;
    attachments: Array<{
        path: string;
        original_name: string;
        size: number;
        mime_type: string;
    }> | null;
    created_at: string;
    responded_at: string | null;
    responded_by: {
        name: string;
    } | null;
}

interface Props {
    complaint: Complaint;

    statuses: Record<string, string>;
    priorities: Record<string, string>;
    visibilities: Record<string, string>;
    responseTemplates: Record<string, string>;
}

export default function ComplaintShow({ complaint, statuses, priorities, visibilities, responseTemplates }: Props) {
    const [activeTab, setActiveTab] = useState<'details' | 'response'>('details');
    // Removed modal state for image preview

    const { data, setData, put, post, processing, errors } = useForm({
        status: complaint.status,
        priority: complaint.priority,
        visibility: complaint.visibility,
        admin_response: complaint.admin_response || '',
    });

    const handleStatusUpdate = (e: React.FormEvent) => {
        e.preventDefault();
        put(route('admin.complaints.update-status', complaint.id), {
            onSuccess: () => {
                // Success handled by flash message
            },
        });
    };

    const handleResponse = (e: React.FormEvent) => {
        e.preventDefault();

        // Update the form data before submitting
        setData({
            ...data,
            admin_response: data.admin_response,
            status: data.status !== complaint.status ? data.status : complaint.status,
        });

        post(route('admin.complaints.respond', complaint.id), {
            onSuccess: () => {
                // Success handled by flash message
            },
        });
    };

    const applyTemplate = (template: string) => {
        setData('admin_response', responseTemplates[template]);
    };

    const getStatusIcon = (status: string) => {
        switch (status) {
            case 'pending':
                return <Clock className="h-5 w-5" />;
            case 'in_progress':
                return <MessageSquare className="h-5 w-5" />;
            case 'resolved':
                return <CheckCircle className="h-5 w-5" />;
            case 'closed':
                return <XCircle className="h-5 w-5" />;
            default:
                return <Clock className="h-5 w-5" />;
        }
    };

    const getPriorityIcon = (priority: string) => {
        if (priority === 'urgent') {
            return <AlertTriangle className="h-5 w-5" />;
        }
        return null;
    };

    const formatFileSize = (bytes: number) => {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    };

    // Removed modal handlers for image preview

    return (
        <AdminLayout>
            <Head title={`Pengaduan ${complaint.ticket_number}`} />

            <div className="flex h-full flex-1 flex-col gap-6 p-6">
                {/* Header */}
                <Link href={route('admin.complaints.index')}>
                    <Button variant="outline" size="sm">
                        <ArrowLeft className="mr-2 h-4 w-4" />
                        Kembali
                    </Button>
                </Link>
                <div className="flex items-center justify-between">
                    <div className="flex items-center gap-4">
                        <div>
                            <h1 className="text-3xl font-bold text-gray-900 dark:text-gray-100">Pengaduan {complaint.ticket_number}</h1>
                            <p className="text-gray-600 dark:text-gray-400">Detail pengaduan dari {complaint.name}</p>
                        </div>
                    </div>
                    <div className="flex items-center gap-2">
                        <Badge
                            variant={complaint.status === 'resolved' ? 'default' : 'secondary'}
                            className={`flex items-center gap-2 px-3 py-1 ${
                                complaint.status === 'pending'
                                    ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300'
                                    : complaint.status === 'in_progress'
                                      ? 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300'
                                      : complaint.status === 'resolved'
                                        ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300'
                                        : 'bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-300'
                            }`}
                        >
                            {getStatusIcon(complaint.status)}
                            {complaint.status}
                        </Badge>
                        <Badge
                            variant="outline"
                            className={`flex items-center gap-2 px-3 py-1 ${
                                complaint.priority === 'urgent'
                                    ? 'border-red-200 bg-red-50 text-red-800 dark:border-red-700 dark:bg-red-900 dark:text-red-300'
                                    : complaint.priority === 'high'
                                      ? 'border-orange-200 bg-orange-50 text-orange-800 dark:border-orange-700 dark:bg-orange-900 dark:text-orange-300'
                                      : complaint.priority === 'medium'
                                        ? 'border-yellow-200 bg-yellow-50 text-yellow-800 dark:border-yellow-700 dark:bg-yellow-900 dark:text-yellow-300'
                                        : 'border-green-200 bg-green-50 text-green-800 dark:border-green-700 dark:bg-green-900 dark:text-green-300'
                            }`}
                        >
                            {getPriorityIcon(complaint.priority)}
                            {complaint.priority}
                        </Badge>
                        <Badge
                            variant={complaint.visibility === 'public' ? 'default' : 'secondary'}
                            className={`flex items-center gap-2 px-3 py-1 ${
                                complaint.visibility === 'public'
                                    ? 'border-green-200 bg-green-50 text-green-800 dark:border-green-700 dark:bg-green-900 dark:text-green-300'
                                    : 'border-gray-200 bg-gray-50 text-gray-800 dark:border-gray-700 dark:bg-gray-800 dark:text-gray-300'
                            }`}
                        >
                            {complaint.visibility}
                        </Badge>
                    </div>
                </div>

                <div className="grid grid-cols-1 gap-6 lg:grid-cols-3">
                    {/* Main Content */}
                    <div className="space-y-6 lg:col-span-2">
                        {/* Tabs */}
                        <div className="flex border-b">
                            <button
                                className={`px-4 py-2 font-medium ${
                                    activeTab === 'details'
                                        ? 'border-b-2 border-blue-500 text-blue-600 dark:text-blue-400'
                                        : 'text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300'
                                }`}
                                onClick={() => setActiveTab('details')}
                            >
                                Detail Pengaduan
                            </button>
                            <button
                                className={`px-4 py-2 font-medium ${
                                    activeTab === 'response'
                                        ? 'border-b-2 border-blue-500 text-blue-600 dark:text-blue-400'
                                        : 'text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300'
                                }`}
                                onClick={() => setActiveTab('response')}
                            >
                                Respon & Status
                            </button>
                        </div>

                        {/* Detail Tab */}
                        {activeTab === 'details' && (
                            <Card>
                                <CardHeader>
                                    <CardTitle className="flex items-center gap-2">
                                        <FileText className="h-5 w-5" />
                                        Detail Pengaduan
                                    </CardTitle>
                                </CardHeader>
                                <CardContent className="space-y-6">
                                    <div>
                                        <h3 className="mb-2 text-lg font-semibold">{complaint.subject}</h3>
                                        <Badge variant="outline">{complaint.category}</Badge>
                                    </div>

                                    <Separator />

                                    <div>
                                        <h4 className="mb-2 font-medium">Deskripsi Pengaduan</h4>
                                        <div className="prose prose-sm max-w-none">
                                            <p className="whitespace-pre-wrap text-gray-700 dark:text-gray-300">{complaint.description}</p>
                                        </div>
                                    </div>

                                    {complaint.attachments && complaint.attachments.length > 0 && (
                                        <>
                                            <Separator />
                                            <div>
                                                <h4 className="mb-3 font-medium">Foto Bukti ({complaint.attachments.length})</h4>
                                                <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
                                                    {complaint.attachments.map((attachment, index) => (
                                                        <div
                                                            key={index}
                                                            className="rounded-lg border border-gray-200 bg-white p-3 dark:border-gray-700 dark:bg-gray-900"
                                                        >
                                                            <div className="space-y-3">
                                                                <div className="relative h-32 w-full overflow-hidden rounded-lg bg-gray-100 dark:bg-gray-800">
                                                                    <a href={`/storage/${attachment.path}`} target="_blank" rel="noopener noreferrer">
                                                                        <img
                                                                            src={`/storage/${attachment.path}`}
                                                                            alt={attachment.original_name}
                                                                            className="h-full w-full object-cover transition-transform duration-200 hover:scale-105"
                                                                            onError={(e) => {
                                                                                console.error('Image failed to load:', `/storage/${attachment.path}`);
                                                                                e.currentTarget.src = '/images/placeholder-image.jpg';
                                                                            }}
                                                                        />
                                                                    </a>
                                                                </div>
                                                                <div className="flex items-start justify-between gap-2">
                                                                    <div className="flex min-w-0 flex-1 items-start gap-2">
                                                                        <Image className="mt-0.5 h-4 w-4 flex-shrink-0 text-green-600 dark:text-green-300" />
                                                                        <div className="min-w-0 flex-1">
                                                                            <div
                                                                                className="line-clamp-2 text-sm font-medium break-words"
                                                                                title={attachment.original_name}
                                                                            >
                                                                                {attachment.original_name}
                                                                            </div>
                                                                            <div className="text-xs text-gray-500 dark:text-gray-400">
                                                                                {formatFileSize(attachment.size)} •{' '}
                                                                                {attachment.mime_type.split('/')[1].toUpperCase()}
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                    <Button variant="ghost" size="sm" className="flex-shrink-0" asChild>
                                                                        <a
                                                                            href={`/storage/${attachment.path}`}
                                                                            target="_blank"
                                                                            rel="noopener noreferrer"
                                                                        >
                                                                            <Download className="h-4 w-4" />
                                                                        </a>
                                                                    </Button>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    ))}
                                                </div>
                                            </div>
                                        </>
                                    )}

                                    {complaint.admin_response && (
                                        <>
                                            <Separator />
                                            <div>
                                                <h4 className="mb-2 font-medium">Respon Admin</h4>
                                                <div className="rounded-lg bg-blue-50 p-4">
                                                    <p className="whitespace-pre-wrap text-gray-700 dark:text-gray-300">{complaint.admin_response}</p>
                                                    {complaint.responded_at && complaint.responded_by && (
                                                        <div className="mt-3 text-sm text-gray-500 dark:text-gray-400">
                                                            Direspon oleh {complaint.responded_by.name} pada{' '}
                                                            {new Date(complaint.responded_at).toLocaleDateString('id-ID', {
                                                                day: '2-digit',
                                                                month: 'long',
                                                                year: 'numeric',
                                                                hour: '2-digit',
                                                                minute: '2-digit',
                                                            })}
                                                        </div>
                                                    )}
                                                </div>
                                            </div>
                                        </>
                                    )}
                                </CardContent>
                            </Card>
                        )}

                        {/* Response Tab */}
                        {activeTab === 'response' && (
                            <div className="space-y-6">
                                {/* Status Update */}
                                <Card>
                                    <CardHeader>
                                        <CardTitle>Update Status & Prioritas</CardTitle>
                                        <CardDescription>Perbarui status dan prioritas pengaduan</CardDescription>
                                    </CardHeader>
                                    <CardContent>
                                        <form onSubmit={handleStatusUpdate} className="space-y-4">
                                            <div className="grid grid-cols-1 gap-4 md:grid-cols-3">
                                                <div>
                                                    <label className="mb-2 block text-sm font-medium">Status</label>
                                                    <Select value={data.status} onValueChange={(value) => setData('status', value)}>
                                                        <SelectTrigger>
                                                            <SelectValue />
                                                        </SelectTrigger>
                                                        <SelectContent>
                                                            {Object.entries(statuses).map(([key, label]) => (
                                                                <SelectItem key={key} value={key}>
                                                                    {label}
                                                                </SelectItem>
                                                            ))}
                                                        </SelectContent>
                                                    </Select>
                                                    {errors.status && <p className="mt-1 text-sm text-red-500">{errors.status}</p>}
                                                </div>

                                                <div>
                                                    <label className="mb-2 block text-sm font-medium">Prioritas</label>
                                                    <Select value={data.priority} onValueChange={(value) => setData('priority', value)}>
                                                        <SelectTrigger>
                                                            <SelectValue />
                                                        </SelectTrigger>
                                                        <SelectContent>
                                                            {Object.entries(priorities).map(([key, label]) => (
                                                                <SelectItem key={key} value={key}>
                                                                    {label}
                                                                </SelectItem>
                                                            ))}
                                                        </SelectContent>
                                                    </Select>
                                                    {errors.priority && <p className="mt-1 text-sm text-red-500">{errors.priority}</p>}
                                                </div>

                                                <div>
                                                    <label className="mb-2 block text-sm font-medium">Visibilitas</label>
                                                    <Select value={data.visibility} onValueChange={(value) => setData('visibility', value)}>
                                                        <SelectTrigger>
                                                            <SelectValue />
                                                        </SelectTrigger>
                                                        <SelectContent>
                                                            {Object.entries(visibilities).map(([key, label]) => (
                                                                <SelectItem key={key} value={key}>
                                                                    {label}
                                                                </SelectItem>
                                                            ))}
                                                        </SelectContent>
                                                    </Select>
                                                    {errors.visibility && <p className="mt-1 text-sm text-red-500">{errors.visibility}</p>}
                                                </div>
                                            </div>

                                            <Button type="submit" disabled={processing}>
                                                Update Status, Prioritas & Visibilitas
                                            </Button>
                                        </form>
                                    </CardContent>
                                </Card>

                                {/* Response */}
                                <Card>
                                    <CardHeader>
                                        <CardTitle>Kirim Respon</CardTitle>
                                        <CardDescription>Berikan respon kepada pengadu</CardDescription>
                                    </CardHeader>
                                    <CardContent>
                                        <form onSubmit={handleResponse} className="space-y-4">
                                            {/* Template Responses */}
                                            <div>
                                                <label className="mb-2 block text-sm font-medium">Template Respon</label>
                                                <div className="grid grid-cols-1 gap-2 md:grid-cols-2">
                                                    {Object.entries(responseTemplates).map(([key, template]) => (
                                                        <Button
                                                            key={key}
                                                            type="button"
                                                            variant="outline"
                                                            size="sm"
                                                            onClick={() => applyTemplate(key)}
                                                            className="h-auto justify-start p-3 text-left"
                                                        >
                                                            <div>
                                                                <div className="font-medium capitalize">{key.replace('_', ' ')}</div>
                                                                <div className="truncate text-xs text-gray-500">{template.substring(0, 50)}...</div>
                                                            </div>
                                                        </Button>
                                                    ))}
                                                </div>
                                            </div>

                                            <div>
                                                <label className="mb-2 block text-sm font-medium">Respon Admin</label>
                                                <Textarea
                                                    value={data.admin_response}
                                                    onChange={(e) => setData('admin_response', e.target.value)}
                                                    placeholder="Tulis respon untuk pengadu..."
                                                    rows={6}
                                                    className="w-full"
                                                />
                                                {errors.admin_response && <p className="mt-1 text-sm text-red-500">{errors.admin_response}</p>}
                                            </div>

                                            <Button type="submit" disabled={processing || !data.admin_response.trim()}>
                                                <Send className="mr-2 h-4 w-4" />
                                                Kirim Respon
                                            </Button>
                                        </form>
                                    </CardContent>
                                </Card>
                            </div>
                        )}
                    </div>

                    {/* Sidebar */}
                    <div className="space-y-6">
                        {/* Complainant Info */}
                        <Card>
                            <CardHeader>
                                <CardTitle className="flex items-center gap-2">
                                    <User className="h-5 w-5" />
                                    Informasi Pengadu
                                </CardTitle>
                            </CardHeader>
                            <CardContent className="space-y-4">
                                <div>
                                    <div className="font-medium">{complaint.name}</div>
                                    {complaint.email && (
                                        <div className="mt-1 flex items-center gap-2 text-sm text-gray-600">
                                            <Mail className="h-4 w-4 text-gray-600 dark:text-gray-400" />
                                            <a href={`mailto:${complaint.email}`} className="hover:text-blue-600 dark:hover:text-blue-400">
                                                {complaint.email}
                                            </a>
                                        </div>
                                    )}
                                    {complaint.phone && (
                                        <div className="mt-1 flex items-center gap-2 text-sm text-gray-600">
                                            <Phone className="h-4 w-4 text-gray-600 dark:text-gray-400" />
                                            <a href={`tel:${complaint.phone}`} className="hover:text-blue-600 dark:hover:text-blue-400">
                                                {complaint.phone}
                                            </a>
                                        </div>
                                    )}
                                </div>
                            </CardContent>
                        </Card>

                        {/* Timeline */}
                        <Card>
                            <CardHeader>
                                <CardTitle className="flex items-center gap-2">
                                    <Calendar className="h-5 w-5" />
                                    Timeline
                                </CardTitle>
                            </CardHeader>
                            <CardContent className="space-y-4">
                                <div className="flex items-start gap-3">
                                    <div className="mt-2 h-2 w-2 rounded-full bg-blue-500"></div>
                                    <div>
                                        <div className="font-medium">Pengaduan Diterima</div>
                                        <div className="text-sm text-gray-600">
                                            {new Date(complaint.created_at).toLocaleDateString('id-ID', {
                                                day: '2-digit',
                                                month: 'long',
                                                year: 'numeric',
                                                hour: '2-digit',
                                                minute: '2-digit',
                                            })}
                                        </div>
                                    </div>
                                </div>

                                {complaint.responded_at && (
                                    <div className="flex items-start gap-3">
                                        <div className="mt-2 h-2 w-2 rounded-full bg-green-500"></div>
                                        <div>
                                            <div className="font-medium">Respon Diberikan</div>
                                            <div className="text-sm text-gray-600">
                                                {new Date(complaint.responded_at).toLocaleDateString('id-ID', {
                                                    day: '2-digit',
                                                    month: 'long',
                                                    year: 'numeric',
                                                    hour: '2-digit',
                                                    minute: '2-digit',
                                                })}
                                            </div>
                                            {complaint.responded_by && (
                                                <div className="text-sm text-gray-500">oleh {complaint.responded_by.name}</div>
                                            )}
                                        </div>
                                    </div>
                                )}
                            </CardContent>
                        </Card>
                    </div>
                </div>
            </div>

            {/* Image Modal - Removed for simplification */}
        </AdminLayout>
    );
}
