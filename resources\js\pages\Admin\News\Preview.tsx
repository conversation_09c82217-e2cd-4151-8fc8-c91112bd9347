import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import AdminLayout from '@/layouts/AdminLayout';
import { getImageUrl } from '@/lib/image-utils';
import { type BreadcrumbItem } from '@/types';
import { Head, Link } from '@inertiajs/react';
import { ArrowLeft, Calendar, Edit, ExternalLink, Tag, User } from 'lucide-react';

interface News {
    id: number;
    title: string;
    slug: string;
    content: string;
    excerpt: string | null;
    featured_image: string | null;
    category: string;
    is_published: boolean;
    published_at: string | null;
    created_at: string;
    updated_at: string;
    meta_title: string | null;
    meta_description: string | null;
}

interface Props {
    news: News;
}

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'Dashboard',
        href: '/dashboard',
    },
    {
        title: 'Manajemen Berita',
        href: '/admin/news',
    },
    {
        title: 'Preview Berita',
        href: '#',
    },
];

export default function NewsPreview({ news }: Props) {
    return (
        <AdminLayout breadcrumbs={breadcrumbs}>
            <Head title={`Preview: ${news.title} - Admin Desa Lemah Duhur`} />

            <div className="flex h-full flex-1 flex-col gap-6 p-6">
                {/* Header */}
                <div className="flex items-center justify-between">
                    <div>
                        <h1 className="text-3xl font-bold text-gray-900 dark:text-gray-100">Preview Berita</h1>
                        <p className="text-gray-600 dark:text-gray-300">Pratinjau tampilan berita sebelum dipublikasikan</p>
                    </div>
                    <div className="flex items-center space-x-2">
                        {news.is_published && (
                            <Link href={route('news.show', news.slug)} target="_blank">
                                <Button variant="outline" className="flex items-center space-x-2">
                                    <ExternalLink className="h-4 w-4" />
                                    <span>Lihat di Website</span>
                                </Button>
                            </Link>
                        )}
                        <Link href={route('admin.news.edit', news.id)}>
                            <Button variant="outline" className="flex items-center space-x-2">
                                <Edit className="h-4 w-4" />
                                <span>Edit</span>
                            </Button>
                        </Link>
                        <Link href={route('admin.news.index')}>
                            <Button variant="outline" className="flex items-center space-x-2">
                                <ArrowLeft className="h-4 w-4" />
                                <span>Kembali</span>
                            </Button>
                        </Link>
                    </div>
                </div>

                <div className="grid grid-cols-1 gap-6 lg:grid-cols-4">
                    {/* Main Content */}
                    <div className="lg:col-span-3">
                        <Card>
                            <CardContent className="p-0">
                                {/* Featured Image */}
                                {news.featured_image && (
                                    <div className="aspect-video w-full overflow-hidden rounded-t-lg">
                                        <img
                                            src={getImageUrl(news.featured_image, 'original')}
                                            alt={news.title}
                                            className="h-full w-full object-cover"
                                        />
                                    </div>
                                )}

                                <div className="space-y-6 p-6">
                                    {/* Article Header */}
                                    <div className="space-y-4">
                                        <div className="flex items-center space-x-2">
                                            <Badge variant="outline">
                                                <Tag className="mr-1 h-3 w-3" />
                                                {news.category}
                                            </Badge>
                                            {news.is_published ? (
                                                <Badge className="bg-green-100 text-green-800">Dipublikasikan</Badge>
                                            ) : (
                                                <Badge variant="secondary">Draft</Badge>
                                            )}
                                        </div>

                                        <h1 className="text-3xl leading-tight font-bold text-gray-900 dark:text-gray-100">{news.title}</h1>

                                        <div className="flex items-center space-x-4 text-sm text-gray-500 dark:text-gray-400">
                                            <div className="flex items-center space-x-1">
                                                <Calendar className="h-4 w-4" />
                                                <span>
                                                    {news.published_at
                                                        ? new Date(news.published_at).toLocaleDateString('id-ID', {
                                                              weekday: 'long',
                                                              year: 'numeric',
                                                              month: 'long',
                                                              day: 'numeric',
                                                          })
                                                        : 'Belum dipublikasikan'}
                                                </span>
                                            </div>
                                            <div className="flex items-center space-x-1">
                                                <User className="h-4 w-4" />
                                                <span>Admin Desa</span>
                                            </div>
                                        </div>

                                        {news.excerpt && (
                                            <div className="rounded-r-lg border-l-4 border-blue-500 bg-blue-50 p-4 pl-4 text-lg leading-relaxed text-gray-600 dark:bg-blue-900 dark:text-gray-300">
                                                {news.excerpt}
                                            </div>
                                        )}
                                    </div>

                                    {/* Article Content */}
                                    <div className="prose prose-lg max-w-none">
                                        <div
                                            className="leading-relaxed whitespace-pre-wrap text-gray-800 dark:text-gray-200"
                                            style={{ lineHeight: '1.8' }}
                                        >
                                            {news.content}
                                        </div>
                                    </div>

                                    {/* Article Footer */}
                                    <div className="border-t pt-6">
                                        <div className="text-sm text-gray-500 dark:text-gray-400">
                                            <p>
                                                Dibuat:{' '}
                                                {new Date(news.created_at).toLocaleDateString('id-ID', {
                                                    year: 'numeric',
                                                    month: 'long',
                                                    day: 'numeric',
                                                    hour: '2-digit',
                                                    minute: '2-digit',
                                                })}
                                            </p>
                                            {news.updated_at !== news.created_at && (
                                                <p>
                                                    Diperbarui:{' '}
                                                    {new Date(news.updated_at).toLocaleDateString('id-ID', {
                                                        year: 'numeric',
                                                        month: 'long',
                                                        day: 'numeric',
                                                        hour: '2-digit',
                                                        minute: '2-digit',
                                                    })}
                                                </p>
                                            )}
                                        </div>
                                    </div>
                                </div>
                            </CardContent>
                        </Card>
                    </div>

                    {/* Sidebar */}
                    <div className="space-y-6">
                        {/* Article Info */}
                        <Card>
                            <CardHeader>
                                <CardTitle>Informasi Artikel</CardTitle>
                            </CardHeader>
                            <CardContent className="space-y-4">
                                <div className="space-y-2">
                                    <div className="text-sm font-medium text-gray-700 dark:text-gray-300">Status</div>
                                    <div>
                                        {news.is_published ? (
                                            <Badge className="bg-green-100 text-green-800">Dipublikasikan</Badge>
                                        ) : (
                                            <Badge variant="secondary">Draft</Badge>
                                        )}
                                    </div>
                                </div>

                                <div className="space-y-2">
                                    <div className="text-sm font-medium text-gray-700 dark:text-gray-300">Kategori</div>
                                    <Badge variant="outline">{news.category}</Badge>
                                </div>

                                <div className="space-y-2">
                                    <div className="text-sm font-medium text-gray-700">Slug URL</div>
                                    <div className="rounded bg-gray-50 p-2 font-mono text-sm text-gray-600 dark:bg-gray-900 dark:text-gray-300">
                                        /berita/{news.slug}
                                    </div>
                                </div>

                                <div className="space-y-2">
                                    <div className="text-sm font-medium text-gray-700 dark:text-gray-300">Jumlah Karakter</div>
                                    <div className="text-sm text-gray-600 dark:text-gray-300">{news.content.length.toLocaleString()} karakter</div>
                                </div>

                                <div className="space-y-2">
                                    <div className="text-sm font-medium text-gray-700 dark:text-gray-300">Estimasi Waktu Baca</div>
                                    <div className="text-sm text-gray-600 dark:text-gray-300">
                                        {Math.ceil(news.content.split(' ').length / 200)} menit
                                    </div>
                                </div>
                            </CardContent>
                        </Card>

                        {/* SEO Info */}
                        <Card>
                            <CardHeader>
                                <CardTitle>Informasi SEO</CardTitle>
                            </CardHeader>
                            <CardContent className="space-y-4">
                                <div className="space-y-2">
                                    <div className="text-sm font-medium text-gray-700 dark:text-gray-300">Meta Title</div>
                                    <div className="text-sm text-gray-600 dark:text-gray-300">{news.meta_title || news.title}</div>
                                </div>

                                <div className="space-y-2">
                                    <div className="text-sm font-medium text-gray-700 dark:text-gray-300">Meta Description</div>
                                    <div className="text-sm text-gray-600 dark:text-gray-300">
                                        {news.meta_description || news.excerpt || news.content.substring(0, 150) + '...'}
                                    </div>
                                </div>

                                <div className="space-y-2">
                                    <div className="text-sm font-medium text-gray-700 dark:text-gray-300">URL Lengkap</div>
                                    <div className="rounded bg-gray-50 p-2 font-mono text-sm break-all text-gray-600 dark:bg-gray-900 dark:text-gray-300">
                                        {window.location.origin}/berita/{news.slug}
                                    </div>
                                </div>
                            </CardContent>
                        </Card>

                        {/* Actions */}
                        <Card>
                            <CardHeader>
                                <CardTitle>Aksi</CardTitle>
                            </CardHeader>
                            <CardContent className="space-y-2">
                                <Link href={route('admin.news.edit', news.id)} className="block">
                                    <Button variant="outline" className="w-full justify-start">
                                        <Edit className="mr-2 h-4 w-4" />
                                        Edit Berita
                                    </Button>
                                </Link>

                                {news.is_published && (
                                    <Link href={route('news.show', news.slug)} target="_blank" className="block">
                                        <Button variant="outline" className="w-full justify-start">
                                            <ExternalLink className="mr-2 h-4 w-4" />
                                            Lihat di Website
                                        </Button>
                                    </Link>
                                )}
                            </CardContent>
                        </Card>
                    </div>
                </div>
            </div>
        </AdminLayout>
    );
}
