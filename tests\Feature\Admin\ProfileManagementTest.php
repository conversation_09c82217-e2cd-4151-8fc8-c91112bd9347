<?php

namespace Tests\Feature\Admin;

use App\Models\Profile;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;
use Tests\TestCase;

class ProfileManagementTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();

        // Create an admin user and authenticate
        $user = User::factory()->create(['role' => 'admin']);
        $this->actingAs($user);

        // Fake storage for testing
        Storage::fake('public');
    }

    public function test_can_view_profile_index()
    {
        $response = $this->get(route('admin.profiles.index'));

        $response->assertStatus(200);
        $response->assertInertia(fn ($page) => $page->component('Admin/Profiles/Index')
        );
    }

    public function test_can_view_profile_create_form()
    {
        $response = $this->get(route('admin.profiles.create'));

        $response->assertStatus(200);
        $response->assertInertia(fn ($page) => $page->component('Admin/Profiles/Create')
            ->has('sections')
        );
    }

    public function test_can_create_profile_content()
    {
        $profileData = [
            'section' => 'history',
            'title' => 'Sejarah Desa Lemah Duhur',
            'content' => 'Desa Lemah Duhur didirikan pada tahun 1910-1920...',
            'order' => 1,
        ];

        $response = $this->post(route('admin.profiles.store'), $profileData);

        $response->assertRedirect(route('admin.profiles.index'));
        $response->assertSessionHas('success');

        $this->assertDatabaseHas('profiles', [
            'section' => 'history',
            'title' => 'Sejarah Desa Lemah Duhur',
            'order' => 1,
        ]);
    }

    public function test_can_create_profile_with_image()
    {
        $image = UploadedFile::fake()->image('test-image.jpg');

        $profileData = [
            'section' => 'organization',
            'title' => 'Kepala Desa',
            'content' => 'Informasi kepala desa...',
            'image' => $image,
            'order' => 1,
        ];

        $response = $this->post(route('admin.profiles.store'), $profileData);

        $response->assertRedirect(route('admin.profiles.index'));

        $profile = Profile::where('title', 'Kepala Desa')->first();
        $this->assertNotNull($profile);
        $this->assertNotNull($profile->image);

        Storage::disk('public')->assertExists($profile->image);
    }

    public function test_can_view_profile_details()
    {
        $profile = Profile::factory()->create([
            'section' => 'history',
            'title' => 'Test Profile',
            'content' => 'Test content',
        ]);

        $response = $this->get(route('admin.profiles.show', $profile));

        $response->assertStatus(200);
        $response->assertInertia(fn ($page) => $page->component('Admin/Profiles/Show')
            ->has('profile')
        );
    }

    public function test_can_edit_profile()
    {
        $profile = Profile::factory()->create([
            'section' => 'history',
            'title' => 'Original Title',
            'content' => 'Original content',
        ]);

        $updateData = [
            'section' => 'history',
            'title' => 'Updated Title',
            'content' => 'Updated content',
            'order' => $profile->order,
        ];

        $response = $this->put(route('admin.profiles.update', $profile), $updateData);

        $response->assertRedirect(route('admin.profiles.index'));

        $profile->refresh();
        $this->assertEquals('Updated Title', $profile->title);
        $this->assertEquals('Updated content', $profile->content);
    }

    public function test_can_delete_profile()
    {
        $profile = Profile::factory()->create();

        $response = $this->delete(route('admin.profiles.destroy', $profile));

        $response->assertRedirect(route('admin.profiles.index'));
        $this->assertDatabaseMissing('profiles', ['id' => $profile->id]);
    }

    public function test_can_view_section_management()
    {
        Profile::factory()->count(3)->create(['section' => 'history']);

        $response = $this->get(route('admin.profiles.section', 'history'));

        $response->assertStatus(200);
        $response->assertInertia(fn ($page) => $page->component('Admin/Profiles/ManageSection')
            ->where('section', 'history')
            ->where('sectionTitle', 'Sejarah Desa')
            ->has('profiles', 3)
        );
    }

    public function test_validates_required_fields()
    {
        $response = $this->post(route('admin.profiles.store'), []);

        $response->assertSessionHasErrors(['section', 'title', 'content']);
    }

    public function test_validates_section_values()
    {
        $profileData = [
            'section' => 'invalid_section',
            'title' => 'Test Title',
            'content' => 'Test content',
        ];

        $response = $this->post(route('admin.profiles.store'), $profileData);

        $response->assertSessionHasErrors(['section']);
    }
}
