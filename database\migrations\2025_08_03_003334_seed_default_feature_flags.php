<?php

use Database\Seeders\FeatureFlagSeeder;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\Artisan;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Seed default feature flags for the village website
        Artisan::call('db:seed', [
            '--class' => FeatureFlagSeeder::class,
            '--force' => true,
        ]);
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Remove all feature flag settings
        \App\Models\Setting::where('key', 'like', 'feature_flags.%')->delete();
    }
};
