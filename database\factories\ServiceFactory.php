<?php

namespace Database\Factories;

use App\Models\Service;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Service>
 */
class ServiceFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = Service::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $services = [
            'Surat Keterangan Domisili',
            'Surat Keterangan Tidak Mampu',
            'Surat Pengantar KTP',
            'Surat Pengantar KK',
            'Surat Keterangan Usaha',
            'Surat Keterangan Kelahiran',
            'Surat Keterangan Kematian',
            'Surat Pengantar Nikah',
            'Surat Keterangan Penghasilan',
            'Surat Keterangan Belum Menikah',
        ];

        return [
            'name' => $this->faker->randomElement($services),
            'description' => $this->faker->paragraphs(2, true),
            'requirements' => [
                'KTP asli dan fotocopy',
                'KK asli dan fotocopy',
                'Surat pengantar RT/RW',
                $this->faker->sentence(4),
            ],
            'procedure' => [
                'Datang ke kantor desa dengan membawa persyaratan',
                'Mengisi formulir permohonan',
                'Menunggu proses verifikasi',
                'Mengambil surat yang sudah jadi',
            ],
            'cost' => $this->faker->randomElement([0, 5000, 10000, 15000, null]),
            'processing_time' => $this->faker->randomElement(['1 hari', '2-3 hari', '1 minggu', 'Selesai hari itu']),
            'contact_info' => [
                'phone' => $this->faker->phoneNumber(),
                'email' => $this->faker->email(),
                'person' => $this->faker->name(),
            ],
            'is_active' => $this->faker->boolean(90),
        ];
    }

    /**
     * Indicate that the service is active.
     */
    public function active(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_active' => true,
        ]);
    }

    /**
     * Indicate that the service is inactive.
     */
    public function inactive(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_active' => false,
        ]);
    }

    /**
     * Create a free service.
     */
    public function free(): static
    {
        return $this->state(fn (array $attributes) => [
            'cost' => 0,
        ]);
    }

    /**
     * Create a paid service.
     */
    public function paid(?int $cost = null): static
    {
        return $this->state(fn (array $attributes) => [
            'cost' => $cost ?? $this->faker->numberBetween(5000, 50000),
        ]);
    }

    /**
     * Create a service with custom requirements.
     */
    public function withRequirements(array $requirements): static
    {
        return $this->state(fn (array $attributes) => [
            'requirements' => $requirements,
        ]);
    }

    /**
     * Create a service with custom procedure.
     */
    public function withProcedure(array $procedure): static
    {
        return $this->state(fn (array $attributes) => [
            'procedure' => $procedure,
        ]);
    }
}
