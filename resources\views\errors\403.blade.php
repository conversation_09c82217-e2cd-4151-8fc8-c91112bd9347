<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Halaman akses di<PERSON>lak - Anda tidak memiliki izin untuk mengakses halaman ini">
    <meta name="robots" content="noindex, nofollow">
    <title>403 - <PERSON><PERSON><PERSON> | {{ config('app.name') }}</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #374151;
            line-height: 1.6;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 1rem;
            position: relative;
            overflow-x: hidden;
        }

        body::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="%23ffffff" opacity="0.05"/><circle cx="75" cy="75" r="1" fill="%23ffffff" opacity="0.05"/><circle cx="50" cy="10" r="0.5" fill="%23ffffff" opacity="0.03"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
            pointer-events: none;
        }
        
        .container {
            max-width: 32rem;
            width: 100%;
            text-align: center;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            padding: 3rem 2rem 2rem;
            border-radius: 1rem;
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
            border: 1px solid rgba(255, 255, 255, 0.2);
            animation: slideUp 0.6s ease-out;
            position: relative;
        }

        @keyframes slideUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes pulse {
            0%, 100% {
                transform: scale(1);
            }
            50% {
                transform: scale(1.05);
            }
        }

        @keyframes shake {
            0%, 100% { transform: translateX(0); }
            10%, 30%, 50%, 70%, 90% { transform: translateX(-5px); }
            20%, 40%, 60%, 80% { transform: translateX(5px); }
        }
        
        .icon-container {
            position: relative;
            display: inline-block;
            margin-bottom: 1.5rem;
        }

        .icon {
            width: 5rem;
            height: 5rem;
            color: #ef4444;
            animation: pulse 2s infinite;
            filter: drop-shadow(0 4px 6px rgba(239, 68, 68, 0.2));
        }
        
        .error-code {
            font-size: 3rem;
            font-weight: 800;
            background: linear-gradient(135deg, #ef4444, #dc2626);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 0.5rem;
            animation: shake 0.5s ease-in-out 0.5s;
        }
        
        .error-title {
            font-size: 1.5rem;
            font-weight: 700;
            color: #111827;
            margin-bottom: 1rem;
            opacity: 0;
            animation: fadeIn 0.6s ease-out 0.3s forwards;
        }

        @keyframes fadeIn {
            to {
                opacity: 1;
            }
        }
        
        .error-message {
            color: #6b7280;
            margin-bottom: 2rem;
            font-size: 1rem;
            opacity: 0;
            animation: fadeIn 0.6s ease-out 0.6s forwards;
        }
        
        .buttons {
            display: flex;
            flex-direction: column;
            gap: 1rem;
            margin-bottom: 2rem;
            opacity: 0;
            animation: fadeIn 0.6s ease-out 0.9s forwards;
        }
        
        .btn {
            padding: 0.875rem 2rem;
            border-radius: 0.5rem;
            text-decoration: none;
            font-weight: 600;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            border: none;
            cursor: pointer;
            font-size: 0.9rem;
            position: relative;
            overflow: hidden;
            transform: translateY(0);
        }

        .btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s;
        }

        .btn:hover::before {
            left: 100%;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #3b82f6, #2563eb);
            color: white;
            box-shadow: 0 4px 14px 0 rgba(59, 130, 246, 0.3);
        }
        
        .btn-primary:hover {
            background: linear-gradient(135deg, #2563eb, #1d4ed8);
            transform: translateY(-2px);
            box-shadow: 0 8px 25px 0 rgba(59, 130, 246, 0.4);
        }
        
        .btn-secondary {
            background: rgba(255, 255, 255, 0.8);
            color: #374151;
            border: 2px solid #e5e7eb;
            backdrop-filter: blur(10px);
        }
        
        .btn-secondary:hover {
            background: rgba(249, 250, 251, 0.9);
            border-color: #d1d5db;
            transform: translateY(-2px);
            box-shadow: 0 8px 25px 0 rgba(0, 0, 0, 0.1);
        }
        
        .contact-info {
            font-size: 0.875rem;
            color: #9ca3af;
            padding: 1rem;
            background: rgba(249, 250, 251, 0.5);
            border-radius: 0.5rem;
            border-left: 4px solid #3b82f6;
            opacity: 0;
            animation: fadeIn 0.6s ease-out 1.2s forwards;
        }

        .contact-info::before {
            content: '💡';
            margin-right: 0.5rem;
        }

        .loading-dots {
            display: inline-block;
            margin-left: 0.5rem;
        }

        .loading-dots span {
            display: inline-block;
            width: 4px;
            height: 4px;
            border-radius: 50%;
            background: currentColor;
            margin: 0 1px;
            animation: loading 1.4s infinite ease-in-out;
        }

        .loading-dots span:nth-child(1) { animation-delay: -0.32s; }
        .loading-dots span:nth-child(2) { animation-delay: -0.16s; }

        @keyframes loading {
            0%, 80%, 100% {
                transform: scale(0);
                opacity: 0.5;
            }
            40% {
                transform: scale(1);
                opacity: 1;
            }
        }
        
        @media (min-width: 640px) {
            .buttons {
                flex-direction: row;
                justify-content: center;
            }

            .container {
                padding: 3rem;
            }

            .error-code {
                font-size: 4rem;
            }
        }

        @media (prefers-reduced-motion: reduce) {
            *, *::before, *::after {
                animation-duration: 0.01ms !important;
                animation-iteration-count: 1 !important;
                transition-duration: 0.01ms !important;
            }
        }

        /* Focus styles for accessibility */
        .btn:focus {
            outline: 2px solid #3b82f6;
            outline-offset: 2px;
        }

        /* High contrast mode support */
        @media (prefers-contrast: high) {
            .container {
                background: white;
                border: 2px solid black;
            }
            
            .btn-primary {
                background: #000;
                border: 2px solid #000;
            }
            
            .btn-secondary {
                background: white;
                border: 2px solid #000;
                color: #000;
            }
        }
    </style>
</head>
<body>
    <div class="container" role="main" aria-labelledby="error-title">
        <div class="icon-container">
            <svg class="icon" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18.364 18.364A9 9 0 005.636 5.636m12.728 12.728L5.636 5.636m12.728 12.728L5.636 5.636"/>
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
            </svg>
        </div>
        
        <h1 class="error-code" aria-label="Error 403">403</h1>
        <h2 id="error-title" class="error-title">Akses Ditolak</h2>
        <p class="error-message">
            Maaf, Anda tidak memiliki izin untuk mengakses halaman ini. Silakan hubungi administrator jika Anda merasa ini adalah kesalahan.
        </p>
        
        <div class="buttons" role="group" aria-label="Navigasi">
            <button onclick="history.back()" class="btn btn-secondary" aria-label="Kembali ke halaman sebelumnya">
                ← Kembali
            </button>
            <a href="{{ route('home') }}" class="btn btn-primary" aria-label="Pergi ke halaman beranda">
                🏠 Ke Beranda
            </a>
        </div>
        
        <div class="contact-info" role="complementary">
            <p>
                Jika Anda merasa ini adalah kesalahan, silakan hubungi administrator desa
                <span class="loading-dots" aria-hidden="true">
                    <span></span>
                    <span></span>
                    <span></span>
                </span>
            </p>
        </div>
    </div>

    <script>
        // Add keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                history.back();
            } else if (e.key === 'Enter' && e.target.tagName === 'BUTTON') {
                e.target.click();
            }
        });

        // Add auto-redirect countdown (optional)
        let countdown = 30;
        const redirectTimer = setInterval(() => {
            countdown--;
            if (countdown <= 0) {
                clearInterval(redirectTimer);
                window.location.href = "{{ route('home') }}";
            }
        }, 1000);

        // Clear timer if user interacts
        document.addEventListener('click', () => clearInterval(redirectTimer));
        document.addEventListener('keydown', () => clearInterval(redirectTimer));
    </script>
</body>
</html>