<?php

namespace App\Exceptions;

use Illuminate\Auth\Access\AuthorizationException;
use Illuminate\Auth\AuthenticationException;
use Illuminate\Foundation\Exceptions\Handler as ExceptionHandler;
use Illuminate\Http\Request;
use Illuminate\Validation\ValidationException;
use Inertia\Inertia;
use Symfony\Component\HttpKernel\Exception\HttpException;
use Throwable;

class Handler extends ExceptionHandler
{
    /**
     * The list of the inputs that are never flashed to the session on validation exceptions.
     *
     * @var array<int, string>
     */
    protected $dontFlash = [
        'current_password',
        'password',
        'password_confirmation',
    ];

    /**
     * Register the exception handling callbacks for the application.
     */
    public function register(): void
    {
        $this->reportable(function (Throwable $e) {
            // Log error untuk monitoring
            if (app()->environment('production')) {
                \Log::error('Application Error', [
                    'message' => $e->getMessage(),
                    'file' => $e->getFile(),
                    'line' => $e->getLine(),
                    'trace' => $e->getTraceAsString(),
                    'url' => request()->fullUrl(),
                    'user_id' => auth()->id(),
                    'ip' => request()->ip(),
                    'user_agent' => request()->userAgent(),
                ]);
            }
        });
    }

    /**
     * Render an exception into an HTTP response.
     */
    public function render($request, Throwable $e)
    {
        // Handle validation exceptions
        if ($e instanceof ValidationException) {
            return $this->handleValidationException($e, $request);
        }

        // Handle authentication exceptions
        if ($e instanceof AuthenticationException) {
            return $this->handleAuthenticationException($e, $request);
        }

        // Handle authorization exceptions
        if ($e instanceof AuthorizationException) {
            return $this->handleAuthorizationException($e, $request);
        }

        // Handle HTTP exceptions
        if ($e instanceof HttpException) {
            return $this->handleHttpException($e, $request);
        }

        // Handle general exceptions
        return $this->handleGeneralException($e, $request);
    }

    /**
     * Handle validation exceptions
     */
    protected function handleValidationException(ValidationException $e, Request $request)
    {
        if ($request->inertia() || (! $request->is('api/*') && ! $request->wantsJson())) {
            return back()->withErrors($e->errors())->withInput();
        }

        if ($request->expectsJson() || $request->is('api/*')) {
            return response()->json([
                'message' => 'Data yang Anda masukkan tidak valid.',
                'errors' => $e->errors(),
                'status' => 422,
            ], 422);
        }

        return back()->withErrors($e->errors())->withInput();
    }

    /**
     * Handle authentication exceptions
     */
    protected function handleAuthenticationException(AuthenticationException $e, Request $request)
    {
        if ($request->inertia()) {
            return Inertia::render('auth/login', [
                'message' => 'Sesi Anda telah berakhir. Silakan login kembali.',
            ])->toResponse($request)->setStatusCode(401);
        }

        if ($request->expectsJson()) {
            return response()->json([
                'message' => 'Sesi Anda telah berakhir. Silakan login kembali.',
                'status' => 401,
            ], 401);
        }

        return redirect()->route('login')->with('message', 'Sesi Anda telah berakhir. Silakan login kembali.');
    }

    /**
     * Handle authorization exceptions
     */
    protected function handleAuthorizationException(AuthorizationException $e, Request $request)
    {
        if ($request->inertia()) {
            return Inertia::render('Errors/403')
                ->toResponse($request)
                ->setStatusCode(403);
        }

        if ($request->expectsJson()) {
            return response()->json([
                'message' => 'Anda tidak memiliki izin untuk melakukan tindakan ini.',
                'status' => 403,
            ], 403);
        }

        return redirect()->route('errors.403');
    }

    /**
     * Handle HTTP exceptions
     */
    protected function handleHttpException(HttpException $e, Request $request)
    {
        $statusCode = $e->getStatusCode();

        // Always prioritize Inertia over JSON for web requests
        if ($request->inertia() || (! $request->is('api/*') && ! $request->wantsJson())) {
            return match ($statusCode) {
                403 => Inertia::render('Errors/403')
                    ->toResponse($request)
                    ->setStatusCode(403),
                404 => Inertia::render('Errors/404')
                    ->toResponse($request)
                    ->setStatusCode(404),
                500 => Inertia::render('Errors/500')
                    ->toResponse($request)
                    ->setStatusCode(500),
                default => Inertia::render('Errors/500')
                    ->toResponse($request)
                    ->setStatusCode($statusCode >= 400 ? $statusCode : 500),
            };
        }

        if ($request->expectsJson() || $request->is('api/*')) {
            return response()->json([
                'message' => $this->getErrorMessage($statusCode),
                'status' => $statusCode,
                'error' => app()->environment('local') ? $e->getMessage() : null,
            ], $statusCode);
        }

        // Redirect ke error pages untuk regular requests
        return match ($statusCode) {
            403 => redirect()->route('errors.403'),
            404 => redirect()->route('errors.404'),
            500 => redirect()->route('errors.500'),
            default => redirect()->route('errors.500'),
        };
    }

    /**
     * Handle general exceptions
     */
    protected function handleGeneralException(Throwable $e, Request $request)
    {
        $statusCode = 500;

        // Always prioritize Inertia over JSON for web requests
        if ($request->inertia() || (! $request->is('api/*') && ! $request->wantsJson())) {
            return Inertia::render('Errors/500', [
                'error' => app()->environment('local') ? $e->getMessage() : null,
            ])->toResponse($request)->setStatusCode(500);
        }

        if ($request->expectsJson() || $request->is('api/*')) {
            return response()->json([
                'message' => $this->getErrorMessage($statusCode),
                'status' => $statusCode,
                'error' => app()->environment('local') ? $e->getMessage() : null,
            ], $statusCode);
        }

        return redirect()->route('errors.500');
    }

    /**
     * Get error message in Indonesian
     */
    protected function getErrorMessage(int $statusCode): string
    {
        return match ($statusCode) {
            400 => 'Permintaan tidak valid. Silakan periksa data yang Anda masukkan.',
            401 => 'Sesi Anda telah berakhir. Silakan login kembali.',
            403 => 'Anda tidak memiliki izin untuk melakukan tindakan ini.',
            404 => 'Data yang diminta tidak ditemukan.',
            422 => 'Data yang Anda masukkan tidak valid.',
            429 => 'Terlalu banyak permintaan. Silakan coba lagi dalam beberapa saat.',
            500 => 'Terjadi kesalahan pada server. Silakan coba lagi atau hubungi administrator.',
            503 => 'Layanan sedang dalam pemeliharaan. Silakan coba lagi nanti.',
            default => 'Terjadi kesalahan yang tidak terduga.',
        };
    }

    /**
     * Convert an authentication exception into a response.
     */
    protected function unauthenticated($request, AuthenticationException $exception)
    {
        return $this->handleAuthenticationException($exception, $request);
    }
}
