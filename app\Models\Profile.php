<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Profile extends Model
{
    use HasFactory;

    protected $fillable = [
        'section', // 'history', 'vision_mission', 'demographics', etc.
        'title',
        'content',
        'image',
        'order',
    ];

    // Scopes
    #[\Illuminate\Database\Eloquent\Attributes\Scope]
    protected function bySection($query, $section)
    {
        return $query->where('section', $section);
    }

    #[\Illuminate\Database\Eloquent\Attributes\Scope]
    protected function ordered($query)
    {
        return $query->orderBy('order');
    }

    // Static methods for common sections
    public static function getHistory()
    {
        return static::where('section', 'history')->orderBy('order')->get();
    }

    public static function getVisionMission()
    {
        return static::where('section', 'vision_mission')->orderBy('order')->get();
    }

    public static function getDemographics()
    {
        return static::where('section', 'demographics')->orderBy('order')->get();
    }

    public static function getOrganization()
    {
        return static::where('section', 'organization')->orderBy('order')->get();
    }

    public static function getGeography()
    {
        return static::where('section', 'geography')->orderBy('order')->get();
    }

    // Image helpers
    public function getImageUrl($size = 'medium'): ?string
    {
        if (! $this->image || ! is_array($this->image)) {
            return null;
        }

        return $this->image[$size]['url'] ?? $this->image['medium']['url'] ?? $this->image['original']['url'] ?? null;
    }

    public function getImageSrcSet(): string
    {
        if (! $this->image || ! is_array($this->image)) {
            return '';
        }

        $srcSet = [];

        if (isset($this->image['thumbnail']['url'])) {
            $srcSet[] = $this->image['thumbnail']['url'].' 300w';
        }

        if (isset($this->image['medium']['url'])) {
            $srcSet[] = $this->image['medium']['url'].' 800w';
        }

        if (isset($this->image['original']['url'])) {
            $srcSet[] = $this->image['original']['url'].' 1200w';
        }

        return implode(', ', $srcSet);
    }

    protected function casts(): array
    {
        return [
            'order' => 'integer',
            'image' => 'array',
        ];
    }
}
