<?php

namespace App\Services;

use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use Intervention\Image\Drivers\Gd\Driver;
use Intervention\Image\ImageManager;

class ImageOptimizationService
{
    protected ImageManager $imageManager;

    public function __construct()
    {
        $this->imageManager = new ImageManager(new Driver);
    }

    /**
     * Upload dan optimasi gambar
     */
    public function uploadAndOptimize(
        UploadedFile $file,
        string $directory = 'images',
        array $sizes = ['original' => null, 'medium' => 800, 'thumbnail' => 300]
    ): array {
        $this->validateImage($file);

        $filename = $this->generateFilename($file);
        $results = [];

        foreach ($sizes as $sizeName => $maxWidth) {
            $optimizedImage = $this->processImage($file, $maxWidth);
            $path = $this->saveImage($optimizedImage, $directory, $filename, $sizeName);

            $results[$sizeName] = [
                'path' => $path,
                'url' => Storage::disk('public')->url($path),
                'size' => Storage::disk('public')->size($path),
            ];
        }

        return $results;
    }

    /**
     * Validasi file gambar
     */
    protected function validateImage(UploadedFile $file): void
    {
        $allowedMimes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
        $maxSize = 10 * 1024 * 1024; // 10MB

        throw_unless(in_array($file->getMimeType(), $allowedMimes), new \InvalidArgumentException('Format file tidak didukung. Gunakan JPEG, PNG, GIF, atau WebP.'));

        throw_if($file->getSize() > $maxSize, new \InvalidArgumentException('Ukuran file terlalu besar. Maksimal 10MB.'));
    }

    /**
     * Generate nama file unik
     */
    protected function generateFilename(UploadedFile $file): string
    {
        $extension = $file->getClientOriginalExtension();
        $name = pathinfo($file->getClientOriginalName(), PATHINFO_FILENAME);
        $slug = Str::slug($name);

        return $slug.'_'.time().'_'.Str::random(8).'.'.$extension;
    }

    /**
     * Proses dan optimasi gambar
     */
    protected function processImage(UploadedFile $file, ?int $maxWidth = null): \Intervention\Image\Interfaces\ImageInterface
    {
        $image = $this->imageManager->read($file->getPathname());

        // Resize jika diperlukan
        if ($maxWidth && $image->width() > $maxWidth) {
            $image->scale(width: $maxWidth);
        }

        // Optimasi kualitas berdasarkan format
        $mimeType = $file->getMimeType();

        if ($mimeType === 'image/jpeg') {
            $image->toJpeg(quality: 85);
        } elseif ($mimeType === 'image/png') {
            $image->toPng();
        } elseif ($mimeType === 'image/webp') {
            $image->toWebp(quality: 85);
        }

        return $image;
    }

    /**
     * Simpan gambar ke storage
     */
    protected function saveImage(
        \Intervention\Image\Interfaces\ImageInterface $image,
        string $directory,
        string $filename,
        string $sizeName
    ): string {
        $path = $directory.'/'.$sizeName.'/'.$filename;

        // Konversi ke WebP untuk efisiensi
        $webpPath = $this->convertToWebP($path);
        Storage::disk('public')->put($webpPath, $image->toWebp(quality: 85));

        // Simpan juga format asli sebagai fallback
        Storage::disk('public')->put($path, $image->encode());

        return $webpPath;
    }

    /**
     * Konversi path ke format WebP
     */
    protected function convertToWebP(string $path): string
    {
        $pathInfo = pathinfo($path);

        return $pathInfo['dirname'].'/'.$pathInfo['filename'].'.webp';
    }

    /**
     * Hapus gambar dan semua variannya
     */
    public function deleteImage(string $imagePath): bool
    {
        $pathInfo = pathinfo($imagePath);
        $directory = $pathInfo['dirname'];
        $filename = $pathInfo['filename'];

        $sizes = ['original', 'medium', 'thumbnail'];
        $extensions = ['webp', 'jpg', 'jpeg', 'png', 'gif'];

        foreach ($sizes as $size) {
            foreach ($extensions as $ext) {
                $path = $directory.'/'.$size.'/'.$filename.'.'.$ext;
                if (Storage::disk('public')->exists($path)) {
                    Storage::disk('public')->delete($path);
                }
            }
        }

        return true;
    }

    /**
     * Generate responsive image srcset
     */
    public function generateSrcSet(array $imagePaths): string
    {
        $srcSet = [];

        if (isset($imagePaths['thumbnail'])) {
            $srcSet[] = $imagePaths['thumbnail']['url'].' 300w';
        }

        if (isset($imagePaths['medium'])) {
            $srcSet[] = $imagePaths['medium']['url'].' 800w';
        }

        if (isset($imagePaths['original'])) {
            $srcSet[] = $imagePaths['original']['url'].' 1200w';
        }

        return implode(', ', $srcSet);
    }

    /**
     * Buat placeholder blur untuk lazy loading
     */
    public function generateBlurPlaceholder(string $imagePath): string
    {
        if (! Storage::disk('public')->exists($imagePath)) {
            return '';
        }

        $image = $this->imageManager->read(Storage::disk('public')->path($imagePath));

        // Resize ke ukuran sangat kecil dan blur
        $placeholder = $image->scale(width: 20)->blur(10);

        // Convert ke base64
        return 'data:image/jpeg;base64,'.base64_encode($placeholder->toJpeg(quality: 50));
    }
}
