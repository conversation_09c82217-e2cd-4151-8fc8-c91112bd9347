<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class ImageUploadRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'image' => [
                'required',
                'image',
                'mimes:jpeg,png,jpg,gif,webp',
                'max:10240', // 10MB
            ],
            'directory' => 'sometimes|string|max:255',
            'sizes' => 'sometimes|array',
            'sizes.*.name' => 'required_with:sizes|string',
            'sizes.*.width' => 'required_with:sizes|integer|min:1|max:2000',
        ];
    }

    /**
     * Get custom error messages.
     */
    public function messages(): array
    {
        return [
            'image.required' => 'Gambar wajib diupload.',
            'image.image' => 'File harus berupa gambar.',
            'image.mimes' => 'Format gambar harus JPEG, PNG, JPG, GIF, atau WebP.',
            'image.max' => 'Ukuran gambar maksimal 10MB.',
            'directory.string' => 'Direktori harus berupa teks.',
            'directory.max' => 'Nama direktori terlalu panjang.',
            'sizes.array' => 'Format ukuran gambar tidak valid.',
            'sizes.*.name.required_with' => 'Nama ukuran gambar wajib diisi.',
            'sizes.*.name.string' => 'Nama ukuran gambar harus berupa teks.',
            'sizes.*.width.required_with' => 'Lebar gambar wajib diisi.',
            'sizes.*.width.integer' => 'Lebar gambar harus berupa angka.',
            'sizes.*.width.min' => 'Lebar gambar minimal 1 pixel.',
            'sizes.*.width.max' => 'Lebar gambar maksimal 2000 pixel.',
        ];
    }

    /**
     * Get validated sizes with defaults
     */
    public function getValidatedSizes(): array
    {
        $customSizes = $this->validated()['sizes'] ?? [];

        $defaultSizes = [
            'original' => null,
            'medium' => 800,
            'thumbnail' => 300,
        ];

        if (empty($customSizes)) {
            return $defaultSizes;
        }

        $sizes = [];
        foreach ($customSizes as $size) {
            $sizes[$size['name']] = $size['width'];
        }

        return $sizes;
    }

    /**
     * Get validated directory with default
     */
    public function getValidatedDirectory(): string
    {
        return $this->validated()['directory'] ?? 'images';
    }
}
