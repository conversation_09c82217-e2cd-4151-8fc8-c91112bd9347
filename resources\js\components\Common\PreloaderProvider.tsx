import { createContext, useContext, useEffect, useState, type ReactNode } from 'react';
import Preloader from '../Preloader';

interface PreloaderContextType {
    isLoading: boolean;
    setIsLoading: (loading: boolean) => void;
}

const PreloaderContext = createContext<PreloaderContextType | undefined>(undefined);

export function usePreloader() {
    const context = useContext(PreloaderContext);
    if (context === undefined) {
        throw new Error('usePreloader must be used within a PreloaderProvider');
    }
    return context;
}

interface PreloaderProviderProps {
    children: ReactNode;
    showPreloader?: boolean;
    minDuration?: number;
}

export function PreloaderProvider({
    children,
    showPreloader = import.meta.env.VITE_SHOW_PRELOADER === 'true',
    minDuration = 2000,
}: PreloaderProviderProps) {
    const [isLoading, setIsLoading] = useState(showPreloader);
    const [showChildren, setShowChildren] = useState(!showPreloader);

    useEffect(() => {
        if (!showPreloader) {
            setShowChildren(true);
        }
    }, [showPreloader]);

    const handlePreloaderComplete = () => {
        setIsLoading(false);
        setShowChildren(true);
    };

    return (
        <PreloaderContext.Provider value={{ isLoading, setIsLoading }}>
            {isLoading && showPreloader && <Preloader onComplete={handlePreloaderComplete} minDuration={minDuration} />}
            {showChildren && children}
        </PreloaderContext.Provider>
    );
}
