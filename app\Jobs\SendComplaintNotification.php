<?php

namespace App\Jobs;

use App\Mail\ComplaintSubmitted;
use App\Models\Complaint;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;

class SendComplaintNotification implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public function __construct(
        public Complaint $complaint
    ) {}

    public function handle(): void
    {
        try {
            // Send notification to admin
            $adminEmail = config('mail.admin_email', '<EMAIL>');

            Mail::to($adminEmail)->send(new ComplaintSubmitted($this->complaint));

            Log::info('Complaint notification sent to admin', [
                'ticket_number' => $this->complaint->ticket_number,
                'admin_email' => $adminEmail,
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to send complaint notification', [
                'ticket_number' => $this->complaint->ticket_number,
                'error' => $e->getMessage(),
            ]);

            // Re-throw the exception to trigger job retry
            throw $e;
        }
    }

    public function failed(\Throwable $exception): void
    {
        Log::error('Complaint notification job failed permanently', [
            'ticket_number' => $this->complaint->ticket_number,
            'error' => $exception->getMessage(),
        ]);
    }
}
