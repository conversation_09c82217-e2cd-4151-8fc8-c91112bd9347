import { FeatureFlags } from '@/types/feature-flags';
import { usePage } from '@inertiajs/react';
import React from 'react';

/**
 * Hook to get feature flags from Inertia props
 */
export function useFeatureFlags(): FeatureFlags {
    const { props } = usePage();
    return (props.featureFlags as FeatureFlags) || {};
}

/**
 * Check if a feature flag is active (requires flags parameter for non-React usage)
 */
export function isFeatureActive(feature: string, flags: FeatureFlags): boolean {
    return flags[feature] === true;
}

/**
 * Check if a feature flag is inactive (requires flags parameter for non-React usage)
 */
export function isFeatureInactive(feature: string, flags: FeatureFlags): boolean {
    return !isFeatureActive(feature, flags);
}

/**
 * Execute callback when feature is active (requires flags parameter for non-React usage)
 */
export function whenFeature<T>(feature: string, callback: () => T, flags: FeatureFlags, defaultCallback?: () => T): T | undefined {
    if (isFeatureActive(feature, flags)) {
        return callback();
    }

    return defaultCallback ? defaultCallback() : undefined;
}

/**
 * Execute callback when feature is inactive (requires flags parameter for non-React usage)
 */
export function unlessFeature<T>(feature: string, callback: () => T, flags: FeatureFlags, defaultCallback?: () => T): T | undefined {
    if (isFeatureInactive(feature, flags)) {
        return callback();
    }

    return defaultCallback ? defaultCallback() : undefined;
}

/**
 * Get feature flag value with default (requires flags parameter for non-React usage)
 */
export function getFeatureValue(feature: string, flags: FeatureFlags, defaultValue: boolean = false): boolean {
    return flags[feature] !== undefined ? flags[feature] : defaultValue;
}

/**
 * React hook for conditional rendering based on feature flags
 */
export function useFeature(feature: string): {
    isActive: boolean;
    isInactive: boolean;
    value: boolean;
} {
    const flags = useFeatureFlags();
    const value = flags[feature] || false;

    return {
        isActive: value === true,
        isInactive: value !== true,
        value,
    };
}

/**
 * Higher-order component for feature flag conditional rendering
 */
export function withFeature<P extends object>(feature: string, Component: React.ComponentType<P>, FallbackComponent?: React.ComponentType<P>) {
    return function FeatureWrappedComponent(props: P) {
        const { isActive } = useFeature(feature);

        if (isActive) {
            return React.createElement(Component, props);
        }

        if (FallbackComponent) {
            return React.createElement(FallbackComponent, props);
        }

        return null;
    };
}

/**
 * React component for conditional rendering based on feature flags
 */
interface FeatureGateProps {
    feature: string;
    children: React.ReactNode;
    fallback?: React.ReactNode;
    invert?: boolean;
}

export function FeatureGate({ feature, children, fallback = null, invert = false }: FeatureGateProps) {
    const { isActive } = useFeature(feature);
    const shouldShow = invert ? !isActive : isActive;

    return shouldShow ? React.createElement(React.Fragment, null, children) : React.createElement(React.Fragment, null, fallback);
}
