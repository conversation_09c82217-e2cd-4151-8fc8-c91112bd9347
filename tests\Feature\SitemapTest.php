<?php

namespace Tests\Feature;

use App\Models\News;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class SitemapTest extends TestCase
{
    use RefreshDatabase;

    public function test_sitemap_xml_is_accessible(): void
    {
        $response = $this->get('/sitemap.xml');

        $response->assertStatus(200);
        $response->assertHeader('Content-Type', 'application/xml; charset=UTF-8');
    }

    public function test_sitemap_contains_static_pages(): void
    {
        $response = $this->get('/sitemap.xml');

        $content = $response->getContent();

        // Check for static pages
        $this->assertStringContainsString('<loc>'.url('/').'</loc>', $content);
        $this->assertStringContainsString('<loc>'.url('/profil').'</loc>', $content);
        $this->assertStringContainsString('<loc>'.url('/layanan').'</loc>', $content);
        $this->assertStringContainsString('<loc>'.url('/berita').'</loc>', $content);
        $this->assertStringContainsString('<loc>'.url('/potensi').'</loc>', $content);
    }

    public function test_sitemap_includes_published_news(): void
    {
        // Create a published news article
        $news = News::factory()->create([
            'title' => 'Test News Article',
            'slug' => 'test-news-article',
            'is_published' => true,
            'published_at' => now(),
        ]);

        $response = $this->get('/sitemap.xml');
        $content = $response->getContent();

        $this->assertStringContainsString(
            '<loc>'.url('/berita/test-news-article').'</loc>',
            $content
        );
    }

    public function test_robots_txt_is_accessible(): void
    {
        $response = $this->get('/robots.txt');

        $response->assertStatus(200);
        $response->assertHeader('Content-Type', 'text/plain; charset=UTF-8');

        $content = $response->getContent();
        $this->assertStringContainsString('User-agent: *', $content);
        $this->assertStringContainsString('Sitemap: http://localhost/sitemap.xml', $content);
    }
}
