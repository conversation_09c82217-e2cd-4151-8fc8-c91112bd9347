/**
 * Browser Extension Defense Utilities
 * 
 * This module provides defensive measures against browser extension interference
 * that can cause XrayWrapper errors and other cross-origin issues.
 */

/**
 * Safely define properties on objects to prevent XrayWrapper errors
 */
export function safeDefineProperty(obj: any, prop: string, value: any): boolean {
    try {
        if (obj && typeof obj === 'object' && !Object.isFrozen(obj)) {
            Object.defineProperty(obj, prop, {
                value,
                writable: true,
                enumerable: false,
                configurable: true
            });
            return true;
        }
    } catch (error) {
        // Silently fail for XrayWrapper errors
        console.debug('Failed to define property (likely browser extension interference):', prop);
    }
    return false;
}

/**
 * Safely access object properties to prevent extension conflicts
 */
export function safePropertyAccess(obj: any, prop: string): any {
    try {
        return obj?.[prop];
    } catch (error) {
        console.debug('Failed to access property (likely browser extension interference):', prop);
        return undefined;
    }
}

/**
 * Initialize defensive measures against browser extension interference
 */
export function initializeBrowserExtensionDefense(): void {
    // Prevent extensions from modifying critical global objects
    const protectedGlobals = ['window', 'document', 'navigator'];
    
    protectedGlobals.forEach(globalName => {
        try {
            const globalObj = (window as any)[globalName];
            if (globalObj && typeof globalObj === 'object') {
                // Freeze critical properties to prevent modification
                const criticalProps = ['location', 'origin', 'protocol', 'host'];
                criticalProps.forEach(prop => {
                    try {
                        const descriptor = Object.getOwnPropertyDescriptor(globalObj, prop);
                        if (descriptor && descriptor.configurable) {
                            Object.defineProperty(globalObj, prop, {
                                ...descriptor,
                                configurable: false
                            });
                        }
                    } catch (e) {
                        // Silently ignore if we can't protect this property
                    }
                });
            }
        } catch (error) {
            // Silently ignore if we can't access the global
        }
    });

    // Add error handler for unhandled extension errors
    window.addEventListener('error', (event) => {
        const errorMessage = event.message?.toLowerCase() || '';
        
        // Check if this is a browser extension related error
        if (
            errorMessage.includes('xraywrapper') ||
            errorMessage.includes('cross-origin') ||
            errorMessage.includes('content-script') ||
            event.filename?.includes('extension')
        ) {
            // Prevent the error from propagating and cluttering the console
            event.preventDefault();
            event.stopPropagation();
            
            // Log a debug message instead
            console.debug('Browser extension error suppressed:', {
                message: event.message,
                filename: event.filename,
                lineno: event.lineno,
                colno: event.colno
            });
            
            return false;
        }
    }, true);

    // Handle unhandled promise rejections from extensions
    window.addEventListener('unhandledrejection', (event) => {
        const reason = event.reason?.toString()?.toLowerCase() || '';
        
        if (
            reason.includes('xraywrapper') ||
            reason.includes('cross-origin') ||
            reason.includes('content-script')
        ) {
            // Prevent the error from propagating
            event.preventDefault();
            
            // Log a debug message instead
            console.debug('Browser extension promise rejection suppressed:', event.reason);
        }
    });
}

/**
 * Wrapper for iframe creation that handles extension interference
 */
export function createSafeIframe(src: string, attributes: Record<string, any> = {}): HTMLIFrameElement | null {
    try {
        const iframe = document.createElement('iframe');
        
        // Set basic attributes
        iframe.src = src;
        iframe.style.border = '0';
        iframe.loading = 'lazy';
        
        // Apply additional attributes safely
        Object.entries(attributes).forEach(([key, value]) => {
            try {
                if (key === 'style' && typeof value === 'object') {
                    Object.assign(iframe.style, value);
                } else {
                    iframe.setAttribute(key, value);
                }
            } catch (error) {
                console.debug(`Failed to set iframe attribute ${key}:`, error);
            }
        });
        
        return iframe;
    } catch (error) {
        console.error('Failed to create iframe:', error);
        return null;
    }
}

/**
 * Check if the current environment has problematic browser extensions
 */
export function detectProblematicExtensions(): boolean {
    try {
        // Check for common extension indicators
        const indicators = [
            'chrome-extension:',
            'moz-extension:',
            'safari-extension:',
            'edge-extension:'
        ];
        
        // Check if any scripts are loaded from extension URLs
        const scripts = Array.from(document.scripts);
        const hasExtensionScripts = scripts.some(script => 
            indicators.some(indicator => script.src.includes(indicator))
        );
        
        // Check for extension-injected elements
        const hasExtensionElements = document.querySelector('[data-extension]') !== null;
        
        return hasExtensionScripts || hasExtensionElements;
    } catch (error) {
        return false;
    }
}
