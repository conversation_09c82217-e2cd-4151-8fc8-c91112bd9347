<?php

namespace App\Exports;

use App\Models\Complaint;
use Illuminate\Support\Collection;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\WithStyles;
use Maatwebsite\Excel\Concerns\WithTitle;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;

class ComplaintExport implements FromCollection, WithHeadings, WithMapping, WithStyles, WithTitle
{
    protected $complaints;

    public function __construct(Collection $complaints)
    {
        $this->complaints = $complaints;
    }

    /**
     * @return Collection
     */
    public function collection()
    {
        return $this->complaints;
    }

    public function headings(): array
    {
        return [
            'No. Tiket',
            'Nama',
            'Email',
            'Telepon',
            'Kategori',
            'Subjek',
            'Deskripsi',
            'Status',
            'Prioritas',
            'Tanggal Pengaduan',
            'Respon Admin',
            'Tanggal Respon',
            'Petugas Respon',
        ];
    }

    /**
     * @param  Complaint  $complaint
     */
    public function map($complaint): array
    {
        return [
            $complaint->ticket_number,
            $complaint->name,
            $complaint->email,
            $complaint->phone,
            $complaint->category,
            $complaint->subject,
            strip_tags($complaint->description),
            $complaint->status,
            $complaint->priority,
            $complaint->created_at->format('d/m/Y H:i'),
            $complaint->admin_response ? strip_tags($complaint->admin_response) : '-',
            $complaint->responded_at ? $complaint->responded_at->format('d/m/Y H:i') : '-',
            $complaint->respondedBy ? $complaint->respondedBy->name : '-',
        ];
    }

    /**
     * @return array
     */
    public function styles(Worksheet $sheet)
    {
        return [
            // Style the first row as bold text.
            1 => ['font' => ['bold' => true]],
        ];
    }

    public function title(): string
    {
        return 'Laporan Pengaduan';
    }
}
