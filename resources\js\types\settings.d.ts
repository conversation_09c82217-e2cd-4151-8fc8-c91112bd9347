// Settings types for Desa <PERSON>mah <PERSON>
// These types match the structure defined in the SettingSeeder

export interface ContactInfo {
    phone: string;
    whatsapp: string;
    email: string;
    address: string;
    postal_code: string;
    maps_link: string;
}

export interface VillageProfile {
    name: string;
    district: string;
    regency: string;
    province: string;
    established_year: string;
    area: string;
    population: string;
}

export interface OperatingHours {
    weekdays: string;
    saturday: string;
    sunday: string;
    break: string;
    holidays: string;
}

export interface EmergencyContact {
    title: string;
    phone: string;
}

export interface EmergencyContacts {
    village_head: EmergencyContact;
    village_secretary: EmergencyContact;
    security: EmergencyContact;
    health_center: EmergencyContact;
}

export interface VillageHistory {
    etymology: {
        lemah: string;
        duhur_luhur: string;
        meaning: string;
    };
    legend: string;
    administrative_history: string;
    cultural_significance: string;
}

export interface ServiceSettings {
    online_services_available: boolean;
    mobile_optimized: boolean;
    emergency_contact_available: boolean;
    multilingual_support: boolean;
    digital_signature_enabled: boolean;
}

// Main settings interface that maps setting keys to their values
export interface VillageSettings {
    'village.contact_info': ContactInfo;
    'village.profile': VillageProfile;
    'village.operating_hours': OperatingHours;
    'village.emergency_contacts': EmergencyContacts;
    'village.history': VillageHistory;
    'village.service_settings': ServiceSettings;
}

// Type for individual setting keys
export type SettingKey = keyof VillageSettings;

// Type for setting values
export type SettingValue<K extends SettingKey> = VillageSettings[K];

// Generic type for any setting value
export type AnySetting = VillageSettings[SettingKey];
