<?php

namespace App\Traits;

trait FlashMessage
{
    /**
     * Flash a success message
     */
    protected function flashSuccess(string $message): void
    {
        session()->flash('success', $message);
    }

    /**
     * Flash an error message
     */
    protected function flashError(string $message): void
    {
        session()->flash('error', $message);
    }

    /**
     * Flash a warning message
     */
    protected function flashWarning(string $message): void
    {
        session()->flash('warning', $message);
    }

    /**
     * Flash an info message
     */
    protected function flashInfo(string $message): void
    {
        session()->flash('info', $message);
    }

    /**
     * Flash a general message
     */
    protected function flashMessage(string $message): void
    {
        session()->flash('message', $message);
    }

    /**
     * Redirect back with success message
     */
    protected function redirectWithSuccess(string $message, ?string $route = null)
    {
        $this->flashSuccess($message);

        if ($route) {
            return redirect()->route($route);
        }

        return redirect()->back();
    }

    /**
     * Redirect back with error message
     */
    protected function redirectWithError(string $message, ?string $route = null)
    {
        $this->flashError($message);

        if ($route) {
            return redirect()->route($route);
        }

        return redirect()->back();
    }

    /**
     * Redirect back with warning message
     */
    protected function redirectWithWarning(string $message, ?string $route = null)
    {
        $this->flashWarning($message);

        if ($route) {
            return redirect()->route($route);
        }

        return redirect()->back();
    }

    /**
     * Redirect back with info message
     */
    protected function redirectWithInfo(string $message, ?string $route = null)
    {
        $this->flashInfo($message);

        if ($route) {
            return redirect()->route($route);
        }

        return redirect()->back();
    }
}
