<?php

namespace Database\Seeders;

use App\Models\Setting;
use Illuminate\Database\Seeder;

class FeatureFlagSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Feature flag metadata - stores flag definitions and descriptions
        Setting::updateOrCreate(
            ['key' => 'feature_flags._metadata'],
            [
                'value' => [
                    'pages.layanan' => [
                        'title' => 'Halaman Layanan',
                        'description' => 'Mengaktifkan/menonaktifkan akses ke halaman layanan publik desa',
                        'category' => 'pages',
                        'default' => true,
                        'experimental' => false,
                        'warning' => 'Menonaktifkan halaman ini akan menyembunyikan semua informasi layanan dari warga',
                        'dependencies' => [],
                    ],
                    'pages.berita' => [
                        'title' => 'Halaman Berita',
                        'description' => 'Mengaktifkan/menonaktifkan akses ke halaman berita dan artikel desa',
                        'category' => 'pages',
                        'default' => true,
                        'experimental' => false,
                        'warning' => 'Menonaktifkan halaman ini akan menyembunyikan semua berita dari warga',
                        'dependencies' => [],
                    ],
                    'pages.potensi' => [
                        'title' => 'Halaman Potensi',
                        'description' => 'Mengaktifkan/menonaktifkan akses ke halaman potensi wisata dan UMKM desa',
                        'category' => 'pages',
                        'default' => true,
                        'experimental' => false,
                        'warning' => 'Menonaktifkan halaman ini akan menyembunyikan informasi wisata dan UMKM',
                        'dependencies' => [],
                    ],
                    'pages.pengaduan' => [
                        'title' => 'Halaman Pengaduan',
                        'description' => 'Mengaktifkan/menonaktifkan akses ke sistem pengaduan masyarakat',
                        'category' => 'pages',
                        'default' => true,
                        'experimental' => false,
                        'warning' => 'Menonaktifkan halaman ini akan menutup akses warga untuk menyampaikan pengaduan',
                        'dependencies' => [],
                    ],
                ],
                'type' => 'json',
                'description' => 'Metadata dan definisi untuk feature flags halaman publik website desa',
            ]
        );

        // Pages Category Feature Flags
        Setting::updateOrCreate(
            ['key' => 'feature_flags.pages.layanan'],
            [
                'value' => true,
                'type' => 'boolean',
                'description' => 'Status aktif/nonaktif untuk halaman layanan publik desa',
            ]
        );

        Setting::updateOrCreate(
            ['key' => 'feature_flags.pages.berita'],
            [
                'value' => true,
                'type' => 'boolean',
                'description' => 'Status aktif/nonaktif untuk halaman berita dan artikel desa',
            ]
        );

        Setting::updateOrCreate(
            ['key' => 'feature_flags.pages.potensi'],
            [
                'value' => true,
                'type' => 'boolean',
                'description' => 'Status aktif/nonaktif untuk halaman potensi wisata dan UMKM desa',
            ]
        );

        Setting::updateOrCreate(
            ['key' => 'feature_flags.pages.pengaduan'],
            [
                'value' => true,
                'type' => 'boolean',
                'description' => 'Status aktif/nonaktif untuk sistem pengaduan masyarakat',
            ]
        );
    }
}
