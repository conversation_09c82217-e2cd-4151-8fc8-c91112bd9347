<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Symfony\Component\HttpFoundation\Response;

class CachePublicPages
{
    /**
     * Handle an incoming request.
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Only cache GET requests for public pages
        if (! $request->isMethod('GET') || $request->user()) {
            return $next($request);
        }

        // Skip caching for admin routes
        if ($request->is('dashboard*') || $request->is('admin*')) {
            return $next($request);
        }

        // Generate cache key based on URL and query parameters
        $cacheKey = 'page_cache:'.md5($request->fullUrl());

        // Check if page is cached
        if (Cache::has($cacheKey)) {
            $cachedResponse = Cache::get($cacheKey);

            return response($cachedResponse['content'])
                ->withHeaders($cachedResponse['headers'])
                ->header('X-Cache-Status', 'HIT');
        }

        // Process request
        $response = $next($request);

        // Only cache successful responses
        if ($response->getStatusCode() === 200) {
            $cacheData = [
                'content' => $response->getContent(),
                'headers' => [
                    'Content-Type' => $response->headers->get('Content-Type'),
                    'Cache-Control' => 'public, max-age=3600',
                    'Expires' => now()->addHour()->toRfc7231String(),
                ],
            ];

            // Cache for different durations based on route
            $cacheDuration = $this->getCacheDuration($request);
            Cache::put($cacheKey, $cacheData, $cacheDuration);

            $response->header('X-Cache-Status', 'MISS');
        }

        return $response;
    }

    /**
     * Get cache duration based on route
     */
    private function getCacheDuration(Request $request): int
    {
        // Homepage and profile pages - cache longer (2 hours)
        if ($request->is('/') || $request->is('profil*')) {
            return 7200; // 2 hours
        }

        // Services and potential pages - cache medium (1 hour)
        if ($request->is('layanan*') || $request->is('potensi*')) {
            return 3600; // 1 hour
        }

        // News pages - cache shorter (30 minutes)
        if ($request->is('berita*')) {
            return 1800; // 30 minutes
        }

        // Default cache duration (1 hour)
        return 3600;
    }
}
