import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import PublicLayout from '@/layouts/PublicLayout';
import { Head, Link, router } from '@inertiajs/react';
import { AlertTriangle, Calendar, CheckCircle, Clock, Filter, Image, MessageSquare, Search, XCircle } from 'lucide-react';
import React from 'react';

interface Complaint {
    id: number;
    ticket_number: string;
    name: string;
    category: string;
    subject: string;
    description: string;
    status: string;

    priority: string;

    admin_response: string | null;
    attachments?: Array<{
        path: string;
        original_name: string;
        size: number;
        mime_type: string;
    }>;
    created_at: string;
    responded_at: string | null;
}

interface Stats {
    total: number;
    pending: number;
    in_progress: number;
    resolved: number;
    closed: number;
}

interface PaginationLink {
    url: string | null;
    label: string;
    active: boolean;
}

interface PaginationMeta {
    current_page: number;
    last_page: number;
    per_page: number;
    total: number;
}

interface Props {
    complaints: {
        data?: Complaint[];
        links?: PaginationLink[];
        meta?: PaginationMeta;
    };
    stats: Stats;
    filters: {
        status?: string;
        category?: string;
        priority?: string;
        search?: string;
    };
    categories: Record<string, string>;
    statuses: Record<string, string>;
    priorities: Record<string, string>;
}

export default function ComplaintsPublic({ complaints, stats, filters, categories, statuses, priorities }: Props) {
    const handleFilter = (key: string, value: string) => {
        router.get(
            route('complaints.public'),
            {
                ...filters,
                [key]: value === 'all' ? undefined : value,
            },
            {
                preserveState: true,
                replace: true,
            },
        );
    };

    const handleSearch = (e: React.FormEvent<HTMLFormElement>) => {
        e.preventDefault();
        const formData = new FormData(e.currentTarget);
        const search = formData.get('search') as string;

        router.get(
            route('complaints.public'),
            {
                ...filters,
                search: search || undefined,
            },
            {
                preserveState: true,
                replace: true,
            },
        );
    };

    const getStatusIcon = (status: string) => {
        switch (status) {
            case 'pending':
                return <Clock className="h-4 w-4" />;
            case 'in_progress':
                return <MessageSquare className="h-4 w-4" />;
            case 'resolved':
                return <CheckCircle className="h-4 w-4" />;
            case 'closed':
                return <XCircle className="h-4 w-4" />;
            default:
                return <Clock className="h-4 w-4" />;
        }
    };

    return (
        <PublicLayout>
            <Head title="Pengaduan Publik - Desa Lemah Duhur" />

            <div className="container mx-auto px-4 py-8">
                {/* Header */}
                <div className="mb-8 text-center">
                    <h1 className="mb-4 text-4xl font-bold text-gray-900 dark:text-gray-100">Pengaduan Publik</h1>
                    <p className="text-lg text-gray-600 dark:text-gray-300">
                        Lihat pengaduan masyarakat yang telah dipublikasikan oleh pemerintah desa
                    </p>
                </div>

                {/* Statistics Cards */}
                <div className="mb-8 grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-5">
                    <Card className="dark:border-neutral-700 dark:bg-neutral-800">
                        <CardHeader className="pb-2">
                            <CardTitle className="text-sm font-medium text-gray-600 dark:text-gray-300">Total Publik</CardTitle>
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold text-gray-900 dark:text-gray-100">{stats.total}</div>
                        </CardContent>
                    </Card>
                    <Card className="dark:border-neutral-700 dark:bg-neutral-800">
                        <CardHeader className="pb-2">
                            <CardTitle className="text-sm font-medium text-yellow-600 dark:text-yellow-400">Menunggu</CardTitle>
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold text-yellow-600 dark:text-yellow-400">{stats.pending}</div>
                        </CardContent>
                    </Card>
                    <Card className="dark:border-neutral-700 dark:bg-neutral-800">
                        <CardHeader className="pb-2">
                            <CardTitle className="text-sm font-medium text-blue-600 dark:text-blue-400">Diproses</CardTitle>
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold text-blue-600 dark:text-blue-400">{stats.in_progress}</div>
                        </CardContent>
                    </Card>
                    <Card className="dark:border-neutral-700 dark:bg-neutral-800">
                        <CardHeader className="pb-2">
                            <CardTitle className="text-sm font-medium text-green-600 dark:text-green-400">Selesai</CardTitle>
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold text-green-600 dark:text-green-400">{stats.resolved}</div>
                        </CardContent>
                    </Card>
                    <Card className="dark:border-neutral-700 dark:bg-neutral-800">
                        <CardHeader className="pb-2">
                            <CardTitle className="text-sm font-medium text-gray-600 dark:text-gray-300">Ditutup</CardTitle>
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold text-gray-600 dark:text-gray-300">{stats.closed}</div>
                        </CardContent>
                    </Card>
                </div>

                {/* Filters */}
                <Card className="mb-8 dark:border-neutral-800 dark:bg-neutral-900">
                    <CardHeader>
                        <CardTitle className="flex items-center gap-2 dark:text-gray-100">
                            <Filter className="h-5 w-5" />
                            Filter & Pencarian
                        </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                        <form onSubmit={handleSearch} className="flex gap-2">
                            <div className="flex-1">
                                <Input
                                    name="search"
                                    placeholder="Cari berdasarkan nomor tiket atau subjek..."
                                    defaultValue={filters.search}
                                    className="w-full dark:border-neutral-700 dark:bg-neutral-800 dark:text-gray-100"
                                />
                            </div>
                            <Button type="submit" className="dark:bg-blue-700 dark:text-white">
                                <Search className="mr-2 h-4 w-4" />
                                Cari
                            </Button>
                        </form>

                        <div className="grid grid-cols-1 gap-4 md:grid-cols-4">
                            <Select value={filters.status || 'all'} onValueChange={(value) => handleFilter('status', value)}>
                                <SelectTrigger className="dark:border-neutral-700 dark:bg-neutral-800 dark:text-gray-100">
                                    <SelectValue placeholder="Semua Status" />
                                </SelectTrigger>
                                <SelectContent className="dark:bg-neutral-900 dark:text-gray-100">
                                    <SelectItem value="all">Semua Status</SelectItem>
                                    {Object.entries(statuses).map(([key, label]) => (
                                        <SelectItem key={key} value={key}>
                                            {label}
                                        </SelectItem>
                                    ))}
                                </SelectContent>
                            </Select>

                            <Select value={filters.category || 'all'} onValueChange={(value) => handleFilter('category', value)}>
                                <SelectTrigger className="dark:border-neutral-700 dark:bg-neutral-800 dark:text-gray-100">
                                    <SelectValue placeholder="Semua Kategori" />
                                </SelectTrigger>
                                <SelectContent className="dark:bg-neutral-900 dark:text-gray-100">
                                    <SelectItem value="all">Semua Kategori</SelectItem>
                                    {Object.entries(categories).map(([key, label]) => (
                                        <SelectItem key={key} value={key}>
                                            {label}
                                        </SelectItem>
                                    ))}
                                </SelectContent>
                            </Select>

                            <Select value={filters.priority || 'all'} onValueChange={(value) => handleFilter('priority', value)}>
                                <SelectTrigger className="dark:border-neutral-700 dark:bg-neutral-800 dark:text-gray-100">
                                    <SelectValue placeholder="Semua Prioritas" />
                                </SelectTrigger>
                                <SelectContent className="dark:bg-neutral-900 dark:text-gray-100">
                                    <SelectItem value="all">Semua Prioritas</SelectItem>
                                    {Object.entries(priorities).map(([key, label]) => (
                                        <SelectItem key={key} value={key}>
                                            {label}
                                        </SelectItem>
                                    ))}
                                </SelectContent>
                            </Select>

                            <Link href={route('complaints.index')}>
                                <Button className="w-full dark:bg-blue-700 dark:text-white">
                                    <MessageSquare className="mr-2 h-4 w-4" />
                                    Buat Pengaduan
                                </Button>
                            </Link>
                        </div>
                    </CardContent>
                </Card>

                {/* Complaints List */}
                <Card className="dark:border-neutral-800 dark:bg-neutral-900">
                    <CardHeader>
                        <CardTitle className="dark:text-gray-100">Daftar Pengaduan Publik</CardTitle>
                        <CardDescription className="dark:text-gray-300">
                            Menampilkan {complaints.data?.length || 0} dari {complaints.meta?.total || 0} pengaduan publik
                        </CardDescription>
                    </CardHeader>
                    <CardContent>
                        <div className="space-y-4">
                            {complaints.data?.map((complaint) => (
                                <Card key={complaint.id} className="transition-shadow hover:shadow-md dark:border-neutral-700 dark:bg-neutral-800">
                                    <CardContent className="p-6">
                                        <div className="flex items-start justify-between">
                                            <div className="flex-1">
                                                <div className="mb-2 flex items-center gap-2">
                                                    <span className="font-mono text-sm text-blue-600 dark:text-blue-400">
                                                        {complaint.ticket_number}
                                                    </span>
                                                    <Badge variant="outline" className="dark:border-gray-600 dark:text-gray-300">
                                                        {complaint.category}
                                                    </Badge>
                                                    <Badge
                                                        variant={complaint.status === 'resolved' ? 'default' : 'secondary'}
                                                        className={`flex items-center gap-1 ${
                                                            complaint.status === 'pending'
                                                                ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300'
                                                                : complaint.status === 'in_progress'
                                                                  ? 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300'
                                                                  : complaint.status === 'resolved'
                                                                    ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300'
                                                                    : 'bg-gray-100 text-gray-800 dark:bg-neutral-700 dark:text-gray-300'
                                                        }`}
                                                    >
                                                        {getStatusIcon(complaint.status)}
                                                        {complaint.status}
                                                    </Badge>
                                                    {complaint.priority === 'urgent' && (
                                                        <Badge
                                                            variant="outline"
                                                            className="border-red-200 bg-red-50 text-red-800 dark:border-red-700 dark:bg-red-900 dark:text-red-300"
                                                        >
                                                            <AlertTriangle className="mr-1 h-3 w-3" />
                                                            {complaint.priority}
                                                        </Badge>
                                                    )}
                                                </div>
                                                <h3 className="mb-2 text-lg font-semibold dark:text-gray-100">{complaint.subject}</h3>
                                                <p className="mb-3 line-clamp-2 text-gray-600 dark:text-gray-300">{complaint.description}</p>

                                                {/* Image Thumbnails */}
                                                {complaint.attachments && complaint.attachments.length > 0 && (
                                                    <div className="mb-3 flex gap-2">
                                                        {complaint.attachments.slice(0, 3).map((image, index) => (
                                                            <div
                                                                key={index}
                                                                className="h-16 w-16 overflow-hidden rounded-lg bg-gray-100 dark:bg-neutral-700"
                                                            >
                                                                <img
                                                                    src={`/storage/${image.path}`}
                                                                    alt={`Foto bukti ${index + 1}`}
                                                                    className="h-full w-full object-cover"
                                                                />
                                                            </div>
                                                        ))}
                                                        {complaint.attachments.length > 3 && (
                                                            <div className="flex h-16 w-16 items-center justify-center rounded-lg bg-gray-100 text-xs text-gray-500 dark:bg-neutral-700 dark:text-gray-300">
                                                                +{complaint.attachments.length - 3}
                                                            </div>
                                                        )}
                                                        <div className="flex items-center text-xs text-gray-500 dark:text-gray-300">
                                                            <Image className="mr-1 h-3 w-3" />
                                                            {complaint.attachments.length} foto
                                                        </div>
                                                    </div>
                                                )}
                                                <div className="flex items-center gap-4 text-sm text-gray-500 dark:text-gray-300">
                                                    <div className="flex items-center gap-1">
                                                        <Calendar className="h-4 w-4" />
                                                        {new Date(complaint.created_at).toLocaleDateString('id-ID', {
                                                            day: '2-digit',
                                                            month: 'long',
                                                            year: 'numeric',
                                                        })}
                                                    </div>
                                                    <span>oleh {complaint.name}</span>
                                                </div>
                                                {complaint.admin_response && (
                                                    <div className="mt-4 rounded-lg bg-blue-50 p-3 dark:bg-blue-900">
                                                        <h4 className="mb-1 text-sm font-medium text-blue-900 dark:text-blue-200">
                                                            Respon Pemerintah Desa:
                                                        </h4>
                                                        <p className="text-sm text-blue-800 dark:text-blue-200">{complaint.admin_response}</p>
                                                        {complaint.responded_at && (
                                                            <p className="mt-1 text-xs text-blue-600 dark:text-blue-300">
                                                                Direspon pada{' '}
                                                                {new Date(complaint.responded_at).toLocaleDateString('id-ID', {
                                                                    day: '2-digit',
                                                                    month: 'long',
                                                                    year: 'numeric',
                                                                })}
                                                            </p>
                                                        )}
                                                    </div>
                                                )}
                                            </div>
                                        </div>
                                    </CardContent>
                                </Card>
                            ))}

                            {(complaints.data?.length || 0) === 0 && (
                                <div className="py-12 text-center text-gray-500 dark:text-gray-300">
                                    <MessageSquare className="mx-auto mb-4 h-12 w-12 text-gray-300 dark:text-gray-700" />
                                    <h3 className="mb-2 text-lg font-medium text-gray-900 dark:text-gray-100">Belum ada pengaduan publik</h3>
                                    <p className="mb-4 text-gray-600 dark:text-gray-300">
                                        Belum ada pengaduan yang dipublikasikan oleh pemerintah desa.
                                    </p>
                                    <Link href={route('complaints.index')}>
                                        <Button className="dark:bg-blue-700 dark:text-white">
                                            <MessageSquare className="mr-2 h-4 w-4" />
                                            Buat Pengaduan Pertama
                                        </Button>
                                    </Link>
                                </div>
                            )}
                        </div>

                        {/* Pagination */}
                        {(complaints.meta?.last_page || 0) > 1 && (
                            <div className="mt-6 flex justify-center">
                                <div className="flex gap-2">
                                    {complaints.links?.map((link, index) => (
                                        <Button
                                            key={index}
                                            variant={link.active ? 'default' : 'outline'}
                                            size="sm"
                                            disabled={!link.url}
                                            onClick={() => link.url && router.get(link.url)}
                                            dangerouslySetInnerHTML={{ __html: link.label }}
                                            className="dark:border-blue-700 dark:bg-blue-700 dark:text-white"
                                        />
                                    ))}
                                </div>
                            </div>
                        )}
                    </CardContent>
                </Card>
            </div>
        </PublicLayout>
    );
}
