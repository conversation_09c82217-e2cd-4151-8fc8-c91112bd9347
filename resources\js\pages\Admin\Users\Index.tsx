import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import AdminLayout from '@/layouts/AdminLayout';
import { type BreadcrumbItem, type PaginatedData, type User } from '@/types';
import { Head, Link, router, useForm } from '@inertiajs/react';
import { Edit, Eye, Plus, Search, Trash2, Users as UsersIcon } from 'lucide-react';
import { FormEventHandler, useEffect } from 'react';

interface Props {
    users: PaginatedData<User & { role: string }>;
    roles: Record<string, string>;
    filters: {
        role?: string;
        search?: string;
    };
    auth: {
        user: User & { role: string };
    };
}

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'Dashboard',
        href: '/dashboard',
    },
    {
        title: 'Manajemen Pengguna',
        href: '/admin/users',
    },
];

export default function UsersIndex({ users, roles, filters, auth }: Props) {
    const { data, setData, processing } = useForm({
        search: filters.search || '',
        role: filters.role || 'all',
    });

    const handleFilter: FormEventHandler = (e) => {
        e.preventDefault();
        const params = {
            search: data.search,
            ...(data.role !== 'all' && { role: data.role }),
        };
        router.get(route('admin.users.index'), params, {
            preserveState: true,
            preserveScroll: true,
        });
    };

    const handleReset = () => {
        setData({
            search: '',
            role: 'all',
        });
        router.get(route('admin.users.index'));
    };

    const handleDelete = (user: User & { role: string }) => {
        if (window.confirm(`Apakah Anda yakin ingin menghapus pengguna "${user.name}"?`)) {
            router.delete(route('admin.users.destroy', user.id), {
                onSuccess: () => {
                    // Success message akan ditangani oleh FlashMessages
                },
            });
        }
    };

    const getRoleBadgeVariant = (role: string) => {
        switch (role) {
            case 'superadmin':
                return 'destructive';
            case 'admin':
                return 'default';
            case 'user':
                return 'secondary';
            default:
                return 'outline';
        }
    };

    const canEditUser = (user: User & { role: string }) => {
        // Can't edit yourself
        if (user.id === auth.user.id) return false;

        // Superadmin can edit anyone
        if (auth.user.role === 'superadmin') return true;

        // Admin can only edit users, not other admins or superadmins
        if (auth.user.role === 'admin') {
            return user.role === 'user';
        }

        return false;
    };

    const canDeleteUser = (user: User & { role: string }) => {
        // Can't delete yourself
        if (user.id === auth.user.id) return false;

        // Superadmin can delete anyone except themselves
        if (auth.user.role === 'superadmin') return true;

        // Admin can only delete users, not other admins or superadmins
        if (auth.user.role === 'admin') {
            return user.role === 'user';
        }

        return false;
    };

    const formatDate = (dateString: string) => {
        return new Date(dateString).toLocaleDateString('id-ID', {
            year: 'numeric',
            month: 'long',
            day: 'numeric',
        });
    };

    // Auto-submit form when role filter changes
    useEffect(() => {
        const currentRole = filters.role || 'all';
        if (currentRole !== data.role && data.role !== 'all') {
            const params = {
                search: data.search,
                ...(data.role !== 'all' && { role: data.role }),
            };
            router.get(route('admin.users.index'), params, {
                preserveState: true,
                preserveScroll: true,
            });
        }
    }, [data.role, filters.role, data.search]);

    return (
        <AdminLayout breadcrumbs={breadcrumbs}>
            <Head title="Manajemen Pengguna - Admin Desa Lemah Duhur" />

            <div className="flex h-full flex-1 flex-col gap-6 p-6">
                {/* Header */}
                <div className="flex items-center justify-between">
                    <div>
                        <h1 className="text-3xl font-bold text-gray-900 dark:text-gray-100">Manajemen Pengguna</h1>
                        <p className="text-gray-600 dark:text-gray-300">Kelola akun pengguna dan administrator sistem</p>
                    </div>
                    <Link href={route('admin.users.create')}>
                        <Button className="flex items-center space-x-2">
                            <Plus className="h-4 w-4" />
                            <span>Tambah Pengguna</span>
                        </Button>
                    </Link>
                </div>

                {/* Filters */}
                <Card>
                    <CardHeader>
                        <CardTitle className="flex items-center space-x-2">
                            <Search className="h-5 w-5" />
                            <span>Pencarian & Filter</span>
                        </CardTitle>
                    </CardHeader>
                    <CardContent>
                        <form onSubmit={handleFilter} className="space-y-4">
                            <div className="grid grid-cols-1 gap-4 md:grid-cols-3">
                                <div className="space-y-2">
                                    <Label htmlFor="search">Cari Pengguna</Label>
                                    <Input
                                        id="search"
                                        value={data.search}
                                        onChange={(e) => setData('search', e.target.value)}
                                        placeholder="Nama atau email pengguna..."
                                    />
                                </div>

                                <div className="space-y-2">
                                    <Label htmlFor="role">Filter Peran</Label>
                                    <Select value={data.role} onValueChange={(value) => setData('role', value)}>
                                        <SelectTrigger>
                                            <SelectValue placeholder="Semua Peran" />
                                        </SelectTrigger>
                                        <SelectContent>
                                            <SelectItem value="all">Semua Peran</SelectItem>
                                            {Object.entries(roles).map(([key, label]) => (
                                                <SelectItem key={key} value={key}>
                                                    {label}
                                                </SelectItem>
                                            ))}
                                        </SelectContent>
                                    </Select>
                                </div>

                                <div className="flex items-end space-x-2">
                                    <Button type="submit" disabled={processing} className="flex-1">
                                        <Search className="mr-2 h-4 w-4" />
                                        {processing ? 'Mencari...' : 'Cari'}
                                    </Button>
                                    <Button type="button" variant="outline" onClick={handleReset} disabled={processing}>
                                        Reset
                                    </Button>
                                </div>
                            </div>
                        </form>
                    </CardContent>
                </Card>

                {/* Results */}
                <Card>
                    <CardHeader>
                        <CardTitle className="flex items-center justify-between">
                            <div className="flex items-center space-x-2">
                                <UsersIcon className="h-5 w-5" />
                                <span>Daftar Pengguna</span>
                            </div>
                            <div className="text-sm font-normal text-gray-600 dark:text-gray-300">{users.total} pengguna ditemukan</div>
                        </CardTitle>
                    </CardHeader>
                    <CardContent>
                        {users.data.length > 0 ? (
                            <div className="space-y-4">
                                {/* Table */}
                                <div className="rounded-md border">
                                    <Table>
                                        <TableHeader>
                                            <TableRow>
                                                <TableHead>Nama</TableHead>
                                                <TableHead>Email</TableHead>
                                                <TableHead>Peran</TableHead>
                                                <TableHead>Status</TableHead>
                                                <TableHead>Terdaftar</TableHead>
                                                <TableHead className="w-[120px]">Aksi</TableHead>
                                            </TableRow>
                                        </TableHeader>
                                        <TableBody>
                                            {users.data.map((user) => (
                                                <TableRow key={user.id}>
                                                    <TableCell className="font-medium">{user.name}</TableCell>
                                                    <TableCell>{user.email}</TableCell>
                                                    <TableCell>
                                                        <Badge variant={getRoleBadgeVariant(user.role)}>{roles[user.role] || user.role}</Badge>
                                                    </TableCell>
                                                    <TableCell>
                                                        <Badge variant={user.email_verified_at ? 'default' : 'outline'}>
                                                            {user.email_verified_at ? 'Terverifikasi' : 'Belum Verifikasi'}
                                                        </Badge>
                                                    </TableCell>
                                                    <TableCell className="text-gray-600 dark:text-gray-300">{formatDate(user.created_at)}</TableCell>
                                                    <TableCell>
                                                        <div className="flex items-center space-x-1">
                                                            <Link href={route('admin.users.show', user.id)}>
                                                                <Button variant="ghost" size="sm">
                                                                    <Eye className="h-4 w-4" />
                                                                </Button>
                                                            </Link>
                                                            {canEditUser(user) && (
                                                                <Link href={route('admin.users.edit', user.id)}>
                                                                    <Button variant="ghost" size="sm">
                                                                        <Edit className="h-4 w-4" />
                                                                    </Button>
                                                                </Link>
                                                            )}
                                                            {canDeleteUser(user) && (
                                                                <Button
                                                                    variant="ghost"
                                                                    size="sm"
                                                                    onClick={() => handleDelete(user)}
                                                                    className="text-red-600 hover:text-red-700 dark:text-red-400 dark:hover:text-red-300"
                                                                >
                                                                    <Trash2 className="h-4 w-4" />
                                                                </Button>
                                                            )}
                                                        </div>
                                                    </TableCell>
                                                </TableRow>
                                            ))}
                                        </TableBody>
                                    </Table>
                                </div>

                                {/* Pagination */}
                                {users.last_page > 1 && (
                                    <div className="flex items-center justify-between">
                                        <div className="flex items-center space-x-2 text-sm text-gray-600 dark:text-gray-300">
                                            <span>
                                                Menampilkan {users.from} - {users.to} dari {users.total} pengguna
                                            </span>
                                        </div>
                                        <div className="flex items-center space-x-1">
                                            {users.links.map((link, index) => {
                                                if (link.url === null) {
                                                    return (
                                                        <Button
                                                            key={index}
                                                            variant="outline"
                                                            size="sm"
                                                            disabled
                                                            dangerouslySetInnerHTML={{ __html: link.label }}
                                                        />
                                                    );
                                                }

                                                return (
                                                    <Link key={index} href={link.url} preserveScroll preserveState>
                                                        <Button
                                                            variant={link.active ? 'default' : 'outline'}
                                                            size="sm"
                                                            dangerouslySetInnerHTML={{ __html: link.label }}
                                                        />
                                                    </Link>
                                                );
                                            })}
                                        </div>
                                    </div>
                                )}
                            </div>
                        ) : (
                            <div className="flex flex-col items-center justify-center py-12 text-center">
                                <UsersIcon className="h-12 w-12 text-gray-400 dark:text-gray-600" />
                                <h3 className="mt-4 text-lg font-medium text-gray-900 dark:text-gray-100">Tidak ada pengguna ditemukan</h3>
                                <p className="mt-2 text-gray-600 dark:text-gray-300">
                                    {filters.search || filters.role
                                        ? 'Coba ubah kriteria pencarian atau filter Anda.'
                                        : 'Belum ada pengguna yang terdaftar dalam sistem.'}
                                </p>
                                {(filters.search || filters.role) && (
                                    <Button variant="outline" onClick={handleReset} className="mt-4">
                                        Reset Filter
                                    </Button>
                                )}
                            </div>
                        )}
                    </CardContent>
                </Card>
            </div>
        </AdminLayout>
    );
}
