<?php

namespace Database\Factories;

use App\Models\Potential;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Potential>
 */
class PotentialFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = Potential::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $type = $this->faker->randomElement(['tourism', 'umkm']);

        $tourismNames = [
            'Air Terjun Lemah Duhur',
            'Pemandian Air Panas',
            'Kebun Teh Hijau',
            'Bukit Sunrise',
            'Sungai Jernih',
        ];

        $umkmNames = [
            'Keripik Singkong Bu Sari',
            'Kopi Robusta Lemah Duhur',
            'Kerajinan Bambu Pak Budi',
            'Madu Hutan Alami',
            'Batik Tulis Tradisional',
        ];

        $name = $type === 'tourism'
            ? $this->faker->randomElement($tourismNames)
            : $this->faker->randomElement($umkmNames);

        return [
            'name' => $name,
            'type' => $type,
            'description' => $this->faker->paragraphs(3, true),
            'images' => [],
            'contact_info' => [
                'phone' => $this->faker->phoneNumber(),
                'email' => $this->faker->email(),
                'person' => $this->faker->name(),
                'address' => $this->faker->address(),
            ],
            'location' => $this->faker->address(),
            'is_featured' => false,
        ];
    }

    /**
     * Create a tourism potential.
     */
    public function tourism(): static
    {
        return $this->state(fn (array $attributes) => [
            'type' => 'tourism',
            'name' => $this->faker->randomElement([
                'Air Terjun Lemah Duhur',
                'Pemandian Air Panas',
                'Kebun Teh Hijau',
                'Bukit Sunrise',
                'Sungai Jernih',
            ]),
        ]);
    }

    /**
     * Create a UMKM potential.
     */
    public function umkm(): static
    {
        return $this->state(fn (array $attributes) => [
            'type' => 'umkm',
            'name' => $this->faker->randomElement([
                'Keripik Singkong Bu Sari',
                'Kopi Robusta Lemah Duhur',
                'Kerajinan Bambu Pak Budi',
                'Madu Hutan Alami',
                'Batik Tulis Tradisional',
            ]),
        ]);
    }

    /**
     * Indicate that the potential is featured.
     */
    public function featured(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_featured' => true,
        ]);
    }

    /**
     * Indicate that the potential has images.
     */
    public function withImages(): static
    {
        return $this->state(fn (array $attributes) => [
            'images' => [
                [
                    'original' => [
                        'url' => 'storage/potentials/original/'.$this->faker->uuid().'.jpg',
                        'width' => 1200,
                        'height' => 800,
                    ],
                    'medium' => [
                        'url' => 'storage/potentials/medium/'.$this->faker->uuid().'.jpg',
                        'width' => 800,
                        'height' => 533,
                    ],
                    'thumbnail' => [
                        'url' => 'storage/potentials/thumbnail/'.$this->faker->uuid().'.jpg',
                        'width' => 300,
                        'height' => 200,
                    ],
                ],
                [
                    'original' => [
                        'url' => 'storage/potentials/original/'.$this->faker->uuid().'.jpg',
                        'width' => 1200,
                        'height' => 800,
                    ],
                    'medium' => [
                        'url' => 'storage/potentials/medium/'.$this->faker->uuid().'.jpg',
                        'width' => 800,
                        'height' => 533,
                    ],
                    'thumbnail' => [
                        'url' => 'storage/potentials/thumbnail/'.$this->faker->uuid().'.jpg',
                        'width' => 300,
                        'height' => 200,
                    ],
                ],
            ],
        ]);
    }

    /**
     * Create potential with custom contact info.
     */
    public function withContact(array $contactInfo): static
    {
        return $this->state(fn (array $attributes) => [
            'contact_info' => array_merge($attributes['contact_info'] ?? [], $contactInfo),
        ]);
    }
}
